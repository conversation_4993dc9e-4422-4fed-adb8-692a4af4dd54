import '@testing-library/jest-dom';
import { vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import React from 'react';

// Mock IndexedDB for tests
const mockIDBRequest = {
  result: null,
  error: null,
  onsuccess: null,
  onerror: null,
  readyState: 'done',
};

const mockIDBDatabase = {
  createObjectStore: vi.fn(),
  transaction: vi.fn(() => ({
    objectStore: vi.fn(() => ({
      add: vi.fn(() => mockIDBRequest),
      get: vi.fn(() => mockIDBRequest),
      put: vi.fn(() => mockIDBRequest),
      delete: vi.fn(() => mockIDBRequest),
      getAll: vi.fn(() => mockIDBRequest),
    })),
  })),
  close: vi.fn(),
};

const mockIDBOpenRequest = {
  ...mockIDBRequest,
  onupgradeneeded: null,
  result: mockIDBDatabase,
};

// Setup global mocks
Object.defineProperty(window, 'indexedDB', {
  value: {
    open: vi.fn(() => mockIDBOpenRequest),
    deleteDatabase: vi.fn(() => mockIDBRequest),
  },
  writable: true,
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Global test setup
beforeAll(() => {
  // Setup global test environment
  console.log('Setting up test environment');
});

afterAll(() => {
  // Clean up global test environment
  console.log('Cleaning up test environment');
});

beforeEach(() => {
  // Reset mocks before each test
  vi.clearAllMocks();
});

afterEach(() => {
  // Clean up after each test
  vi.restoreAllMocks();
});

// Add custom matchers
expect.extend({
  toHaveStyleRule(received, property, value) {
    try {
      const styles = window.getComputedStyle(received);
      const pass = styles[property as any] === value;
      if (pass) {
        return {
          message: () => `expected ${received} not to have CSS property "${property}: ${value}"`,
          pass: true,
        };
      } else {
        return {
          message: () =>
            `expected ${received} to have CSS property "${property}: ${value}", instead received "${
              styles[property as any]
            }"`,
          pass: false,
        };
      }
    } catch (err) {
      return {
        message: () =>
          `expected ${received} to have CSS property "${property}: ${value}", but failed with error: ${err}`,
        pass: false,
      };
    }
  },
});
