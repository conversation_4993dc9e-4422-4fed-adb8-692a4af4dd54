{
  // === SECURITY & WORKSPACE ===
  "security.workspace.trust.untrustedFiles": "open",

  // === EDITOR BEHAVIOR ===
  "editor.inlineSuggest.enabled": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.foldingStrategy": "indentation",
  "editor.renderWhitespace": "boundary",
  "editor.showUnused": true,
  "editor.unusedVariables.showWarning": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit",
    "source.fixAll.eslint": "explicit"
  },

  // === GIT CONFIGURATION ===
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,

  // === DEBUG SETTINGS ===
  "debug.allowBreakpointsEverywhere": true,

  // === DIFF EDITOR ===
  "diffEditor.codeLens": true,
  "diffEditor.ignoreTrimWhitespace": false,

  // === GITHUB COPILOT ===
  "github.copilot.chat.codeGeneration.instructions": [],
  "github.copilot.chat.reviewSelection.instructions": [],
  "github.copilot.nextEditSuggestions.enabled": true,
  "github.copilot.enable": {
    "*": false
  },

  // === CHAT & INSTRUCTIONS ===
  "chat.instructionsFilesLocations": ["/Users/<USER>/my-project/"],

  // === TYPESCRIPT CONFIGURATION ===
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.validate.enable": true,
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.useAliasesForRenames": true,
  "typescript.suggestionActions.enabled": true,
  "typescript.reportStyleChecksAsWarnings": true,
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.preferences.quoteStyle": "single",
  "typescript.suggest.completeFunctionCalls": true,
  "typescript.suggest.enabled": true,
  "typescript.suggest.paths": true,
  "typescript.tsc.autoDetect": "on",
  "typescript.preferences.preferTypeOnlyAutoImports": true,
  "typescript.preferences.importModuleSpecifier": "relative",

  // === ENHANCED TYPESCRIPT STRICTNESS ===
  "typescript.workspaceSymbols.scope": "allOpenProjects",

  // === EDITOR PARAMETER HINTS & INLAY HINTS ===
  "editor.parameterHints.enabled": true,
  "editor.inlayHints.enabled": "on",
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,

  // === JAVASCRIPT CONFIGURATION ===
  "javascript.preferences.importModuleSpecifier": "relative",
  "javascript.updateImportsOnFileMove.enabled": "always",

  // === ERROR VISIBILITY ===
  "problems.decorations.enabled": true,

  // === FILE MANAGEMENT ===
  "files.autoSave": "onWindowChange",
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": false,
    "**/dist": false
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/dist/**": true
  },

  // === SEARCH CONFIGURATION ===
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  },

  // === ESLINT INTEGRATION ===
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.run": "onType",

  // === LANGUAGE-SPECIFIC FORMATTERS ===
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // === PRETTIER CONFIGURATION ===
  "prettier.singleQuote": true,
  "prettier.trailingComma": "es5",
  "prettier.printWidth": 100,

  // === TERMINAL ===
  "terminal.integrated.enableMultiLinePasteWarning": "never",

  // === AUGMENT AI CONFIGURATION ===
  "augment.completions.enableAutomaticCompletions": false,
  "augment.chat.userGuidelines": "Augment AI User Guidelines\nTech Stack: Augment AI, VS Code, yarn, TypeScript, ES modules, Vitest\nWork Style: Solo development, execution-focused, essentials-only communication\nPain Points: TypeScript errors, duct tape fixes, context-providing struggles\nTYPESCRIPT STANDARDS\n\nAlways use strict TypeScript mode\nImport types with \"import type\" for type-only imports\nExport types with \"export type\" for type definitions\nNever reference components without proper imports\nVerify all component references exist before using\nRun TypeScript check before completing any code changes\n\nCENTRALIZED THEME SYSTEM\nRequired Import Pattern:\ntypescriptimport { theme } from '@/styles/theme' // adjust path as needed\nMandatory Usage:\n\nColors: theme.colors.primary NOT hardcoded #colors\nSpacing: theme.spacing.md NOT hardcoded pixel values\nTypography: theme.fonts.body NOT hardcoded font families\nBreakpoints: theme.breakpoints.md NOT hardcoded media queries\n\nNEVER ALLOW:\n\nDirect color values (#ffffff, rgb(), etc.)\nHardcoded spacing (margin: 10px)\nInline styles with theme-related properties\nComponent-specific theme overrides without proper theming\nStyled-components without proper theme integration\n\nIMPORT/EXPORT RULES\n\nAlways verify imports exist and are correctly typed\nUse consistent import patterns across codebase\nGroup imports: types first, then components, then utilities\nRemove unused imports automatically\nExport components with proper TypeScript interfaces\n\nCODE QUALITY GATES\n\nEvery component must have TypeScript interface for props\nNo 'any' types allowed - use proper typing\nVerify builds pass before marking work complete\nInclude error boundaries for new components\nAdd JSDoc comments for complex logic\n\nTESTING REQUIREMENTS\n\nWrite Vitest tests for new components\nMock external dependencies properly\nTest TypeScript interfaces in test files\nVerify tests pass before completing work\n\nPRE-COMPLETION CHECKLIST\n\n✅ TypeScript compilation passes\n✅ Theme imports follow centralized pattern\n✅ All component references properly imported\n✅ No hardcoded styles or colors\n✅ Tests pass\n✅ No 'any' types used"
}
