{"name": "@adhd-trading-dashboard/dashboard", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@adhd-trading-dashboard/shared": "1.0.0", "@anthropic-ai/sdk": "0.52.0", "@babel/parser": "7.27.2", "@babel/traverse": "7.27.1", "canvas": "3.1.0", "express": "4.21.2", "idb": "8.0.3", "react-router-dom": "6.6.2"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0", "recharts": "2.10.3", "styled-components": "5.3.6"}, "scripts": {"prestart": "node scripts/manage-assets.js && node scripts/build-themes.js", "start": "vite", "predev": "node scripts/manage-assets.js && node scripts/build-themes.js", "dev": "vite", "prebuild": "node scripts/manage-assets.js && node scripts/build-themes.js", "build": "tsc && vite build", "build:themes": "node scripts/build-themes.js", "build:dev": "tsc && vite build --mode development", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "dev-tools": "node dev-tools/index.js", "f1-dev": "node dev-tools/index.js", "dev:interactive": "node dev-tools/index.js interactive", "dev:analyze": "node dev-tools/index.js analyze", "dev:generate": "node dev-tools/index.js generate", "dev:validate": "node dev-tools/index.js validate", "dev:health": "node dev-tools/index.js health", "dev:refactor": "node dev-tools/index.js refactor", "metrics": "node code-health/architecture-metrics.js", "schema-check": "node code-health/schema-validator.js"}, "eslintConfig": {"extends": ["react-app", "plugin:storybook/recommended"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/parser": "7.27.2", "@babel/traverse": "7.27.1", "@storybook/addon-essentials": "7.6.7", "@storybook/addon-interactions": "7.6.7", "@storybook/addon-links": "7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/addon-a11y": "7.6.7", "@storybook/blocks": "^7.6.7", "@storybook/react": "7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@types/babel__traverse": "7.20.0", "@types/figlet": "1.5.0", "@types/inquirer": "9.0.0", "chalk": "5.4.1", "commander": "14.0.0", "figlet": "1.8.1", "glob": "11.0.2", "inquirer": "9.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1", "recharts": "2.10.3", "storybook": "7.6.7", "styled-components": "5.3.6", "typescript": "^5.8.0", "vite-plugin-pwa": "1.0.0", "web-vitals": "2.1.4"}}