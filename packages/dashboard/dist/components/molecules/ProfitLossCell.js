import { jsx as _jsx } from "react/jsx-runtime";
import styled, { css } from 'styled-components';
const StyledCell = styled.span `
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: ${({ theme }) => theme.fontWeights?.semibold || '600'};
  font-family: ${({ theme }) => theme.fontFamilies?.mono || 'monospace'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  ${({ $size, theme }) => {
    switch ($size) {
        case 'small':
            return css `
          font-size: ${theme.fontSizes?.xs || '12px'};
          padding: ${theme.spacing?.xxs || '2px'} ${theme.spacing?.xs || '4px'};
        `;
        case 'large':
            return css `
          font-size: ${theme.fontSizes?.lg || '18px'};
          padding: ${theme.spacing?.sm || '8px'} ${theme.spacing?.md || '12px'};
        `;
        default:
            return css `
          font-size: ${theme.fontSizes?.sm || '14px'};
          padding: ${theme.spacing?.xs || '4px'} ${theme.spacing?.sm || '8px'};
        `;
    }
}}

  ${({ $isProfit, $isLoss, $isNeutral, theme }) => {
    if ($isProfit) {
        return css `
        color: ${theme.colors?.profit || theme.colors?.success || 'var(--success-color)'};
        background-color: ${theme.colors?.profit
            ? `${theme.colors.profit}15`
            : 'rgba(76, 175, 80, 0.1)'};
        border: 1px solid
          ${theme.colors?.profit ? `${theme.colors.profit}30` : 'rgba(76, 175, 80, 0.2)'};
      `;
    }
    if ($isLoss) {
        return css `
        color: ${theme.colors?.loss || theme.colors?.error || 'var(--error-color)'};
        background-color: ${theme.colors?.loss
            ? `${theme.colors.loss}15`
            : 'rgba(244, 67, 54, 0.1)'};
        border: 1px solid
          ${theme.colors?.loss ? `${theme.colors.loss}30` : 'rgba(244, 67, 54, 0.2)'};
      `;
    }
    if ($isNeutral) {
        return css `
        color: ${theme.colors?.neutral || theme.colors?.textSecondary || '#757575'};
        background-color: ${theme.colors?.neutral
            ? `${theme.colors.neutral}15`
            : 'rgba(117, 117, 117, 0.1)'};
        border: 1px solid
          ${theme.colors?.neutral ? `${theme.colors.neutral}30` : 'rgba(117, 117, 117, 0.2)'};
      `;
    }
    return css `
      color: ${theme.colors?.textPrimary || '#ffffff'};
      background-color: transparent;
      border: 1px solid transparent;
    `;
}}

  ${({ $isLoading }) => $isLoading &&
    css `
      opacity: 0.6;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        animation: shimmer 1.5s infinite;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }
    `}

  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows?.sm || '0 2px 4px rgba(0, 0, 0, 0.1)'};
  }
`;
const LoadingPlaceholder = styled.span `
  display: inline-block;
  width: 60px;
  height: 1em;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 2px;
`;
/**
 * Formats a number as currency
 */
const formatCurrency = (amount, currency = '$', showPositiveSign = false) => {
    const absAmount = Math.abs(amount);
    const formattedAmount = absAmount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
    if (amount > 0) {
        return showPositiveSign ? `+${currency}${formattedAmount}` : `${currency}${formattedAmount}`;
    }
    else if (amount < 0) {
        return `-${currency}${formattedAmount}`;
    }
    else {
        return `${currency}${formattedAmount}`;
    }
};
/**
 * ProfitLossCell Component
 *
 * Displays profit/loss amounts with appropriate color coding and formatting.
 * Designed for ADHD-friendly quick visual recognition in trading interfaces.
 */
export const ProfitLossCell = ({ amount, currency = '$', size = 'medium', showPositiveSign = false, className, isLoading = false, 'aria-label': ariaLabel, }) => {
    // Handle loading state
    if (isLoading || amount === null || amount === undefined) {
        return (_jsx(StyledCell, { className: className, "$isProfit": false, "$isLoss": false, "$isNeutral": false, "$size": size, "$isLoading": true, "aria-label": ariaLabel || 'Loading profit/loss amount', children: _jsx(LoadingPlaceholder, {}) }));
    }
    const isProfit = amount > 0;
    const isLoss = amount < 0;
    const isNeutral = amount === 0;
    const formattedAmount = formatCurrency(amount, currency, showPositiveSign);
    const defaultAriaLabel = `${isProfit ? 'Profit' : isLoss ? 'Loss' : 'Breakeven'} of ${formattedAmount}`;
    return (_jsx(StyledCell, { className: className, "$isProfit": isProfit, "$isLoss": isLoss, "$isNeutral": isNeutral, "$size": size, "$isLoading": false, "aria-label": ariaLabel || defaultAriaLabel, role: "cell", tabIndex: 0, children: formattedAmount }));
};
export default ProfitLossCell;
//# sourceMappingURL=ProfitLossCell.js.map