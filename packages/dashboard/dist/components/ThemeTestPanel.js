import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { useTheme } from '@adhd-trading-dashboard/shared';
const TestPanel = styled.div `
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 12px;
  padding: 16px;
  min-width: 300px;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
`;
const ThemeButton = styled.button `
  background: ${({ isActive }) => isActive ? 'var(--primary-color)' : 'var(--session-card-bg)'};
  color: ${({ isActive }) => isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;

  &:hover {
    background: var(--primary-color);
    color: var(--session-text-primary);
  }
`;
const SampleCard = styled.div `
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  box-shadow: var(--shadow-sm);
`;
const SampleText = styled.div `
  color: var(--session-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
`;
const SampleSecondary = styled.div `
  color: var(--session-text-secondary);
  font-size: 12px;
`;
const StatusBadge = styled.div `
  display: inline-block;
  background: ${({ status }) => {
    switch (status) {
        case 'success': return 'var(--success-color)';
        case 'warning': return 'var(--warning-color)';
        case 'error': return 'var(--error-color)';
        case 'info': return 'var(--info-color)';
        default: return 'var(--session-card-border)';
    }
}};
  color: var(--session-text-primary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  margin: 2px;
`;
const CloseButton = styled.button `
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: var(--session-text-secondary);
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  
  &:hover {
    color: var(--session-text-primary);
  }
`;
export const ThemeTestPanel = ({ onClose }) => {
    const { theme, setTheme } = useTheme();
    const themes = [
        { id: 'mercedes-green', name: 'Mercedes Green' },
        { id: 'f1-official', name: 'F1 Official' },
        { id: 'dark', name: 'Dark' },
    ];
    return (_jsxs(TestPanel, { children: [_jsx(CloseButton, { onClick: onClose, children: "\u00D7" }), _jsx("h3", { style: { color: 'var(--session-text-primary)', margin: '0 0 12px 0' }, children: "Theme Test Panel" }), _jsxs("div", { style: { marginBottom: '16px' }, children: [_jsxs("div", { style: { color: 'var(--session-text-secondary)', fontSize: '12px', marginBottom: '8px' }, children: ["Current: ", theme.name] }), themes.map((t) => (_jsx(ThemeButton, { isActive: theme.name === t.id, onClick: () => setTheme(t.id), children: t.name }, t.id)))] }), _jsxs(SampleCard, { children: [_jsx(SampleText, { children: "Sample Session Card" }), _jsx(SampleSecondary, { children: "This shows the theme colors in action" }), _jsxs("div", { style: { marginTop: '8px' }, children: [_jsx(StatusBadge, { status: "success", children: "SUCCESS" }), _jsx(StatusBadge, { status: "warning", children: "WARNING" }), _jsx(StatusBadge, { status: "error", children: "ERROR" }), _jsx(StatusBadge, { status: "info", children: "INFO" })] })] }), _jsxs("div", { style: {
                    color: 'var(--session-text-secondary)',
                    fontSize: '10px',
                    marginTop: '12px',
                    lineHeight: '1.4'
                }, children: ["Test theme switching to verify:", _jsx("br", {}), "\u2022 Clean backgrounds (no muddy layering)", _jsx("br", {}), "\u2022 Proper accent colors", _jsx("br", {}), "\u2022 High contrast text", _jsx("br", {}), "\u2022 Consistent borders"] })] }));
};
export default ThemeTestPanel;
//# sourceMappingURL=ThemeTestPanel.js.map