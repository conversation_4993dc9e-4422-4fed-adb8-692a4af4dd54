{"version": 3, "file": "useSettingsForm.js", "sourceRoot": "", "sources": ["../../../../src/features/settings/hooks/useSettingsForm.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAClE,OAAO,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAuC1D,MAAM,WAAW,GAAG,iCAAiC,CAAC;AAEtD;;GAEG;AACH,MAAM,gBAAgB,GAAqB;IACzC,KAAK,EAAE,gBAAgB;IACvB,eAAe,EAAE,CAAC;IAClB,iBAAiB,EAAE,IAAI;IACvB,qBAAqB,EAAE,KAAK;IAC5B,eAAe,EAAE,IAAI;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,KAAa,EAAU,EAAE;IAC7C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,UAAU,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;QACpE,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IACD,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,GAAqB,EAAE;IAC1C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClC,0BAA0B;YAC1B,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO,EAAE,GAAG,gBAAgB,EAAE,GAAG,MAAM,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,QAA0B,EAAQ,EAAE;IACxD,IAAI,CAAC;QACH,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,KAAU,EAAiB,EAAE;IAChE,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,OAAO;YACV,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACxC,OAAO,mBAAmB,CAAC;YAC7B,CAAC;YACD,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/D,OAAO,yBAAyB,CAAC;YACnC,CAAC;YACD,OAAO,IAAI,CAAC;QAEd,KAAK,iBAAiB;YACpB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO,mCAAmC,CAAC;YAC7C,CAAC;YACD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBAC5B,OAAO,mDAAmD,CAAC;YAC7D,CAAC;YACD,OAAO,IAAI,CAAC;QAEd,KAAK,mBAAmB,CAAC;QACzB,KAAK,uBAAuB,CAAC;QAC7B,KAAK,iBAAiB;YACpB,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,OAAO,uBAAuB,CAAC;YACjC,CAAC;YACD,OAAO,IAAI,CAAC;QAEd;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,QAA0B,EAAoB,EAAE;IACpE,MAAM,MAAM,GAAqB,EAAE,CAAC;IAEpC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;QACjD,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,GAA0B,EAAE;IACzD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;IAEhC,wBAAwB;IACxB,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAmB,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC;IACnF,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAmB,SAAS,CAAC,CAAC;IAC9D,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEhD,4BAA4B;IAC5B,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,EAAE;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAEtB,yBAAyB;IACzB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE;QAC3B,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IAC1C,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAC9B,CAAC,IAAY,EAAE,KAAU,EAAE,EAAE;QAC3B,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAEhD,mCAAmC;QACnC,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9C,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE;YACjB,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAC9B,IAAI,UAAU,EAAE,CAAC;gBACf,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,6CAA6C;QAC7C,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YACrB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF;;OAEG;IACH,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACxC,uBAAuB;QACvB,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,SAAS,CAAC,UAAU,CAAC,CAAC;QAEtB,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,CAAC;QAElB,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEzD,uBAAuB;YACvB,YAAY,CAAC,IAAI,CAAC,CAAC;YAEnB,0BAA0B;YAC1B,YAAY,CAAC,IAAI,CAAC,CAAC;YAEnB,qBAAqB;YACrB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAErB,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,WAAW,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IAErB;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;QACnC,OAAO,CAAC,SAAS,CAAC,CAAC;QACnB,SAAS,CAAC,EAAE,CAAC,CAAC;QAEd,6BAA6B;QAC7B,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1B,gCAAgC;IAChC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,SAAS,CAAC,UAAU,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,OAAO;QACL,IAAI;QACJ,SAAS;QACT,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,WAAW;QACX,aAAa;QACb,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,eAAe,CAAC"}