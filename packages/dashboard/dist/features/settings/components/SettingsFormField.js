import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const FieldContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.md || '12px'} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  
  &:last-child {
    border-bottom: none;
  }
`;
const FieldRow = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;
const LabelSection = styled.div `
  flex: 1;
  min-width: 0;
`;
const FieldLabel = styled.label `
  display: block;
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  cursor: pointer;
`;
const FieldDescription = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  line-height: 1.4;
`;
const ControlSection = styled.div `
  flex-shrink: 0;
  min-width: 120px;
  
  @media (max-width: 768px) {
    min-width: 0;
  }
`;
const Input = styled.input `
  width: 100%;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasError }) => $hasError
    ? theme.colors?.error || 'var(--error-color)'
    : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
const Select = styled.select `
  width: 100%;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasError }) => $hasError
    ? theme.colors?.error || 'var(--error-color)'
    : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
const ToggleContainer = styled.label `
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  cursor: pointer;
`;
const ToggleInput = styled.input `
  opacity: 0;
  width: 0;
  height: 0;
  
  &:checked + span {
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
  
  &:checked + span:before {
    transform: translateX(24px);
  }
  
  &:focus + span {
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }
`;
const ToggleSlider = styled.span `
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  transition: all 0.3s ease;
  border-radius: 28px;
  
  &:before {
    position: absolute;
    content: '';
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;
const ErrorMessage = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
`;
/**
 * SettingsFormField Component
 *
 * PATTERN: F1 Form Field Pattern
 * - Racing-inspired styling with red accents
 * - Consistent form controls across field types
 * - Built-in validation and error states
 * - Accessible with proper labels and focus
 * - Responsive design for mobile
 */
export const SettingsFormField = ({ name, label, description, type, value, onChange, options = [], inputProps = {}, error, disabled = false, className, }) => {
    const fieldId = `settings-field-${name}`;
    const handleChange = (newValue) => {
        if (!disabled) {
            onChange(name, newValue);
        }
    };
    const renderControl = () => {
        switch (type) {
            case 'text':
            case 'number':
                return (_jsx(Input, { id: fieldId, type: type, value: value || '', onChange: (e) => handleChange(type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value), disabled: disabled, "$hasError": !!error, ...inputProps }));
            case 'select':
                return (_jsx(Select, { id: fieldId, value: value || '', onChange: (e) => handleChange(e.target.value), disabled: disabled, "$hasError": !!error, children: options.map((option) => (_jsx("option", { value: option.value, children: option.label }, option.value))) }));
            case 'toggle':
                return (_jsxs(ToggleContainer, { children: [_jsx(ToggleInput, { id: fieldId, type: "checkbox", checked: !!value, onChange: (e) => handleChange(e.target.checked), disabled: disabled }), _jsx(ToggleSlider, {})] }));
            default:
                return null;
        }
    };
    return (_jsxs(FieldContainer, { className: className, children: [_jsxs(FieldRow, { children: [_jsxs(LabelSection, { children: [_jsx(FieldLabel, { htmlFor: fieldId, children: label }), description && (_jsx(FieldDescription, { children: description }))] }), _jsx(ControlSection, { children: renderControl() })] }), error && (_jsx(ErrorMessage, { role: "alert", children: error }))] }));
};
export default SettingsFormField;
//# sourceMappingURL=SettingsFormField.js.map