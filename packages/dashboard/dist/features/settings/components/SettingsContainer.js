import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * SettingsContainer Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * Main orchestrator for the settings page with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import { Suspense, useState } from 'react';
import styled from 'styled-components';
import { SettingsHeader } from './SettingsHeader';
import { SettingsForm } from './SettingsForm';
import { useSettingsForm } from '../hooks/useSettingsForm';
const Container = styled.div `
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;
const ContentWrapper = styled.div `
  flex: 1;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.lg || '24px'} ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;
const LoadingState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  min-height: 200px;
`;
const LoadingIcon = styled.div `
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
  }
`;
const LoadingText = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
`;
const ErrorState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  min-height: 200px;
  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;
const ErrorIcon = styled.div `
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
`;
const ErrorTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;
const ErrorMessage = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 400px;
`;
const RetryButton = styled.button `
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;
const SuccessNotification = styled.div `
  position: fixed;
  top: 20px;
  right: 20px;
  background: ${({ theme }) => theme.colors?.success || 'var(--success-color)'};
  color: white;
  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateX(${({ $visible }) => $visible ? '0' : '100%'});
  opacity: ${({ $visible }) => $visible ? '1' : '0'};
  transition: all 0.3s ease;
  z-index: 1000;
`;
/**
 * LoadingFallback Component
 */
const LoadingFallback = () => (_jsxs(LoadingState, { children: [_jsx(LoadingIcon, { children: "\u2699\uFE0F" }), _jsx(LoadingText, { children: "Loading Settings..." })] }));
/**
 * ErrorFallback Component
 */
const ErrorFallback = ({ error, onRetry }) => (_jsxs(ErrorState, { children: [_jsx(ErrorIcon, { children: "\u26A0\uFE0F" }), _jsx(ErrorTitle, { children: "Settings Error" }), _jsx(ErrorMessage, { children: error }), _jsx(RetryButton, { onClick: onRetry, children: "Try Again" })] }));
/**
 * SettingsContent Component
 */
const SettingsContent = () => {
    const { data, hasUnsavedChanges, errors, isSaving, handleChange, handleSave, handleReset, } = useSettingsForm();
    const [saveError, setSaveError] = useState(null);
    const [showSuccessNotification, setShowSuccessNotification] = useState(false);
    const handleSaveWithNotification = async () => {
        try {
            setSaveError(null);
            await handleSave();
            // Show success notification
            setShowSuccessNotification(true);
            setTimeout(() => setShowSuccessNotification(false), 3000);
        }
        catch (error) {
            setSaveError(error instanceof Error ? error.message : 'Failed to save settings');
        }
    };
    const handleRetry = () => {
        setSaveError(null);
    };
    if (saveError) {
        return _jsx(ErrorFallback, { error: saveError, onRetry: handleRetry });
    }
    return (_jsxs(_Fragment, { children: [_jsx(SettingsHeader, { hasUnsavedChanges: hasUnsavedChanges, onSave: handleSaveWithNotification, onReset: handleReset }), _jsx(SettingsForm, { data: data, onChange: handleChange, errors: errors, disabled: isSaving }), _jsx(SuccessNotification, { "$visible": showSuccessNotification, children: "\u2705 Settings saved successfully!" })] }));
};
/**
 * SettingsContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const SettingsContainer = ({ className, }) => {
    return (_jsx(Container, { className: className, children: _jsx(ContentWrapper, { children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(SettingsContent, {}) }) }) }));
};
export default SettingsContainer;
//# sourceMappingURL=SettingsContainer.js.map