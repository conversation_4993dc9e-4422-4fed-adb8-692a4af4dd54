{"version": 3, "file": "SettingsHeader.js", "sourceRoot": "", "sources": ["../../../../src/features/settings/components/SettingsHeader.tsx"], "names": [], "mappings": ";AAeA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAavC,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAA;;;;aAIrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;6BAC1B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;mBACxE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;;;;;;;;;;;;;QAarD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;QAC9D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;;;CAKrE,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;CACjD,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAA;eACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,IAAI,MAAM;;WAEjD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;;;;;;;CAO/D,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAA;eACV,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,UAAU;WACpD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,IAAI,uBAAuB;;;CAG/E,CAAC;AAEF,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;CAClD,CAAC;AAEF,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAA0B;;;SAGnD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;aACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;mBAChF,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,IAAI,QAAQ;eACvD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,SAAS;;;;;IAK1D,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,CAC3B,WAAW;IACT,CAAC,CAAC;sBACc,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;iBACpD,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;4BACpC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;OACpE;IACD,CAAC,CAAC;sBACc,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;iBACpD,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;4BACpC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;OACpE;CACN,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAA0B;;;;gBAItC,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,CACvC,WAAW;IACT,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;IACjD,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;eACxC,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM;;;;;;CAM7E,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAuC;aAC5D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;mBACjF,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,IAAI,KAAK;eAClD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,UAAU;;;;;;;;;IAS3D,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,CACxB,QAAQ,KAAK,SAAS;IACpB,CAAC,CAAC;sBACc,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;wBAE7C,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;wBAG/C,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,qBAAqB;0BAChD,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,qBAAqB;;kCAE1C,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;OAE1E;IACD,CAAC,CAAC;;iBAES,KAAK,CAAC,MAAM,EAAE,aAAa,IAAI,uBAAuB;wBAC/C,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,uBAAuB;;;mBAGpD,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;0BAC/B,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;wBACxC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,qBAAqB;;OAE/D;;;;;;;;;;CAUN,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,cAAc,GAAkC,CAAC,EAC5D,SAAS,EACT,iBAAiB,GAAG,KAAK,EACzB,MAAM,EACN,OAAO,GACR,EAAE,EAAE;IACH,OAAO,CACL,MAAC,eAAe,IAAC,SAAS,EAAE,SAAS,aACnC,MAAC,YAAY,eACX,KAAC,KAAK,2BAAiB,EACvB,KAAC,QAAQ,mFAEE,IACE,EAEf,MAAC,cAAc,eACb,MAAC,eAAe,mBAAc,iBAAiB,aAC7C,KAAC,SAAS,mBAAc,iBAAiB,GAAI,EAC5C,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,IACpC,EAEjB,iBAAiB,IAAI,CACpB,8BACE,KAAC,YAAY,gBACF,WAAW,EACpB,OAAO,EAAE,OAAO,EAChB,KAAK,EAAC,2BAA2B,sBAGpB,EAEf,KAAC,YAAY,gBACF,SAAS,EAClB,OAAO,EAAE,MAAM,EACf,KAAK,EAAC,uBAAuB,qBAGhB,IACd,CACJ,IACc,IACD,CACnB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,cAAc,CAAC"}