import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { SettingsFormField } from './SettingsFormField';
const FormContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
`;
const FormSection = styled.div `
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;
const SectionHeader = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  }
`;
const SectionTitle = styled.h2 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.xs || '4px'} 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const SectionDescription = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  line-height: 1.5;
`;
const SectionContent = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const FieldGroup = styled.div `
  display: flex;
  flex-direction: column;
`;
/**
 * Form field configurations
 */
const THEME_OPTIONS = [
    { value: 'mercedes-green', label: 'Mercedes Green' },
    { value: 'f1-official', label: 'F1 Official' },
    { value: 'dark', label: 'Dark Theme' },
];
/**
 * SettingsForm Component
 *
 * PATTERN: F1 Form Pattern
 * - Racing-inspired section styling
 * - Organized field groups with clear hierarchy
 * - Consistent form field components
 * - Built-in validation and error handling
 * - Responsive design for all screen sizes
 */
export const SettingsForm = ({ data, onChange, errors = {}, disabled = false, className, }) => {
    return (_jsxs(FormContainer, { className: className, children: [_jsxs(FormSection, { children: [_jsxs(SectionHeader, { children: [_jsx(SectionTitle, { children: "Appearance" }), _jsx(SectionDescription, { children: "Customize the visual appearance and theme of your trading dashboard" })] }), _jsx(SectionContent, { children: _jsx(FieldGroup, { children: _jsx(SettingsFormField, { name: "theme", label: "Theme", description: "Choose your preferred visual theme", type: "select", value: data.theme, onChange: onChange, options: THEME_OPTIONS, error: errors.theme, disabled: disabled }) }) })] }), _jsxs(FormSection, { children: [_jsxs(SectionHeader, { children: [_jsx(SectionTitle, { children: "General Settings" }), _jsx(SectionDescription, { children: "Configure general application behavior and performance settings" })] }), _jsx(SectionContent, { children: _jsxs(FieldGroup, { children: [_jsx(SettingsFormField, { name: "refreshInterval", label: "Data Refresh Interval", description: "How often to refresh dashboard data (in minutes)", type: "number", value: data.refreshInterval, onChange: onChange, inputProps: {
                                        min: 1,
                                        max: 60,
                                        step: 1,
                                        style: { width: '100px' },
                                    }, error: errors.refreshInterval, disabled: disabled }), _jsx(SettingsFormField, { name: "showNotifications", label: "Desktop Notifications", description: "Enable desktop notifications for important events and alerts", type: "toggle", value: data.showNotifications, onChange: onChange, error: errors.showNotifications, disabled: disabled }), _jsx(SettingsFormField, { name: "enableAdvancedMetrics", label: "Advanced Metrics", description: "Show additional performance metrics and detailed analytics", type: "toggle", value: data.enableAdvancedMetrics, onChange: onChange, error: errors.enableAdvancedMetrics, disabled: disabled }), _jsx(SettingsFormField, { name: "autoSaveJournal", label: "Auto-Save Trade Journal", description: "Automatically save trade entries as you type to prevent data loss", type: "toggle", value: data.autoSaveJournal, onChange: onChange, error: errors.autoSaveJournal, disabled: disabled })] }) })] })] }));
};
export default SettingsForm;
//# sourceMappingURL=SettingsForm.js.map