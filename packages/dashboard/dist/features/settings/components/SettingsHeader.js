import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
const HeaderContainer = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
  border-bottom: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};
  position: relative;
  
  /* F1 Racing accent line */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80 50%,
      transparent 100%
    );
    border-radius: 2px;
  }
`;
const TitleSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const Title = styled.h1 `
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;
  
  /* F1 Racing style */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;
const Subtitle = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  font-weight: 400;
`;
const ActionsSection = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const StatusIndicator = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  
  ${({ $hasChanges, theme }) => $hasChanges
    ? `
        background: ${theme.colors?.warning || 'var(--warning-color)'}20;
        color: ${theme.colors?.warning || 'var(--warning-color)'};
        border: 1px solid ${theme.colors?.warning || 'var(--warning-color)'}40;
      `
    : `
        background: ${theme.colors?.success || 'var(--success-color)'}20;
        color: ${theme.colors?.success || 'var(--success-color)'};
        border: 1px solid ${theme.colors?.success || 'var(--success-color)'}40;
      `}
`;
const StatusDot = styled.div `
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${({ $hasChanges, theme }) => $hasChanges
    ? theme.colors?.warning || 'var(--warning-color)'
    : theme.colors?.success || 'var(--success-color)'};
  animation: ${({ $hasChanges }) => $hasChanges ? 'pulse 2s infinite' : 'none'};
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;
const ActionButton = styled.button `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 80px;
  
  ${({ $variant, theme }) => $variant === 'primary'
    ? `
        background: ${theme.colors?.primary || 'var(--primary-color)'};
        color: white;
        border-color: ${theme.colors?.primary || 'var(--primary-color)'};
        
        &:hover:not(:disabled) {
          background: ${theme.colors?.primaryDark || 'var(--primary-dark)'};
          border-color: ${theme.colors?.primaryDark || 'var(--primary-dark)'};
          transform: translateY(-1px);
          box-shadow: 0 4px 8px ${theme.colors?.primary || 'var(--primary-color)'}40;
        }
      `
    : `
        background: transparent;
        color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        border-color: ${theme.colors?.border || 'var(--border-primary)'};
        
        &:hover:not(:disabled) {
          color: ${theme.colors?.textPrimary || '#ffffff'};
          border-color: ${theme.colors?.textPrimary || '#ffffff'};
          background: ${theme.colors?.surface || 'var(--bg-secondary)'};
        }
      `}
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
/**
 * SettingsHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with red accents
 * - Status indicators with animations
 * - Action buttons with hover effects
 * - Consistent with F1 design system
 * - Accessible and responsive
 */
export const SettingsHeader = ({ className, hasUnsavedChanges = false, onSave, onReset, }) => {
    return (_jsxs(HeaderContainer, { className: className, children: [_jsxs(TitleSection, { children: [_jsx(Title, { children: "Settings" }), _jsx(Subtitle, { children: "Configure your trading dashboard preferences and system settings" })] }), _jsxs(ActionsSection, { children: [_jsxs(StatusIndicator, { "$hasChanges": hasUnsavedChanges, children: [_jsx(StatusDot, { "$hasChanges": hasUnsavedChanges }), hasUnsavedChanges ? 'Unsaved Changes' : 'All Saved'] }), hasUnsavedChanges && (_jsxs(_Fragment, { children: [_jsx(ActionButton, { "$variant": "secondary", onClick: onReset, title: "Reset to last saved state", children: "Reset" }), _jsx(ActionButton, { "$variant": "primary", onClick: onSave, title: "Save current settings", children: "Save" })] }))] })] }));
};
export default SettingsHeader;
//# sourceMappingURL=SettingsHeader.js.map