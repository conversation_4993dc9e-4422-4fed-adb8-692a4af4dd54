{"version": 3, "file": "tradeAnalysisCalculations.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/services/tradeAnalysisCalculations.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAWH;;GAEG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAAG,CAC7C,MAA2B,EACP,EAAE;IACtB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,8CAA8C;IAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAE9C,6BAA6B;IAC7B,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjF,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChF,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAErF,2BAA2B;IAC3B,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/F,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/F,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAC9B,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CACvE,CAAC;IAEF,uBAAuB;IACvB,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAExF,iBAAiB;IACjB,MAAM,UAAU,GACd,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,MAAM,WAAW,GACf,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/F,mDAAmD;IACnD,MAAM,SAAS,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACxD,MAAM,eAAe,GACnB,SAAS,CAAC,MAAM,GAAG,CAAC;QAClB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;QAC3E,CAAC,CAAC,CAAC,CAAC;IAER,yBAAyB;IACzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,MAAM,YAAY,GAChB,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7F,MAAM,UAAU,GAAG,OAAO,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,WAAW,CAAC;IAEtE,kFAAkF;IAClF,2BAA2B;IAC3B,4BAA4B;IAC5B,oGAAoG;IACpG,WAAW;IAEX,+EAA+E;IAC/E,yEAAyE;IACzE,0DAA0D;IAC1D,iFAAiF;IAEjF,OAAO;QACL,WAAW,EAAE,MAAM,CAAC,MAAM;QAC1B,aAAa,EAAE,aAAa,CAAC,MAAM;QACnC,YAAY,EAAE,YAAY,CAAC,MAAM;QACjC,SAAS,EAAE,eAAe,CAAC,MAAM;QACjC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,6BAA6B;QACzE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;QAChD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;QAClD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;QACxD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;QAChD,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;QACxD,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9C,wHAAwH;QACxH,8GAA8G;QAC9G,4HAA4H;QAC5H,8GAA8G;QAC9G,8GAA8G;KAC/G,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,CAC1C,MAA2B,EAC3B,QAAqE,EAC9C,EAAE;IACzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEnC,2BAA2B;IAC3B,MAAM,UAAU,GAAG,IAAI,GAAG,EAA+B,CAAC;IAE1D,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACzB,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,IAAI,aAAqB,CAAC;QAE1B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,aAAa,GAAG,KAAK,CAAC,MAAM,IAAI,SAAS,CAAC;gBAC1C,MAAM;YACR,KAAK,YAAY;gBACf,aAAa,GAAG,KAAK,CAAC,UAAU,IAAI,SAAS,CAAC;gBAC9C,MAAM;YACR,KAAK,SAAS;gBACZ,aAAa,GAAG,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC;gBAC3C,MAAM;YACR,KAAK,OAAO;gBACV,aAAa,GAAG,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC;gBACzC,MAAM;YACR,KAAK,WAAW;gBACd,aAAa,GAAG,KAAK,CAAC,SAAS,IAAI,SAAS,CAAC;gBAC7C,MAAM;YACR;gBACE,aAAa,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YACnC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;QACD,UAAU,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,WAAW,GAA0B,EAAE,CAAC;IAE9C,UAAU,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,aAAa,EAAE,EAAE;QACnD,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjF,MAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/F,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,MAAM,iBAAiB,GACrB,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1E,WAAW,CAAC,IAAI,CAAC;YACf,QAAQ;YACR,KAAK,EAAE,aAAa;YACpB,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG;YAC1C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;YACnD,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,GAAG;SAC7D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;AACjE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACtC,MAA2B,EAC3B,QAA+C,EAC5B,EAAE;IACrB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEnC,IAAI,SAAmB,CAAC;IAExB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,WAAW;YACd,SAAS,GAAG;gBACV,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,aAAa;aACd,CAAC;YACF,MAAM;QACR,KAAK,WAAW;YACd,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM;QACR,KAAK,SAAS;YACZ,SAAS,GAAG;gBACV,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,UAAU;aACX,CAAC;YACF,MAAM;QACR;YACE,OAAO,EAAE,CAAC;IACd,CAAC;IAED,mCAAmC;IACnC,MAAM,eAAe,GAAsB,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACpE,QAAQ;QACR,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,UAAU,EAAE,CAAC;KACd,CAAC,CAAC,CAAC;IAEJ,qBAAqB;IACrB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACzB,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,SAAS,GAAW,CAAC,CAAC,CAAC;QAE3B,IAAI,QAAQ,KAAK,WAAW,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACjD,SAAS,GAAG,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;gBACrC,mBAAmB;gBACnB,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;YACzD,MAAM,IAAI,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACtC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBAC3C,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;gBAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAEvC,IAAI,QAAQ,KAAK,WAAW,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;oBACjD,OAAO,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC;gBACtD,CAAC;qBAAM,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;oBACpC,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;oBACrC,OAAO,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC;gBACjC,CAAC;qBAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAClC,OAAO,SAAS,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC;gBACxC,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CACzC,SAAS,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CACpD,CAAC;YACF,IAAI,CAAC,OAAO;gBACV,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrF,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,2CAA2C;IAC3C,OAAO,eAAe;SACnB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SAC/B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACZ,GAAG,IAAI;QACP,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;QAC7C,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;KACpD,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,MAA2B,EAAiB,EAAE;IAChF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEnC,sBAAsB;IACtB,MAAM,YAAY,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CACnC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAC9E,CAAC;IAEF,MAAM,YAAY,GAAkB,EAAE,CAAC;IACvC,IAAI,cAAc,GAAG,CAAC,CAAC;IAEvB,YAAY,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;QACxC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,cAAc,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QAEzC,YAAY,CAAC,IAAI,CAAC;YAChB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;YAC9C,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;YAC/C,WAAW,EAAE,KAAK,GAAG,CAAC;YACtB,UAAU,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;SACnC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CAAC,MAA2B,EAAqB,EAAE;IACzF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAEnC,oBAAoB;IACpB,MAAM,MAAM,GAAG;QACb,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;QACjD,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,iBAAiB,EAAE;QACnD,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;QACjD,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE;QAC3C,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE;QACzC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,cAAc,EAAE;QAC7C,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE;QAC/C,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;KAC/C,CAAC;IAEF,MAAM,YAAY,GAAsB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3D,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,KAAK,EAAE,CAAC;QACR,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;QACX,KAAK,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,0CAA0C;KAClE,CAAC,CAAC,CAAC;IAEJ,gCAAgC;IAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACzB,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAElF,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YACpB,YAAY,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;YACjC,YAAY,CAAC,UAAU,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACzB,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,mBAAmB;AACnB,MAAM,eAAe,GAAG,GAAuB,EAAE,CAAC,CAAC;IACjD,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,YAAY,EAAE,CAAC;IACf,SAAS,EAAE,CAAC;IACZ,OAAO,EAAE,CAAC;IACV,UAAU,EAAE,CAAC;IACb,WAAW,EAAE,CAAC;IACd,YAAY,EAAE,CAAC;IACf,eAAe,EAAE,CAAC;IAClB,UAAU,EAAE,CAAC;IACb,WAAW,EAAE,CAAC;IACd,eAAe,EAAE,CAAC;IAClB,UAAU,EAAE,CAAC;CACd,CAAC,CAAC;AAEH,MAAM,uBAAuB,GAAG,CAAC,MAAqB,EAAY,EAAE;IAClE,OAAO,MAAM;SACV,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS,CAAC;SACpD,GAAG,CAAC,KAAK,CAAC,EAAE;QACX,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAW,CAAC,CAAC,OAAO,EAAE,CAAC;QACxD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAU,CAAC,CAAC,OAAO,EAAE,CAAC;QACtD,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,sBAAsB;IACrE,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,UAAkB,EAAU,EAAE;IACtD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IACjC,MAAM,SAAS,GAAG,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC;IAErC,IAAI,SAAS,GAAG,GAAG,IAAI,SAAS,IAAI,EAAE;QAAE,OAAO,CAAC,CAAC,CAAC,CAAC,uBAAuB;IAC1E,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,IAAI,SAAS,GAAG,IAAI;QAAE,OAAO,CAAC,CAAC;IAC/B,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,+CAA+C;AAE/C,4EAA4E"}