{"version": 3, "file": "AnalysisTabs.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/AnalysisTabs.tsx"], "names": [], "mappings": ";AAQA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAavC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;SAEvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;mBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;;oBAEzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;;;;;;;;;CAS5D,CAAC;AAEF,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAqB;;;aAGjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;eACrF,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,UAAU;iBAC9C,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CACnC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,IAAI,KAAK;WAC3E,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB,CAAC,CAAC,CAAC,uBAAuB,CAAC;;6BAEzF,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAC/C,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB,CAAC,CAAC,CAAC,aAAa,CAAC;oBAC1D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,WAAW;;;;;;;;;;aAU5D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;;;;;qBAOtD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,IAAI,KAAK;;;;IAI/D,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,MAAM,IAAI;;;;;;;;oBAQjB,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;;;;;;;;;;GAYhE;CACF,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAA;;iBAEX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;;;;mBAIvC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,IAAI,KAAK;eAClD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,SAAS;;;CAG7D,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,GAA+D;IAC7E,OAAO,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;IAC7B,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC3B,OAAO,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;IAC7B,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;IACnC,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;IACnC,IAAI,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;CACjC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACxD,SAAS,EACT,WAAW,EACX,SAAS,GACV,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,CAAC,GAAoB,EAAE,EAAE;QAC9C,WAAW,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,aAAa,IAAC,SAAS,EAAE,SAAS,YAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,CAA6D,CAAC,GAAG,CAC1F,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CACpB,MAAC,GAAG,IAEF,MAAM,EAAE,SAAS,KAAK,MAAM,EAC5B,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,mBACtB,SAAS,KAAK,MAAM,EACnC,IAAI,EAAC,KAAK,aAET,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,KAAK,IAAI,KAAC,QAAQ,cAAE,MAAM,CAAC,KAAK,GAAY,KAP/C,MAAM,CAQP,CACP,CACF,GACa,CACjB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,YAAY,CAAC"}