import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * TradeAnalysisContainer Component
 *
 * REFACTORED: Main orchestrator for the trade analysis feature
 * Follows the proven TradingDashboard architecture pattern for consistency.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - F1 racing theme consistency
 * - Performance optimized with memoization
 * - Reusable tab navigation pattern
 */
import { Suspense } from 'react';
import styled from 'styled-components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { AnalysisHeader } from './AnalysisHeader';
import { AnalysisTabs } from './AnalysisTabs';
import { FilterPanel } from './FilterPanel';
import { TabContentRenderer } from './TabContentRenderer';
const AnalysisLayout = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1400px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  min-height: 100vh;
`;
const ContentArea = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const TabContentContainer = styled.div `
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
const ErrorFallback = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const RetryButton = styled.button `
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;
/**
 * Loading Fallback Component
 */
const LoadingFallback = () => (_jsxs("div", { style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '400px',
        gap: '16px',
    }, children: [_jsx(LoadingSpinner, { size: 'lg' }), _jsx("div", { style: { color: 'var(--text-secondary)' }, children: "Loading Trade Analysis..." })] }));
/**
 * Analysis Content Component (uses context)
 */
const AnalysisContent = () => {
    const { data, isLoading, error, preferences, updatePreferences, fetchData } = useTradeAnalysis();
    // Handle tab changes with preference persistence
    const handleTabChange = (tab) => {
        updatePreferences({ defaultView: tab });
    };
    if (error) {
        return (_jsxs(ErrorFallback, { children: [_jsx("div", { children: "\u274C Analysis Loading Error" }), _jsx("div", { children: error }), _jsx(RetryButton, { onClick: fetchData, children: "Retry Analysis" })] }));
    }
    return (_jsxs(AnalysisLayout, { children: [_jsx(AnalysisHeader, { onRefresh: fetchData, isRefreshing: isLoading }), _jsx(FilterPanel, {}), _jsx(AnalysisTabs, { activeTab: preferences.defaultView || 'summary', onTabChange: handleTabChange }), _jsx(ContentArea, { children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(TabContentContainer, { children: _jsx(TabContentRenderer, { activeTab: preferences.defaultView || 'summary', data: data, isLoading: isLoading, error: error }) }) }) })] }));
};
/**
 * TradeAnalysisContainer Component
 *
 * Main container that provides layout management and error boundaries
 * for the refactored trade analysis. Follows TradingDashboard patterns.
 */
export const TradeAnalysisContainer = ({ className }) => {
    return (_jsx("div", { className: className, children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(AnalysisContent, {}) }) }));
};
export default TradeAnalysisContainer;
//# sourceMappingURL=TradeAnalysisContainer.js.map