/**
 * Trade Analysis Table Component
 *
 * A component for displaying trade analysis data in a table.
 */
import React from 'react';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { TradeSort } from '../types/index';
export interface TradeAnalysisTableProps {
    /** The trades to display */
    trades: CompleteTradeData[];
    /** The current sort */
    sort: TradeSort;
    /** Function called when the sort is changed */
    onSort: (field: string, direction: 'asc' | 'desc') => void;
    /** The current page */
    page: number;
    /** Function called when the page is changed */
    onPageChange: (page: number) => void;
    /** The page size */
    pageSize: number;
    /** Function called when the page size is changed */
    onPageSizeChange: (pageSize: number) => void;
    /** The total number of pages */
    totalPages: number;
    /** Whether the component is in a loading state */
    isLoading?: boolean;
    /** Function called when a row is clicked */
    onRowClick?: (trade: CompleteTradeData) => void;
}
/**
 * Trade Analysis Table Component
 *
 * A component for displaying trade analysis data in a table.
 */
export declare const TradeAnalysisTable: React.FC<TradeAnalysisTableProps>;
//# sourceMappingURL=TradeAnalysisTable.d.ts.map