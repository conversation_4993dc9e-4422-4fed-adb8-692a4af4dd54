{"version": 3, "file": "TradeAnalysisCharts.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeAnalysisCharts.tsx"], "names": [], "mappings": ";AAMA,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAC;AAYtD;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAuC,CAAC,EACtE,eAAe,EACf,gBAAgB,EAChB,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,kBAAkB;IAClB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACvC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,gBAAgB;IAChB,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,6CAAoC,GACnF,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,6CAAoC,GACnF,IACH,CACP,CAAC;IACJ,CAAC;IAED,cAAc;IACd,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClE,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,4EAE9C,GACD,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,4EAE9C,GACD,IACH,CACP,CAAC;IACJ,CAAC;IAED,0CAA0C;IAC1C,MAAM,gBAAgB,GAAG,GAAG,CAAC;IAC7B,MAAM,iBAAiB,GAAG,GAAG,CAAC;IAC9B,MAAM,kBAAkB,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACxE,MAAM,qBAAqB,GACzB,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC;IACxE,MAAM,sBAAsB,GAC1B,iBAAiB,GAAG,kBAAkB,CAAC,GAAG,GAAG,kBAAkB,CAAC,MAAM,CAAC;IAEzE,sCAAsC;IACtC,MAAM,WAAW,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3E,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAE3E,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1D,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,cAAc,CAAC,CAAC;IAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,cAAc,CAAC,CAAC;IAE/D,qCAAqC;IACrC,MAAM,MAAM,GAAG,CAAC,IAAU,EAAE,EAAE;QAC5B,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,SAAS,CAAC;QACpE,OAAO,kBAAkB,CAAC,IAAI,GAAG,UAAU,GAAG,qBAAqB,CAAC;IACtE,CAAC,CAAC;IAEF,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE;QAC/B,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;QACzC,MAAM,UAAU,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC;QACpD,OAAO,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,UAAU,GAAG,sBAAsB,CAAC;IAC/F,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,KAAK,eAAe;SACpC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;SAC7D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;IAEjB,MAAM,YAAY,GAAG,KAAK,eAAe;SACtC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;SAC/D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;IAEjB,0CAA0C;IAC1C,MAAM,cAAc,GAAG,GAAG,CAAC;IAC3B,MAAM,eAAe,GAAG,GAAG,CAAC;IAC5B,MAAM,gBAAgB,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IACtE,MAAM,mBAAmB,GAAG,cAAc,GAAG,gBAAgB,CAAC,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC;IAC5F,MAAM,oBAAoB,GAAG,eAAe,GAAG,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAE9F,sCAAsC;IACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACnE,MAAM,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB,CAAC,MAAM,CAAC;IAE/D,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,aAC1E,KAAC,IAAI,IAAC,KAAK,EAAC,cAAc,YACxB,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,YAC7B,eAAK,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,aAErD,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,EAAE,EAAE,kBAAkB,CAAC,IAAI,GAAG,qBAAqB,EACnD,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,MAAM,EAAC,MAAM,GACb,EAGF,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,EAC1B,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,MAAM,EAAC,MAAM,GACb,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gCAC1C,MAAM,KAAK,GAAG,SAAS,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;gCAC/D,OAAO,CACL,wBACE,eACE,EAAE,EAAE,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAC/B,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EACjB,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAC3B,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EACjB,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,kBAAkB,CAAC,IAAI,GAAG,EAAE,EAC/B,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAChB,UAAU,EAAC,KAAK,EAChB,gBAAgB,EAAC,QAAQ,EACzB,QAAQ,EAAC,IAAI,YAEZ,cAAc,CAAC,KAAK,CAAC,GACjB,KAhBD,UAAU,CAiBd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gCAC1C,MAAM,IAAI,GAAG,IAAI,IAAI,CACnB,OAAO,CAAC,OAAO,EAAE,GAAG,UAAU,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CACzE,CAAC;gCACF,OAAO,CACL,wBACE,eACE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAChB,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,EACnD,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAChB,EAAE,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,CAAC,EACvD,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EACf,CAAC,EAAE,kBAAkB,CAAC,GAAG,GAAG,sBAAsB,GAAG,EAAE,EACvD,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,YAEZ,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,GAClE,KAfD,UAAU,CAgBd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGF,eACE,CAAC,EAAE,YAAY,EACf,IAAI,EAAC,MAAM,EACX,MAAM,EAAC,MAAM,EACb,WAAW,EAAC,GAAG,EACf,eAAe,EAAC,KAAK,GACrB,EAGF,eAAM,CAAC,EAAE,UAAU,EAAE,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,sBAAsB,EAAC,WAAW,EAAC,GAAG,GAAG,EAGjF,aACE,SAAS,EAAE,aAAa,kBAAkB,CAAC,IAAI,GAAG,EAAE,KAClD,kBAAkB,CAAC,GAAG,GAAG,EAC3B,GAAG,aAEH,eAAM,EAAE,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,EAAE,EAAC,IAAI,EAAC,EAAE,EAAC,GAAG,EAAC,MAAM,EAAC,sBAAsB,EAAC,WAAW,EAAC,GAAG,GAAG,EACnF,eAAM,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,GAAG,EAAC,gBAAgB,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,uBAEnD,EAEP,eACE,EAAE,EAAC,GAAG,EACN,EAAE,EAAC,IAAI,EACP,EAAE,EAAC,IAAI,EACP,EAAE,EAAC,IAAI,EACP,MAAM,EAAC,MAAM,EACb,WAAW,EAAC,GAAG,EACf,eAAe,EAAC,KAAK,GACrB,EACF,eAAM,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,gBAAgB,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,yBAEpD,IACL,IACA,GACF,GACD,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,qBAAqB,YAC/B,cAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,YAC7B,eAAK,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,aAEjD,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,EAAE,EAAE,gBAAgB,CAAC,IAAI,GAAG,mBAAmB,EAC/C,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,MAAM,EAAC,MAAM,GACb,EAGF,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,EACxB,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,EAC/C,MAAM,EAAC,MAAM,GACb,EAGD,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gCAC1C,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,CAAC;gCACpC,OAAO,CACL,wBACE,eACE,EAAE,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,EAC7B,EAAE,EACA,gBAAgB,CAAC,GAAG;gDACpB,oBAAoB;gDACpB,UAAU,GAAG,oBAAoB,EAEnC,EAAE,EAAE,gBAAgB,CAAC,IAAI,EACzB,EAAE,EACA,gBAAgB,CAAC,GAAG;gDACpB,oBAAoB;gDACpB,UAAU,GAAG,oBAAoB,EAEnC,MAAM,EAAC,MAAM,GACb,EACF,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,EAAE,EAC7B,CAAC,EACC,gBAAgB,CAAC,GAAG;gDACpB,oBAAoB;gDACpB,UAAU,GAAG,oBAAoB,EAEnC,UAAU,EAAC,KAAK,EAChB,gBAAgB,EAAC,QAAQ,EACzB,QAAQ,EAAC,IAAI,YAEZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GACb,KA5BD,UAAU,CA6Bd,CACL,CAAC;4BACJ,CAAC,CAAC,EAGD,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gCAC7B,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,oBAAoB,CAAC;gCAC9D,OAAO,CACL,wBACE,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,EACvC,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,SAAS,EAC1D,KAAK,EAAE,QAAQ,GAAG,CAAC,EACnB,MAAM,EAAE,SAAS,EACjB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EACrC,OAAO,EAAE,GAAG,GACZ,EAEF,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,EACtD,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,EAAE,EACnD,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,EACb,SAAS,EAAE,cACT,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CACpD,KAAK,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,EAAE,GAAG,YAEvD,CAAC,CAAC,KAAK,GACH,EAEP,eACE,CAAC,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,EACtD,CAAC,EAAE,gBAAgB,CAAC,GAAG,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,EAC9D,UAAU,EAAC,QAAQ,EACnB,QAAQ,EAAC,IAAI,EACb,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,YAEpC,CAAC,CAAC,KAAK,GACH,KA9BD,CAAC,CA+BL,CACL,CAAC;4BACJ,CAAC,CAAC,IACE,GACF,GACD,IACH,CACP,CAAC;AACJ,CAAC,CAAC"}