/**
 * Trade Analysis Components Index
 *
 * Centralized exports for all trade analysis components.
 * Organized by refactoring status and functionality.
 */
// REFACTORED COMPONENTS (New Architecture)
export { TradeAnalysisContainer } from './TradeAnalysisContainer';
export { AnalysisHeader } from './AnalysisHeader';
export { AnalysisTabs } from './AnalysisTabs';
export { Tab<PERSON>ontentRenderer } from './TabContentRenderer';
// EXISTING COMPONENTS (Maintained)
export { FilterPanel } from './FilterPanel';
export { PerformanceSummary } from './PerformanceSummary';
export { TradesTable } from './TradesTable';
export { CategoryPerformanceChart } from './CategoryPerformanceChart';
// CategoryPerformanceChartRefactored removed as file doesn't exist
export { TimePerformanceChart } from './TimePerformanceChart';
export { TradeDetail } from './TradeDetail';
export { MetricsPanel } from './MetricsPanel';
export { default as EquityCurve } from './EquityCurve';
//# sourceMappingURL=index.js.map