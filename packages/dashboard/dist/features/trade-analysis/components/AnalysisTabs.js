import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const TabsContainer = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  overflow-x: auto;
  
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;
const Tab = styled.button `
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: ${({ theme, active }) => active ? theme.fontWeights?.semibold || '600' : theme.fontWeights?.medium || '500'};
  color: ${({ theme, active }) => (active ? theme.colors?.primary || 'var(--primary-color)' : 'var(--text-secondary)')};
  cursor: pointer;
  border-bottom: 2px solid ${({ theme, active }) => (active ? theme.colors?.primary || 'var(--primary-color)' : 'transparent')};
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  position: relative;
  
  /* F1 Racing aesthetic */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  &:hover {
    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    background: rgba(220, 38, 38, 0.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    border-radius: ${({ theme }) => theme.borderRadius?.xs || '2px'};
  }

  /* Active tab indicator */
  ${({ active, theme }) => active && `
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: ${theme.colors?.primary || 'var(--primary-color)'};
      animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from {
        transform: scaleX(0);
      }
      to {
        transform: scaleX(1);
      }
    }
  `}
`;
const TabBadge = styled.span `
  display: inline-block;
  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: 2px 6px;
  background: rgba(220, 38, 38, 0.1);
  color: var(--primary-color);
  border-radius: ${({ theme }) => theme.borderRadius?.xs || '2px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  line-height: 1;
`;
/**
 * Tab configuration with labels and optional badges
 */
const TAB_CONFIG = {
    summary: { label: 'Summary' },
    trades: { label: 'Trades' },
    symbols: { label: 'Symbols' },
    strategies: { label: 'Strategies' },
    timeframes: { label: 'Timeframes' },
    time: { label: 'Time Analysis' },
};
/**
 * AnalysisTabs Component
 *
 * F1-themed tab navigation that provides consistent navigation
 * experience across the Trade Analysis feature.
 */
export const AnalysisTabs = ({ activeTab, onTabChange, className, }) => {
    const handleTabClick = (tab) => {
        onTabChange(tab);
    };
    return (_jsx(TabsContainer, { className: className, children: Object.entries(TAB_CONFIG).map(([tabKey, config]) => (_jsxs(Tab, { active: activeTab === tabKey, onClick: () => handleTabClick(tabKey), "aria-selected": activeTab === tabKey, role: "tab", children: [config.label, config.badge && _jsx(TabBadge, { children: config.badge })] }, tabKey))) }));
};
export default AnalysisTabs;
//# sourceMappingURL=AnalysisTabs.js.map