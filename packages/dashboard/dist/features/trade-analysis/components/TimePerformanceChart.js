import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
// TimePerformance import removed as unused
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
const Container = styled.div ``;
const ChartContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const TimeSlot = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const TimeSlotHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
const TimeSlotLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const TimeSlotMetrics = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const MetricValue = styled.span `
  color: ${({ theme, positive, negative }) => {
    if (positive)
        return theme.colors.profit;
    if (negative)
        return theme.colors.loss;
    return theme.colors.textSecondary;
}};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;
const BarContainer = styled.div `
  height: 24px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  overflow: hidden;
  position: relative;
`;
const Bar = styled.div `
  height: 100%;
  width: ${({ width }) => `${width}%`};
  background-color: ${({ theme, positive }) => positive ? theme.colors.profit : theme.colors.loss};
  transition: width 0.3s ease;
`;
const BarLabel = styled.div `
  position: absolute;
  top: 0;
  left: ${({ theme }) => theme.spacing.sm};
  height: 100%;
  display: flex;
  align-items: center;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.textInverse};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const TimePerformanceChart = ({ className, timeType, }) => {
    const { data } = useTradeAnalysis();
    if (!data) {
        return null;
    }
    const performanceData = timeType === 'timeOfDay' ? data.timeOfDayPerformance : data.dayOfWeekPerformance;
    if (!performanceData || performanceData.length === 0) {
        return _jsxs(EmptyState, { children: ["No ", timeType, " performance data available."] });
    }
    // Find max profit/loss for bar scaling
    const maxProfitLoss = Math.max(...performanceData.map(item => Math.abs(item.profitLoss)));
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value.toFixed(2)}%`;
    };
    return (_jsx(Container, { className: className, children: _jsx(ChartContainer, { children: performanceData.map((item, index) => (_jsxs(TimeSlot, { children: [_jsxs(TimeSlotHeader, { children: [_jsx(TimeSlotLabel, { children: item.timeSlot }), _jsxs(TimeSlotMetrics, { children: [_jsxs("div", { children: ["Trades: ", _jsx(MetricValue, { children: item.trades })] }), _jsxs("div", { children: ["Win Rate:", ' ', _jsx(MetricValue, { positive: item.winRate > 50, negative: item.winRate < 50, children: formatPercent(item.winRate) })] }), _jsxs("div", { children: ["P&L:", ' ', _jsx(MetricValue, { positive: item.profitLoss > 0, negative: item.profitLoss < 0, children: formatCurrency(item.profitLoss) })] })] })] }), _jsx(BarContainer, { children: _jsx(Bar, { width: Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100), positive: item.profitLoss >= 0, children: item.profitLoss !== 0 && _jsx(BarLabel, { children: formatCurrency(item.profitLoss) }) }) })] }, index))) }) }));
};
//# sourceMappingURL=TimePerformanceChart.js.map