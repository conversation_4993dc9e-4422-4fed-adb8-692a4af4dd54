{"version": 3, "file": "TradeDetail.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeDetail.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,iCAAiC;AACjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,gCAAgC,CAAC;AAMlE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;gBACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;mBACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;CAClD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM;WAC7C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;CACjD,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAmB;eACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;WAC/C,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAC5B,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBAC7E,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBACxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAA;gBACR,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;iBAC9B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;0BACtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;;CAE3D,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;aAChB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;WAEjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA+B,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;IACvE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAErD,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,eAAe,CAAC,CAAC;IAE9E,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,KAAC,UAAU,mCAA8B,CAAC;IACnD,CAAC;IAED,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAU,EAAE;QAChD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,OAAO,CACL,IAAI,CAAC,kBAAkB,EAAE;YACzB,GAAG;YACH,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CACpE,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,2CAA2C;IAE3C,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAU,EAAE;QACxD,OAAO,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEF,qEAAqE;IAErE,OAAO,CACL,MAAC,SAAS,IAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAC,eAAe,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,aACvF,KAAC,UAAU,IAAC,KAAK,EAAE,aAAa,CAAC,UAAU,IAAI,CAAC,YAC7C,cAAc,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,CAAC,GACnC,EAEb,MAAC,UAAU,eACT,MAAC,aAAa,eACZ,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,cACV,KAAC,KAAK,IAAC,OAAO,EAAE,mBAAmB,CAAC,aAAa,CAAC,SAAS,CAAQ,EAAE,IAAI,EAAC,OAAO,YAC9E,aAAa,CAAC,SAAS,GAClB,GACI,IACA,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,yBAAqB,EACjC,KAAC,WAAW,cAAE,aAAa,CAAC,MAAM,GAAe,IACnC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,cAAE,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAe,IAC7C,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,8BAA0B,EACtC,MAAC,WAAW,oBAAG,aAAa,CAAC,KAAK,IAAe,IACnC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,6BAAyB,EACrC,MAAC,WAAW,oBAAG,aAAa,CAAC,IAAI,IAAe,IAClC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,uBAAmB,EAC/B,KAAC,WAAW,cAAE,aAAa,CAAC,IAAI,GAAe,IACjC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,cAAE,aAAa,CAAC,QAAQ,GAAe,IACrC,EAEhB,MAAC,aAAa,eACZ,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,cAAE,aAAa,CAAC,QAAQ,GAAe,IACrC,IACL,EAEZ,aAAa,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CACtD,MAAC,aAAa,eACZ,KAAC,WAAW,uBAAmB,EAC/B,KAAC,aAAa,cACX,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CACtC,KAAC,GAAG,IAAa,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,YACzC,GAAG,IADI,KAAK,CAET,CACP,CAAC,GACY,IACF,CACjB,EAEA,aAAa,CAAC,KAAK,IAAI,CACtB,MAAC,KAAK,eACJ,KAAC,WAAW,wBAAoB,EAChC,KAAC,WAAW,cAAE,aAAa,CAAC,KAAK,GAAe,IAC1C,CACT,IACS,CACb,CAAC;AACJ,CAAC,CAAC"}