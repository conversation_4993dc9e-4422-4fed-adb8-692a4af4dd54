import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from "react/jsx-runtime";
import { DataCard } from '@adhd-trading-dashboard/shared';
import { PerformanceSummary } from './PerformanceSummary';
import { TradesTable } from './TradesTable';
import { CategoryPerformanceChart } from './CategoryPerformanceChart';
import { TimePerformanceChart } from './TimePerformanceChart';
import { TradeDetail } from './TradeDetail';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
/**
 * TabContentRenderer Component
 *
 * Handles rendering of content for each analysis tab with proper
 * error handling and loading states.
 */
export const TabContentRenderer = ({ activeTab, data, isLoading, error, }) => {
    const { selectedTradeId } = useTradeAnalysis();
    const renderContent = () => {
        switch (activeTab) {
            case 'summary':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance Summary", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.metrics, emptyMessage: "No performance data available for the selected filters.", children: _jsx(PerformanceSummary, {}) }), _jsx(DataCard, { title: "Performance by Time of Day", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0, emptyMessage: "No time of day performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "timeOfDay", title: "Time of Day" }) }), _jsx(DataCard, { title: "Performance by Day of Week", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0, emptyMessage: "No day of week performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "dayOfWeek", title: "Day of Week" }) })] }));
            case 'trades':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Trades", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.trades || data.trades.length === 0, emptyMessage: "No trades available for the selected filters.", children: _jsx(TradesTable, {}) }), selectedTradeId && _jsx(TradeDetail, {})] }));
            case 'symbols':
                return (_jsx(DataCard, { title: "Performance by Symbol", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.symbolPerformance || data.symbolPerformance.length === 0, emptyMessage: "No symbol performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "symbol", title: "Symbol" }) }));
            case 'strategies':
                return (_jsx(DataCard, { title: "Performance by Strategy", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.strategyPerformance || data.strategyPerformance.length === 0, emptyMessage: "No strategy performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "strategy", title: "Strategy" }) }));
            case 'timeframes':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance by Timeframe", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeframePerformance || data.timeframePerformance.length === 0, emptyMessage: "No timeframe performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "timeframe", title: "Timeframe" }) }), _jsx(DataCard, { title: "Performance by Session", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.sessionPerformance || data.sessionPerformance.length === 0, emptyMessage: "No session performance data available for the selected filters.", children: _jsx(CategoryPerformanceChart, { category: "session", title: "Session" }) })] }));
            case 'time':
                return (_jsxs(_Fragment, { children: [_jsx(DataCard, { title: "Performance by Time of Day", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0, emptyMessage: "No time of day performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "timeOfDay", title: "Time of Day" }) }), _jsx(DataCard, { title: "Performance by Day of Week", isLoading: isLoading, hasError: !!error, errorMessage: error || '', isEmpty: !data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0, emptyMessage: "No day of week performance data available for the selected filters.", children: _jsx(TimePerformanceChart, { timeType: "dayOfWeek", title: "Day of Week" }) })] }));
            default:
                return (_jsx(DataCard, { title: "Unknown Tab", hasError: true, errorMessage: `Unknown tab: ${activeTab}`, children: _jsxs("div", { style: { textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }, children: ["Tab content not found: ", activeTab] }) }));
        }
    };
    return _jsx(_Fragment, { children: renderContent() });
};
export default TabContentRenderer;
//# sourceMappingURL=TabContentRenderer.js.map