{"version": 3, "file": "PerformanceSummary.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/PerformanceSummary.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,oFAAoF;AACpF,8CAA8C;AAC9C,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAMjE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;sBACP,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;mBACzC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;;CAG3C,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAA4C;eAC3D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;iBAC/B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ;WAC/C,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;IACzC,IAAI,QAAQ;QAAE,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACzC,IAAI,QAAQ;QAAE,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;IACvC,OAAO,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,CAAC;CACF,CAAC;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAA4C;eAC5D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gBAChC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;WACpC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;IACzC,IAAI,QAAQ;QAAE,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACzC,IAAI,QAAQ;QAAE,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;IACvC,OAAO,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;AACpC,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAsC,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE;IACrF,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAEpC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAEzB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;YACf,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;QAC9C,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,SAAS,IAAC,SAAS,EAAE,SAAS,aAC7B,MAAC,UAAU,eACT,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,IAAC,QAAQ,EAAE,OAAO,CAAC,eAAe,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,eAAe,GAAG,CAAC,YACtF,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,GAC5B,IACH,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,2BAAuB,EACnC,KAAC,WAAW,IAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,YACxE,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,GACnB,EACd,MAAC,YAAY,eACV,OAAO,CAAC,aAAa,SAAK,OAAO,CAAC,WAAW,eACjC,IACJ,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,gCAA4B,EACxC,KAAC,WAAW,IAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC,YAChF,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GACpB,IACH,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,6BAAyB,EACrC,KAAC,WAAW,IAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,YAC5E,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GACvB,IACH,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,8BAA0B,EACtC,KAAC,WAAW,IAAC,QAAQ,EAAE,IAAI,YAAG,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAe,IACpE,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,WAAW,IAAC,QAAQ,EAAE,IAAI,YAAG,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAe,IAChF,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,8BAA0B,EACtC,KAAC,WAAW,IAAC,QAAQ,EAAE,IAAI,YAAG,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAe,IACpE,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,WAAW,IAAC,QAAQ,EAAE,IAAI,YAAG,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,GAAe,IACrE,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,WAAW,cAAE,OAAO,CAAC,WAAW,GAAe,EAChD,MAAC,YAAY,iCAAgB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,YAAoB,IACxE,IACH,CACb,CAAC;AACJ,CAAC,CAAC"}