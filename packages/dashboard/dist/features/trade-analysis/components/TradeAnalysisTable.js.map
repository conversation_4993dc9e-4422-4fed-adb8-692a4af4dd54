{"version": 3, "file": "TradeAnalysisTable.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TradeAnalysisTable.tsx"], "names": [], "mappings": ";AAMA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,gCAAgC,CAAC;AA2BpE;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAsC,CAAC,EACpE,MAAM,EACN,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS,GAAG,KAAK,EACjB,UAAU,GACX,EAAE,EAAE;IACH,kBAAkB;IAClB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACvC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YACpC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,cAAc;IACd,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAE,EAAE;QACxC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC,CAAC;IAEF,2BAA2B;IAC3B,MAAM,qBAAqB,GAAG,CAAC,MAAc,EAAE,EAAE;QAC/C,IAAI,MAAM,GAAG,CAAC;YAAE,OAAO,SAAS,CAAC;QACjC,IAAI,MAAM,GAAG,CAAC;YAAE,OAAO,OAAO,CAAC;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,gBAAgB;IAChB,MAAM,OAAO,GAAG;QACd;YACE,EAAE,EAAE,MAAM;YACV,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;YAC5D,QAAQ,EAAE,IAAI;SACf;QACD;YACE,EAAE,EAAE,QAAQ;YACZ,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK;YAC3D,QAAQ,EAAE,IAAI;SACf;QACD;YACE,EAAE,EAAE,WAAW;YACf,MAAM,EAAE,WAAW;YACnB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,CAChC,KAAC,KAAK,IAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,kBAC5E,GAAG,CAAC,KAAK,CAAC,SAAS,GACd,CACT;YACD,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,QAAiB;SACzB;QACD;YACE,EAAE,EAAE,aAAa;YACjB,MAAM,EAAE,aAAa;YACrB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;YAC5E,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,OAAgB;SACxB;QACD;YACE,EAAE,EAAE,YAAY;YAChB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;YAC3E,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,OAAgB;SACxB;QACD;YACE,EAAE,EAAE,iBAAiB;YACrB,MAAM,EAAE,WAAW;YACnB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC;YAChE,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,OAAgB;SACxB;QACD;YACE,EAAE,EAAE,QAAQ;YACZ,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,CAChC,KAAC,KAAK,IAAC,OAAO,EAAE,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,KAAK,kBACrE,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,GACrC,CACT;YACD,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,OAAgB;SACxB;QACD;YACE,EAAE,EAAE,YAAY;YAChB,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YACxE,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,OAAgB;SACxB;KACF,CAAC;IAEF,OAAO,CACL,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAClB,KAAC,KAAK,IACJ,OAAO,EAAE,OAAO,EAChB,IAAI,EAAE,MAAM,EACZ,SAAS,EAAE,SAAS,EACpB,QAAQ,QACR,OAAO,QACP,SAAS,QACT,UAAU,QACV,WAAW,EAAE,IAAI,EACjB,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,UAAU,GAAG,QAAQ,EAChC,YAAY,EAAE,YAAY,EAC1B,gBAAgB,EAAE,gBAAgB,EAClC,MAAM,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,EAC5D,UAAU,EAAE,IAAI,CAAC,KAAK,EACtB,aAAa,EAAE,IAAI,CAAC,SAAS,EAC7B,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAwB,CAAC,CAAC,CAAC,CAAC,SAAS,EAChF,YAAY,EAAC,8CAA8C,GAC3D,GACG,CACR,CAAC;AACJ,CAAC,CAAC"}