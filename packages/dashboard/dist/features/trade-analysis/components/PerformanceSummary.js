import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
// PerformanceMetrics import removed as unused
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
const Container = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const MetricCard = styled.div `
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
`;
const MetricLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const MetricValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme, positive, negative }) => {
    if (positive)
        return theme.colors.profit;
    if (negative)
        return theme.colors.loss;
    return theme.colors.textPrimary;
}};
`;
const MetricChange = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.xs};
  margin-top: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme, positive, negative }) => {
    if (positive)
        return theme.colors.profit;
    if (negative)
        return theme.colors.loss;
    return theme.colors.textSecondary;
}};
`;
export const PerformanceSummary = ({ className }) => {
    const { data } = useTradeAnalysis();
    if (!data) {
        return null;
    }
    const { metrics } = data;
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };
    const formatPercent = (value) => {
        return `${value.toFixed(2)}%`;
    };
    return (_jsxs(Container, { className: className, children: [_jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Total P&L" }), _jsx(MetricValue, { positive: metrics.totalProfitLoss > 0, negative: metrics.totalProfitLoss < 0, children: formatCurrency(metrics.totalProfitLoss) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Win Rate" }), _jsx(MetricValue, { positive: metrics.winRate > 50, negative: metrics.winRate < 50, children: formatPercent(metrics.winRate) }), _jsxs(MetricChange, { children: [metrics.winningTrades, " / ", metrics.totalTrades, " trades"] })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Profit Factor" }), _jsx(MetricValue, { positive: metrics.profitFactor > 1, negative: metrics.profitFactor < 1, children: metrics.profitFactor.toFixed(2) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Expectancy" }), _jsx(MetricValue, { positive: metrics.expectancy > 0, negative: metrics.expectancy < 0, children: formatCurrency(metrics.expectancy) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Average Win" }), _jsx(MetricValue, { positive: true, children: formatCurrency(metrics.averageWin) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Average Loss" }), _jsx(MetricValue, { negative: true, children: formatCurrency(-Math.abs(metrics.averageLoss)) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Largest Win" }), _jsx(MetricValue, { positive: true, children: formatCurrency(metrics.largestWin) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Largest Loss" }), _jsx(MetricValue, { negative: true, children: formatCurrency(metrics.largestLoss) })] }), _jsxs(MetricCard, { children: [_jsx(MetricLabel, { children: "Total Trades" }), _jsx(MetricValue, { children: metrics.totalTrades }), _jsxs(MetricChange, { children: ["Avg Duration: ", metrics.averageDuration.toFixed(0), " min"] })] })] }));
};
//# sourceMappingURL=PerformanceSummary.js.map