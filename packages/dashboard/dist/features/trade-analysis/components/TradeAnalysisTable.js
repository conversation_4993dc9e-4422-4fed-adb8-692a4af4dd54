import { jsx as _jsx } from "react/jsx-runtime";
import { Table, Card, Badge } from '@adhd-trading-dashboard/shared';
/**
 * Trade Analysis Table Component
 *
 * A component for displaying trade analysis data in a table.
 */
export const TradeAnalysisTable = ({ trades, sort, onSort, page, onPageChange, pageSize, onPageSizeChange, totalPages, isLoading = false, onRowClick, }) => {
    // Format currency
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(value);
    };
    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };
    // Get profit badge variant
    const getProfitBadgeVariant = (profit) => {
        if (profit > 0)
            return 'success';
        if (profit < 0)
            return 'error';
        return 'neutral';
    };
    // Table columns
    const columns = [
        {
            id: 'date',
            header: 'Date',
            cell: (row) => formatDate(row.trade.date),
            sortable: true,
        },
        {
            id: 'symbol',
            header: 'Symbol',
            cell: (row) => row.trade.market || 'N/A',
            sortable: true,
        },
        {
            id: 'direction',
            header: 'Direction',
            cell: (row) => (_jsx(Badge, { variant: row.trade.direction === 'Long' ? 'primary' : 'secondary', solid: true, children: row.trade.direction })),
            sortable: true,
            align: 'center',
        },
        {
            id: 'entry_price',
            header: 'Entry Price',
            cell: (row) => formatCurrency(row.trade.entry_price || 0),
            sortable: true,
            align: 'right',
        },
        {
            id: 'exit_price',
            header: 'Exit Price',
            cell: (row) => formatCurrency(row.trade.exit_price || 0),
            sortable: true,
            align: 'right',
        },
        {
            id: 'no_of_contracts',
            header: 'Contracts',
            cell: (row) => row.trade.no_of_contracts || 0,
            sortable: true,
            align: 'right',
        },
        {
            id: 'profit',
            header: 'P&L',
            cell: (row) => (_jsx(Badge, { variant: getProfitBadgeVariant(row.trade.achieved_pl || 0), solid: true, children: formatCurrency(row.trade.achieved_pl || 0) })),
            sortable: true,
            align: 'right',
        },
        {
            id: 'r_multiple',
            header: 'R-Multiple',
            cell: (row) => (row.trade.r_multiple || 0).toFixed(2),
            sortable: true,
            align: 'right',
        },
    ];
    return (_jsx(Card, { title: 'Trades', children: _jsx(Table, { columns: columns, data: trades, isLoading: isLoading, bordered: true, striped: true, hoverable: true, pagination: true, currentPage: page, pageSize: pageSize, totalRows: totalPages * pageSize, onPageChange: onPageChange, onPageSizeChange: onPageSizeChange, onSort: (columnId, direction) => onSort(columnId, direction), sortColumn: sort.field, sortDirection: sort.direction, onRowClick: onRowClick ? row => onRowClick(row) : undefined, emptyMessage: 'No trades found. Try adjusting your filters.' }) }));
};
//# sourceMappingURL=TradeAnalysisTable.js.map