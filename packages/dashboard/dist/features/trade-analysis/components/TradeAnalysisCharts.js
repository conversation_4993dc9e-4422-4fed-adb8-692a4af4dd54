import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card } from '@adhd-trading-dashboard/shared';
/**
 * Trade Analysis Charts Component
 *
 * A component for displaying trade analysis charts.
 */
export const TradeAnalysisCharts = ({ equityCurveData, distributionData, isLoading = false, }) => {
    // Format currency
    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };
    // Loading state
    if (isLoading) {
        return (_jsxs("div", { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }, children: [_jsx(Card, { title: "Equity Curve", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Loading equity curve data..." }) }), _jsx(Card, { title: "Profit Distribution", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Loading distribution data..." }) })] }));
    }
    // Empty state
    if (equityCurveData.length === 0 || distributionData.length === 0) {
        return (_jsxs("div", { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }, children: [_jsx(Card, { title: "Equity Curve", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "No equity curve data available. Try adjusting your filters." }) }), _jsx(Card, { title: "Profit Distribution", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "No distribution data available. Try adjusting your filters." }) })] }));
    }
    // Calculate equity curve chart dimensions
    const equityChartWidth = 500;
    const equityChartHeight = 300;
    const equityChartPadding = { top: 20, right: 20, bottom: 30, left: 60 };
    const equityChartInnerWidth = equityChartWidth - equityChartPadding.left - equityChartPadding.right;
    const equityChartInnerHeight = equityChartHeight - equityChartPadding.top - equityChartPadding.bottom;
    // Calculate equity curve chart scales
    const equityDates = equityCurveData.map((d) => new Date(d.date));
    const minDate = new Date(Math.min(...equityDates.map((d) => d.getTime())));
    const maxDate = new Date(Math.max(...equityDates.map((d) => d.getTime())));
    const equityValues = equityCurveData.map((d) => d.equity);
    const baselineValues = equityCurveData.map((d) => d.baseline);
    const minEquity = Math.min(...equityValues, ...baselineValues);
    const maxEquity = Math.max(...equityValues, ...baselineValues);
    // Calculate equity curve chart paths
    const xScale = (date) => {
        const dateRange = maxDate.getTime() - minDate.getTime();
        const percentage = (date.getTime() - minDate.getTime()) / dateRange;
        return equityChartPadding.left + percentage * equityChartInnerWidth;
    };
    const yScale = (value) => {
        const valueRange = maxEquity - minEquity;
        const percentage = (value - minEquity) / valueRange;
        return equityChartPadding.top + equityChartInnerHeight - percentage * equityChartInnerHeight;
    };
    const equityPath = `M ${equityCurveData
        .map((d) => `${xScale(new Date(d.date))},${yScale(d.equity)}`)
        .join(' L ')}`;
    const baselinePath = `M ${equityCurveData
        .map((d) => `${xScale(new Date(d.date))},${yScale(d.baseline)}`)
        .join(' L ')}`;
    // Calculate distribution chart dimensions
    const distChartWidth = 500;
    const distChartHeight = 300;
    const distChartPadding = { top: 20, right: 20, bottom: 60, left: 60 };
    const distChartInnerWidth = distChartWidth - distChartPadding.left - distChartPadding.right;
    const distChartInnerHeight = distChartHeight - distChartPadding.top - distChartPadding.bottom;
    // Calculate distribution chart scales
    const maxCount = Math.max(...distributionData.map((d) => d.count));
    const barWidth = distChartInnerWidth / distributionData.length;
    return (_jsxs("div", { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }, children: [_jsx(Card, { title: "Equity Curve", children: _jsx("div", { style: { padding: '16px' }, children: _jsxs("svg", { width: equityChartWidth, height: equityChartHeight, children: [_jsx("line", { x1: equityChartPadding.left, y1: equityChartPadding.top + equityChartInnerHeight, x2: equityChartPadding.left + equityChartInnerWidth, y2: equityChartPadding.top + equityChartInnerHeight, stroke: "#ccc" }), _jsx("line", { x1: equityChartPadding.left, y1: equityChartPadding.top, x2: equityChartPadding.left, y2: equityChartPadding.top + equityChartInnerHeight, stroke: "#ccc" }), [0, 0.25, 0.5, 0.75, 1].map((percentage) => {
                                const value = minEquity + percentage * (maxEquity - minEquity);
                                return (_jsxs("g", { children: [_jsx("line", { x1: equityChartPadding.left - 5, y1: yScale(value), x2: equityChartPadding.left, y2: yScale(value), stroke: "#ccc" }), _jsx("text", { x: equityChartPadding.left - 10, y: yScale(value), textAnchor: "end", dominantBaseline: "middle", fontSize: "12", children: formatCurrency(value) })] }, percentage));
                            }), [0, 0.25, 0.5, 0.75, 1].map((percentage) => {
                                const date = new Date(minDate.getTime() + percentage * (maxDate.getTime() - minDate.getTime()));
                                return (_jsxs("g", { children: [_jsx("line", { x1: xScale(date), y1: equityChartPadding.top + equityChartInnerHeight, x2: xScale(date), y2: equityChartPadding.top + equityChartInnerHeight + 5, stroke: "#ccc" }), _jsx("text", { x: xScale(date), y: equityChartPadding.top + equityChartInnerHeight + 20, textAnchor: "middle", fontSize: "12", children: date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' }) })] }, percentage));
                            }), _jsx("path", { d: baselinePath, fill: "none", stroke: "#999", strokeWidth: "2", strokeDasharray: "5,5" }), _jsx("path", { d: equityPath, fill: "none", stroke: "var(--primary-color)", strokeWidth: "2" }), _jsxs("g", { transform: `translate(${equityChartPadding.left + 20}, ${equityChartPadding.top + 20})`, children: [_jsx("line", { x1: "0", y1: "0", x2: "20", y2: "0", stroke: "var(--primary-color)", strokeWidth: "2" }), _jsx("text", { x: "25", y: "0", dominantBaseline: "middle", fontSize: "12", children: "Equity" }), _jsx("line", { x1: "0", y1: "20", x2: "20", y2: "20", stroke: "#999", strokeWidth: "2", strokeDasharray: "5,5" }), _jsx("text", { x: "25", y: "20", dominantBaseline: "middle", fontSize: "12", children: "Baseline" })] })] }) }) }), _jsx(Card, { title: "Profit Distribution", children: _jsx("div", { style: { padding: '16px' }, children: _jsxs("svg", { width: distChartWidth, height: distChartHeight, children: [_jsx("line", { x1: distChartPadding.left, y1: distChartPadding.top + distChartInnerHeight, x2: distChartPadding.left + distChartInnerWidth, y2: distChartPadding.top + distChartInnerHeight, stroke: "#ccc" }), _jsx("line", { x1: distChartPadding.left, y1: distChartPadding.top, x2: distChartPadding.left, y2: distChartPadding.top + distChartInnerHeight, stroke: "#ccc" }), [0, 0.25, 0.5, 0.75, 1].map((percentage) => {
                                const value = percentage * maxCount;
                                return (_jsxs("g", { children: [_jsx("line", { x1: distChartPadding.left - 5, y1: distChartPadding.top +
                                                distChartInnerHeight -
                                                percentage * distChartInnerHeight, x2: distChartPadding.left, y2: distChartPadding.top +
                                                distChartInnerHeight -
                                                percentage * distChartInnerHeight, stroke: "#ccc" }), _jsx("text", { x: distChartPadding.left - 10, y: distChartPadding.top +
                                                distChartInnerHeight -
                                                percentage * distChartInnerHeight, textAnchor: "end", dominantBaseline: "middle", fontSize: "12", children: Math.round(value) })] }, percentage));
                            }), distributionData.map((d, i) => {
                                const barHeight = (d.count / maxCount) * distChartInnerHeight;
                                return (_jsxs("g", { children: [_jsx("rect", { x: distChartPadding.left + i * barWidth, y: distChartPadding.top + distChartInnerHeight - barHeight, width: barWidth - 4, height: barHeight, fill: d.isWin ? 'var(--success-color)' : 'var(--error-color)', opacity: 0.8 }), _jsx("text", { x: distChartPadding.left + i * barWidth + barWidth / 2, y: distChartPadding.top + distChartInnerHeight + 15, textAnchor: "middle", fontSize: "10", transform: `rotate(45, ${distChartPadding.left + i * barWidth + barWidth / 2}, ${distChartPadding.top + distChartInnerHeight + 15})`, children: d.range }), _jsx("text", { x: distChartPadding.left + i * barWidth + barWidth / 2, y: distChartPadding.top + distChartInnerHeight - barHeight - 5, textAnchor: "middle", fontSize: "10", fill: d.isWin ? 'var(--success-color)' : 'var(--error-color)', children: d.count })] }, i));
                            })] }) }) })] }));
};
//# sourceMappingURL=TradeAnalysisCharts.js.map