/**
 * useTradesTableData Hook
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Custom hook for managing trades table data, sorting, and formatting.
 *
 * BENEFITS:
 * - Focused responsibility (data management only)
 * - Reusable across different table implementations
 * - Optimized with useMemo for performance
 * - Clean separation of business logic
 * - Easy to test and maintain
 */
import { useState, useMemo, useCallback } from 'react';
/**
 * useTradesTableData Hook
 *
 * Manages sorting, filtering, and formatting for the trades table.
 * Optimized for performance with memoization.
 */
export const useTradesTableData = (trades) => {
    const [sortField, setSortField] = useState('entryTime');
    const [sortDirection, setSortDirection] = useState('desc');
    /**
     * Handle sorting logic
     */
    const handleSort = useCallback((field) => {
        if (sortField === field) {
            // Toggle direction if same field
            setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
        }
        else {
            // Set new field and default direction
            setSortField(field);
            setSortDirection('desc');
        }
    }, [sortField]);
    /**
     * Sort trades based on current sort field and direction
     */
    const sortedTrades = useMemo(() => {
        if (!trades || trades.length === 0)
            return [];
        return [...trades].sort((a, b) => {
            let comparison = 0;
            switch (sortField) {
                case 'entryTime':
                    comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
                    break;
                case 'symbol':
                    comparison = a.symbol.localeCompare(b.symbol);
                    break;
                case 'direction':
                    comparison = a.direction.localeCompare(b.direction);
                    break;
                case 'profitLoss':
                    comparison = a.profitLoss - b.profitLoss;
                    break;
                case 'profitLossPercent':
                    // Calculate percentage from profitLoss since profitLossPercent doesn't exist on Trade
                    comparison = a.profitLoss - b.profitLoss;
                    break;
                case 'status':
                    // Derive status from profitLoss since status doesn't exist on Trade
                    const statusA = a.profitLoss > 0 ? 'win' : a.profitLoss < 0 ? 'loss' : 'breakeven';
                    const statusB = b.profitLoss > 0 ? 'win' : b.profitLoss < 0 ? 'loss' : 'breakeven';
                    comparison = statusA.localeCompare(statusB);
                    break;
                default:
                    comparison = 0;
            }
            return sortDirection === 'asc' ? comparison : -comparison;
        });
    }, [trades, sortField, sortDirection]);
    /**
     * Formatting utilities
     */
    const formatters = useMemo(() => ({
        /**
         * Format date string for display
         */
        formatDate: (dateString) => {
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    return 'Invalid Date';
                }
                return (date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: '2-digit',
                }) +
                    ' ' +
                    date.toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false,
                    }));
            }
            catch (error) {
                console.warn('Error formatting date:', dateString, error);
                return 'Invalid Date';
            }
        },
        /**
         * Format currency value for display
         */
        formatCurrency: (value) => {
            try {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                }).format(value);
            }
            catch (error) {
                console.warn('Error formatting currency:', value, error);
                return `$${value.toFixed(2)}`;
            }
        },
        /**
         * Format percentage value for display
         */
        formatPercent: (value) => {
            try {
                const sign = value > 0 ? '+' : '';
                return `${sign}${value.toFixed(2)}%`;
            }
            catch (error) {
                console.warn('Error formatting percent:', value, error);
                return `${value}%`;
            }
        },
    }), []);
    /**
     * Event handlers and utilities
     */
    const handlers = useMemo(() => ({
        /**
         * Get badge variant for trade direction
         */
        getDirectionVariant: (direction) => {
            switch (direction.toLowerCase()) {
                case 'long':
                    return 'success';
                case 'short':
                    return 'error';
                default:
                    return 'default';
            }
        },
        /**
         * Get badge variant for trade status
         */
        getStatusVariant: (status) => {
            switch (status.toLowerCase()) {
                case 'win':
                    return 'success';
                case 'loss':
                    return 'error';
                case 'breakeven':
                    return 'warning';
                case 'open':
                    return 'info';
                default:
                    return 'default';
            }
        },
    }), []);
    return {
        sortedTrades,
        sortField,
        sortDirection,
        handleSort,
        formatters,
        handlers,
    };
};
export default useTradesTableData;
//# sourceMappingURL=useTradesTableData.js.map