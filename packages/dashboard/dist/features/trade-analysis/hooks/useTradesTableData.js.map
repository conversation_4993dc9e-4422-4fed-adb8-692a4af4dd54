{"version": 3, "file": "useTradesTableData.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/useTradesTableData.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AA0BvD;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,MAAe,EAA4B,EAAE;IAC9E,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAY,WAAW,CAAC,CAAC;IACnE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAgB,MAAM,CAAC,CAAC;IAE1E;;OAEG;IACH,MAAM,UAAU,GAAG,WAAW,CAC5B,CAAC,KAAgB,EAAE,EAAE;QACnB,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACxB,iCAAiC;YACjC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,sCAAsC;YACtC,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC,EACD,CAAC,SAAS,CAAC,CACZ,CAAC;IAEF;;OAEG;IACH,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;QAChC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAE9C,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,WAAW;oBACd,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;oBACrE,MAAM;gBACR,KAAK,QAAQ;oBACX,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,WAAW;oBACd,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBACpD,MAAM;gBACR,KAAK,YAAY;oBACf,UAAU,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;oBACzC,MAAM;gBACR,KAAK,mBAAmB;oBACtB,sFAAsF;oBACtF,UAAU,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;oBACzC,MAAM;gBACR,KAAK,QAAQ;oBACX,oEAAoE;oBACpE,MAAM,OAAO,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;oBACnF,MAAM,OAAO,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;oBACnF,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAC5C,MAAM;gBACR;oBACE,UAAU,GAAG,CAAC,CAAC;YACnB,CAAC;YAED,OAAO,aAAa,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,CAAC;IAEvC;;OAEG;IACH,MAAM,UAAU,GAAG,OAAO,CACxB,GAAG,EAAE,CAAC,CAAC;QACL;;WAEG;QACH,UAAU,EAAE,CAAC,UAAkB,EAAU,EAAE;YACzC,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;oBAC1B,OAAO,cAAc,CAAC;gBACxB,CAAC;gBAED,OAAO,CACL,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;oBAC/B,KAAK,EAAE,OAAO;oBACd,GAAG,EAAE,SAAS;oBACd,IAAI,EAAE,SAAS;iBAChB,CAAC;oBACF,GAAG;oBACH,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;wBAC/B,IAAI,EAAE,SAAS;wBACf,MAAM,EAAE,SAAS;wBACjB,MAAM,EAAE,KAAK;qBACd,CAAC,CACH,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC1D,OAAO,cAAc,CAAC;YACxB,CAAC;QACH,CAAC;QAED;;WAEG;QACH,cAAc,EAAE,CAAC,KAAa,EAAU,EAAE;YACxC,IAAI,CAAC;gBACH,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;oBACpC,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,KAAK;oBACf,qBAAqB,EAAE,CAAC;oBACxB,qBAAqB,EAAE,CAAC;iBACzB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACzD,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;QAED;;WAEG;QACH,aAAa,EAAE,CAAC,KAAa,EAAU,EAAE;YACvC,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACxD,OAAO,GAAG,KAAK,GAAG,CAAC;YACrB,CAAC;QACH,CAAC;KACF,CAAC,EACF,EAAE,CACH,CAAC;IAEF;;OAEG;IACH,MAAM,QAAQ,GAAG,OAAO,CACtB,GAAG,EAAE,CAAC,CAAC;QACL;;WAEG;QACH,mBAAmB,EAAE,CAAC,SAAiB,EAAU,EAAE;YACjD,QAAQ,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;gBAChC,KAAK,MAAM;oBACT,OAAO,SAAS,CAAC;gBACnB,KAAK,OAAO;oBACV,OAAO,OAAO,CAAC;gBACjB;oBACE,OAAO,SAAS,CAAC;YACrB,CAAC;QACH,CAAC;QAED;;WAEG;QACH,gBAAgB,EAAE,CAAC,MAAc,EAAU,EAAE;YAC3C,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC7B,KAAK,KAAK;oBACR,OAAO,SAAS,CAAC;gBACnB,KAAK,MAAM;oBACT,OAAO,OAAO,CAAC;gBACjB,KAAK,WAAW;oBACd,OAAO,SAAS,CAAC;gBACnB,KAAK,MAAM;oBACT,OAAO,MAAM,CAAC;gBAChB;oBACE,OAAO,SAAS,CAAC;YACrB,CAAC;QACH,CAAC;KACF,CAAC,EACF,EAAE,CACH,CAAC;IAEF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,aAAa;QACb,UAAU;QACV,UAAU;QACV,QAAQ;KACT,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,kBAAkB,CAAC"}