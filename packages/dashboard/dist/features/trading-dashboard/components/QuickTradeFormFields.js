import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { F1FormField } from '@adhd-trading-dashboard/shared';
const FieldsContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const FieldRow = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;
const FieldGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const GroupLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
/**
 * Symbol options for quick selection
 */
const SYMBOL_OPTIONS = [
    { value: 'MNQ', label: 'MNQ (Micro Nasdaq)' },
    { value: 'MES', label: 'MES (Micro S&P)' },
    { value: 'MYM', label: 'MYM (Micro Dow)' },
    { value: 'M2K', label: 'M2K (Micro Russell)' },
    { value: 'NQ', label: 'NQ (Nasdaq)' },
    { value: 'ES', label: 'ES (S&P 500)' },
    { value: 'YM', label: 'YM (Dow)' },
    { value: 'RTY', label: 'RTY (Russell)' },
    { value: 'CUSTOM', label: 'Custom Symbol' },
];
/**
 * Direction options
 */
const DIRECTION_OPTIONS = [
    { value: 'long', label: '📈 Long (Buy)' },
    { value: 'short', label: '📉 Short (Sell)' },
];
/**
 * QuickTradeFormFields Component
 *
 * Renders all form fields for the quick trade entry form
 * using the standardized F1FormField components.
 */
export const QuickTradeFormFields = ({ dateField, symbolField, directionField, quantityField, entryPriceField, exitPriceField, }) => {
    return (_jsxs(FieldsContainer, { children: [_jsxs(FieldGroup, { children: [_jsx(GroupLabel, { children: "\uD83D\uDCCA Basic Trade Information" }), _jsxs(FieldRow, { children: [_jsx(F1FormField, { label: "Trade Date", field: dateField, type: "text", placeholder: "YYYY-MM-DD", required: true, variant: "trading", size: "md", helpText: "Date when the trade was executed" }), _jsx(F1FormField, { label: "Symbol", field: symbolField, type: "select", placeholder: "Select trading symbol", required: true, variant: "trading", size: "md", options: SYMBOL_OPTIONS, helpText: "Trading instrument symbol" })] }), _jsxs(FieldRow, { children: [_jsx(F1FormField, { label: "Direction", field: directionField, type: "select", placeholder: "Select trade direction", required: true, variant: "trading", size: "md", options: DIRECTION_OPTIONS, helpText: "Long (buy) or Short (sell)" }), _jsx(F1FormField, { label: "Quantity", field: quantityField, type: "number", placeholder: "1", required: true, variant: "trading", size: "md", inputProps: {
                                    min: 1,
                                    step: 1,
                                }, helpText: "Number of contracts/shares" })] })] }), _jsxs(FieldGroup, { children: [_jsx(GroupLabel, { children: "\uD83D\uDCB0 Price Information" }), _jsxs(FieldRow, { children: [_jsx(F1FormField, { label: "Entry Price", field: entryPriceField, type: "number", placeholder: "0.00", required: true, variant: "trading", size: "md", prefix: "$", inputProps: {
                                    min: 0.01,
                                    step: 0.01,
                                }, helpText: "Price at which position was opened" }), _jsx(F1FormField, { label: "Exit Price", field: exitPriceField, type: "number", placeholder: "0.00", required: true, variant: "trading", size: "md", prefix: "$", inputProps: {
                                    min: 0.01,
                                    step: 0.01,
                                }, helpText: "Price at which position was closed" })] })] })] }));
};
export default QuickTradeFormFields;
//# sourceMappingURL=QuickTradeFormFields.js.map