import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
const ChartContainer = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  height: 300px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const ChartTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
const LoadingContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const NoDataContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * Custom tooltip for the chart
 */
const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
        return (_jsxs("div", { style: {
                backgroundColor: '#252a37',
                padding: '10px',
                border: '1px solid #333',
                borderRadius: '4px',
            }, children: [_jsx("p", { style: { margin: 0 }, children: `Date: ${label}` }), _jsx("p", { style: { margin: 0, color: 'var(--primary-color)' }, children: `Daily P&L: $${payload[0].value.toFixed(2)}` }), _jsx("p", { style: { margin: 0, color: 'var(--info-color)' }, children: `Cumulative P&L: $${payload[1].value.toFixed(2)}` })] }));
    }
    return null;
};
/**
 * PerformanceChart Component
 *
 * Displays a chart showing trading performance over time
 */
export const PerformanceChart = ({ data, isLoading = false }) => {
    if (isLoading) {
        return (_jsxs(ChartContainer, { children: [_jsx(ChartTitle, { children: "Performance" }), _jsx(LoadingContainer, { children: "Loading chart data..." })] }));
    }
    if (!data || data.length === 0) {
        return (_jsxs(ChartContainer, { children: [_jsx(ChartTitle, { children: "Performance" }), _jsx(NoDataContainer, { children: "No performance data available" })] }));
    }
    return (_jsxs(ChartContainer, { children: [_jsx(ChartTitle, { children: "Performance" }), _jsx(ResponsiveContainer, { width: "100%", height: "90%", children: _jsxs(LineChart, { data: data, margin: {
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                    }, children: [_jsx(CartesianGrid, { strokeDasharray: "3 3", stroke: "rgba(255, 255, 255, 0.1)" }), _jsx(XAxis, { dataKey: "date", stroke: "#aaaaaa", tick: { fill: '#aaaaaa' } }), _jsx(YAxis, { stroke: "#aaaaaa", tick: { fill: '#aaaaaa' }, tickFormatter: (value) => `$${value}` }), _jsx(Tooltip, { content: _jsx(CustomTooltip, {}) }), _jsx(Legend, {}), _jsx(Line, { type: "monotone", dataKey: "pnl", name: "Daily P&L", stroke: "var(--primary-color)", activeDot: { r: 8 }, strokeWidth: 2 }), _jsx(Line, { type: "monotone", dataKey: "cumulative", name: "Cumulative P&L", stroke: "var(--info-color)", strokeWidth: 2 })] }) })] }));
};
export default PerformanceChart;
//# sourceMappingURL=PerformanceChart.js.map