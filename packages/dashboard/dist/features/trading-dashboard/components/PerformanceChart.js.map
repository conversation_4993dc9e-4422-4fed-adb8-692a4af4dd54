{"version": 3, "file": "PerformanceChart.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/PerformanceChart.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AACvC,OAAO,EACL,SAAS,EACT,IAAI,EACJ,KAAK,EACL,KAAK,EACL,aAAa,EACb,OAAO,EACP,mBAAmB,EACnB,MAAM,EACP,MAAM,UAAU,CAAC;AAQlB,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAA;sBACX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;mBACtC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;gBAC5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;mBAE5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,CAAA;eACb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;gBAClC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC9C,CAAC;AAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAKxB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAKvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAkB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;IAClE,IAAI,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACxC,OAAO,CACL,eACE,KAAK,EAAE;gBACL,eAAe,EAAE,SAAS;gBAC1B,OAAO,EAAE,MAAM;gBACf,MAAM,EAAE,gBAAgB;gBACxB,YAAY,EAAE,KAAK;aACpB,aAED,YAAG,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,YAAG,SAAS,KAAK,EAAE,GAAK,EAC/C,YAAG,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,YACnD,eAAe,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAC3C,EACJ,YAAG,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,YAChD,oBAAoB,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAChD,IACA,CACP,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAChE,IAAI,EACJ,SAAS,GAAG,KAAK,EAClB,EAAE,EAAE;IACH,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,8BAAyB,EACpC,KAAC,gBAAgB,wCAAyC,IAC3C,CAClB,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/B,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,8BAAyB,EACpC,KAAC,eAAe,gDAAgD,IACjD,CAClB,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,cAAc,eACb,KAAC,UAAU,8BAAyB,EACpC,KAAC,mBAAmB,IAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,YAC5C,MAAC,SAAS,IACR,IAAI,EAAE,IAAI,EACV,MAAM,EAAE;wBACN,GAAG,EAAE,CAAC;wBACN,KAAK,EAAE,EAAE;wBACT,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,CAAC;qBACV,aAED,KAAC,aAAa,IAAC,eAAe,EAAC,KAAK,EAAC,MAAM,EAAC,0BAA0B,GAAG,EACzE,KAAC,KAAK,IACJ,OAAO,EAAC,MAAM,EACd,MAAM,EAAC,SAAS,EAChB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,GACzB,EACF,KAAC,KAAK,IACJ,MAAM,EAAC,SAAS,EAChB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EACzB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,EAAE,GACrC,EACF,KAAC,OAAO,IAAC,OAAO,EAAE,KAAC,aAAa,KAAG,GAAI,EACvC,KAAC,MAAM,KAAG,EACV,KAAC,IAAI,IACH,IAAI,EAAC,UAAU,EACf,OAAO,EAAC,KAAK,EACb,IAAI,EAAC,WAAW,EAChB,MAAM,EAAC,sBAAsB,EAC7B,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EACnB,WAAW,EAAE,CAAC,GACd,EACF,KAAC,IAAI,IACH,IAAI,EAAC,UAAU,EACf,OAAO,EAAC,YAAY,EACpB,IAAI,EAAC,gBAAgB,EACrB,MAAM,EAAC,mBAAmB,EAC1B,WAAW,EAAE,CAAC,GACd,IACQ,GACQ,IACP,CAClB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,gBAAgB,CAAC"}