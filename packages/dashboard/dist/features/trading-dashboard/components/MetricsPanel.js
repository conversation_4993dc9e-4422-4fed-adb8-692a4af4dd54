import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
// F1 Racing Performance Grid
const Container = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;
// F1 Racing Metric Card
const MetricCard = styled.div `
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors.surface} 0%,
    rgba(75, 85, 99, 0.05) 100%
  );
  border: 1px solid var(--border-primary);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.normal};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--border-primary) 0%, transparent 100%);
  }

  &::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(75, 85, 99, 0.1) 0%, transparent 70%);
    border-radius: 50%;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(75, 85, 99, 0.2);
    border-color: var(--text-secondary);
  }
`;
// F1 Racing Metric Title
const MetricTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: bold;
  position: relative;
  z-index: 2;
`;
// F1 Racing Metric Value
const MetricValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.h3};
  font-weight: bold;
  color: ${({ theme }) => theme.colors.textPrimary};
  font-family: 'Orbitron', 'Inter', monospace;
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;
// F1 Racing Metric Change Indicator
const MetricChange = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, isPositive }) => (isPositive ? theme.colors.success : theme.colors.error)};
  display: flex;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.sm};
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;

  &::before {
    content: ${({ isPositive }) => (isPositive ? "'▲'" : "'▼'")};
    margin-right: ${({ theme }) => theme.spacing.xs};
    font-size: 0.8em;
  }
`;
// F1 Racing Loading Indicator
const LoadingIndicator = styled.div `
  color: var(--text-secondary);
  font-weight: bold;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;

  &::after {
    content: '...';
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0%,
    33% {
      content: '.';
    }
    34%,
    66% {
      content: '..';
    }
    67%,
    100% {
      content: '...';
    }
  }
`;
/**
 * F1 Racing MetricsPanel Component
 *
 * Displays key trading metrics in F1-inspired performance cards
 */
export const MetricsPanel = ({ metrics, isLoading = false }) => {
    if (isLoading) {
        return (_jsx(Container, { children: [1, 2, 3, 4].map((i) => (_jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "LOADING DATA" }), _jsx(LoadingIndicator, { children: "FETCHING TELEMETRY" })] }, i))) }));
    }
    return (_jsx(Container, { children: metrics.map((metric, index) => (_jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: metric.title }), _jsx(MetricValue, { children: metric.value }), metric.change !== undefined && (_jsxs(MetricChange, { isPositive: metric.isPositive, children: [Math.abs(metric.change), "% ", metric.isPositive ? 'GAIN' : 'LOSS'] }))] }, index))) }));
};
export default MetricsPanel;
//# sourceMappingURL=MetricsPanel.js.map