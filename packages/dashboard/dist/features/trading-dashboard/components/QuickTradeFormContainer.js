import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * QuickTradeFormContainer Component
 *
 * REFACTORED: Main orchestrator for the quick trade form feature
 * Follows the proven container pattern from TradeAnalysis and TradingPlan.
 *
 * BENEFITS:
 * - Uses new F1 component library
 * - Clean separation of concerns
 * - Comprehensive error handling
 * - Auto-save functionality
 * - Performance optimized
 */
import { Suspense } from 'react';
import { F1Container, F1Form } from '@adhd-trading-dashboard/shared';
import { QuickTradeFormFields } from './QuickTradeFormFields';
import { QuickTradeFormActions } from './QuickTradeFormActions';
import { useQuickTradeForm } from '../hooks/useQuickTradeForm';
/**
 * Loading Fallback Component
 */
const LoadingFallback = () => (_jsxs("div", { style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '300px',
        gap: '16px'
    }, children: [_jsx("div", { style: {
                width: '32px',
                height: '32px',
                border: '3px solid var(--border-primary)',
                borderTop: '3px solid var(--primary-color)',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
            } }), _jsx("div", { style: { color: 'var(--text-secondary)' }, children: "Loading Quick Trade Form..." })] }));
/**
 * QuickTradeFormContainer Component
 *
 * Main container that orchestrates the quick trade form functionality
 * using the new F1 component library patterns.
 */
export const QuickTradeFormContainer = ({ onSubmit, initialValues = {}, className, autoSave = true, autoSaveInterval = 30000, }) => {
    const { 
    // Form fields
    dateField, symbolField, directionField, quantityField, entryPriceField, exitPriceField, 
    // Form state
    isSubmitting, error, success, 
    // Form actions
    handleSubmit, handleClear, validateForm, 
    // Auto-save
    lastSaved, } = useQuickTradeForm({
        onSubmit,
        initialValues,
        autoSave,
        autoSaveInterval,
    });
    return (_jsx(F1Container, { variant: "form", maxWidth: 600, className: className, background: "surface", children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsxs(F1Form, { title: "\uD83C\uDFCE\uFE0F Quick Trade Entry", subtitle: "Fast trade logging for active sessions", variant: "quick", showAccent: true, onSubmit: handleSubmit, isSubmitting: isSubmitting, error: error, success: success, autoSave: autoSave, autoSaveInterval: autoSaveInterval, children: [_jsx(QuickTradeFormFields, { dateField: dateField, symbolField: symbolField, directionField: directionField, quantityField: quantityField, entryPriceField: entryPriceField, exitPriceField: exitPriceField }), _jsx(QuickTradeFormActions, { onSubmit: handleSubmit, onClear: handleClear, isSubmitting: isSubmitting, canSubmit: validateForm(), lastSaved: lastSaved })] }) }) }));
};
export default QuickTradeFormContainer;
//# sourceMappingURL=QuickTradeFormContainer.js.map