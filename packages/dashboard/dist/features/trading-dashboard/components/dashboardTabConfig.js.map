{"version": 3, "file": "dashboardTabConfig.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/dashboardTabConfig.tsx"], "names": [], "mappings": ";AAgBA,OAAO,YAAY,MAAM,4BAA4B,CAAC;AACtD,OAAO,gBAAgB,MAAM,gCAAgC,CAAC;AAC9D,OAAO,iBAAiB,MAAM,iCAAiC,CAAC;AAChE,OAAO,aAAa,MAAM,6BAA6B,CAAC;AACxD,OAAO,oBAAoB,MAAM,gEAAgE,CAAC;AAClG,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAkCvC,yCAAyC;AACzC,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAG5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;;uBAE5B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,IAAI,QAAQ;;;CAGtE,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGvB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;CAClD,CAAC;AAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;gBACnB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,qBAAqB;mBAC1D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,IAAI,KAAK;aACpD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;sBACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,uBAAuB;;CAEnF,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAA;gBACX,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;WAC/C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;eACjD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,UAAU;;CAE9D,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAuC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;IACpF,sEAAsE;IACtE,2CAA2C;IAC3C,MAAM,YAAY,GAChB,IAAI,CAAC,iBAAiB;QACpB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;QACrF,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IAExB,OAAO,CACL,8BACE,KAAC,YAAY,IAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,SAAS,EAAE,SAAS,GAAI,EACxE,KAAC,gBAAgB,IAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,GAAI,EAChE,KAAC,iBAAiB,IAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,GAAI,IAChE,CACJ,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAuC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;IACnF,sEAAsE;IACtE,8CAA8C;IAC9C,MAAM,YAAY,GAChB,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAC1B,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAC9E,IAAI,EAAE,CAAC;IAEV,OAAO,KAAC,iBAAiB,IAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,GAAI,CAAC;AAC3E,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAuC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;IACnF,OAAO,CACL,KAAC,aAAa,IACZ,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAC3C,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,mBAAmB,GAAuC,CAAC,EAC/D,IAAI,EACJ,SAAS,EACT,eAAe,EACf,qBAAqB,GACtB,EAAE,EAAE;IACH,OAAO,CACL,MAAC,kBAAkB,eACjB,MAAC,aAAa,eACZ,KAAC,YAAY,IAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,SAAS,EAAE,SAAS,GAAI,EACxE,KAAC,gBAAgB,IAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,GAAI,EAChE,KAAC,aAAa,IACZ,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAC3C,SAAS,EAAE,SAAS,GACpB,IACY,EAEhB,MAAC,gBAAgB,eACf,KAAC,SAAS,uDAAkC,EAC3C,eAAe,IAAI,qBAAqB,IAAI,CAC3C,KAAC,oBAAoB,IACnB,UAAU,EAAE,eAAe,EAC3B,YAAY,EAAE,CACZ,CAAgF,EAChF,EAAE,CACF,qBAAqB,CAAC,CAA4D,CAAC,EAErF,gBAAgB,EAAE,EAAE,GACpB,CACH,IACgB,IACA,CACtB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAA6C;IAC5E,OAAO,EAAE;QACP,EAAE,EAAE,SAAS;QACb,KAAK,EAAE,qBAAqB;QAC5B,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,iBAAiB;QAC5B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,gBAAgB;QAC3B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,sDAAsD;QACnE,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,gBAAgB;QAC3B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,SAAS,EAAE;QACT,EAAE,EAAE,WAAW;QACf,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,gDAAgD;QAC7D,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,mBAAmB;QAC9B,YAAY,EAAE,KAAK;QACnB,YAAY,EAAE,IAAI;KACnB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAmB,EAAsB,EAAE;IACtE,OAAO,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAyB,EAAE;IACzD,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAAyB,EAAE;IAC5D,OAAO,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,GAAyB,EAAE;IAClE,OAAO,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAuC,KAAK,CAAC,EAAE;IACrF,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;IAC5B,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;IAEvC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CACL,eACE,KAAK,EAAE;gBACL,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,oBAAoB;aAC5B,qCAEe,SAAS,IACrB,CACP,CAAC;IACJ,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC;IAEtC,OAAO,CACL,cACE,EAAE,EAAE,mBAAmB,SAAS,EAAE,EAClC,IAAI,EAAC,UAAU,qBACE,iBAAiB,SAAS,EAAE,YAE7C,KAAC,YAAY,OAAK,KAAK,GAAI,GACvB,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,2BAA2B,CAAC"}