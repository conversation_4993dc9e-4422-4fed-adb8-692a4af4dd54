{"version": 3, "file": "F1DashboardTabs.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/F1DashboardTabs.tsx"], "names": [], "mappings": ";AAeA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAevC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;;YAGpB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;6BACzE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,uBAAuB;;CAE1F,CAAC;AAEF,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAA6C;aACzD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;;;WAG1F,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,CAChC,SAAS;IACP,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;IACxC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,IAAI,uBAAuB;YAClD,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;;iBAEnD,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;eAC9C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,MAAM;;;;;;;;;;;;;;;kBAezC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;wBACxD,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;aAO/C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,CAChC,SAAS;IACP,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;IACxC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,SAAS;;;;;oBAK5B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,CACrC,SAAS;IACP,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;IACjD,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,IAAI,uBAAuB;;;;;;;;;IAS9D,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAClB,SAAS;IACT;;;GAGD;;;;eAIY,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;iBACrF,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,UAAU;;CAEhE,CAAC;AAEF,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAA;kBACT,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;;;;;;;CAO1D,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAA;;;;CAI3B,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,GAA+E;IAC7F,OAAO,EAAE;QACP,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,sCAAsC;KACpD;IACD,MAAM,EAAE;QACN,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,QAAQ;QACf,WAAW,EAAE,uCAAuC;KACrD;IACD,MAAM,EAAE;QACN,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,QAAQ;QACf,WAAW,EAAE,0CAA0C;KACxD;IACD,SAAS,EAAE;QACT,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,0CAA0C;KACxD;CACF,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,eAAe,GAAmC,CAAC,EAC9D,SAAS,EACT,WAAW,EACX,QAAQ,GAAG,KAAK,EAChB,SAAS,GACV,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,CAAC,GAAiB,EAAE,EAAE;QAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAW,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAA0B,EAAE,GAAiB,EAAE,EAAE;QACtE,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9D,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,WAAW,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,aAAa,IAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAC,SAAS,YAC/C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACvD,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,SAAS,KAAK,GAAG,CAAC;YAEnC,OAAO,CACL,MAAC,GAAG,iBAES,QAAQ,eACR,QAAQ,EACnB,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,EAClC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,EACvC,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAC,KAAK,mBACK,QAAQ,mBACR,mBAAmB,GAAG,EAAE,EACvC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3B,KAAK,EAAE,MAAM,CAAC,WAAW,aAEzB,KAAC,OAAO,cAAE,MAAM,CAAC,IAAI,GAAW,EAChC,KAAC,QAAQ,cAAE,MAAM,CAAC,KAAK,GAAY,KAb9B,GAAG,CAcJ,CACP,CAAC;QACJ,CAAC,CAAC,GACY,CACjB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,eAAe,CAAC"}