import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * DashboardTabs Component
 *
 * EXTRACTED FROM: TradingDashboard.tsx (lines 122-162 + 320-333)
 * F1 racing-themed tab navigation with accessibility and persistence.
 *
 * BENEFITS:
 * - Reusable tab navigation component
 * - F1 racing aesthetic with red accents
 * - Keyboard navigation support
 * - URL synchronization capability
 * - Local storage persistence
 */
import { useEffect } from 'react';
import styled, { css } from 'styled-components';
const TabsContainer = styled.div `
  display: flex;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
  overflow-x: auto;
  
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;
const TabButton = styled.button `
  background: none;
  border: none;
  padding: ${({ theme }) => `${theme.spacing?.md || '12px'} ${theme.spacing?.lg || '16px'}`};
  font-size: ${({ theme }) => theme.fontSizes?.md || '16px'};
  font-weight: ${({ theme, $active }) => $active
    ? theme.fontWeights?.semibold || '600'
    : theme.fontWeights?.regular || '400'};
  color: ${({ theme, $active, $disabled }) => {
    if ($disabled)
        return theme.colors?.textDisabled || 'var(--text-secondary)';
    return $active
        ? theme.colors?.primary || 'var(--primary-color)'
        : theme.colors?.textSecondary || 'var(--text-secondary)';
}};
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  border-bottom: 2px solid ${({ theme, $active }) => $active
    ? theme.colors?.primary || 'var(--primary-color)'
    : 'transparent'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  position: relative;

  /* F1 racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
      opacity: 0.5;
      transition: opacity 0.2s ease;
    }
  }

  /* Focus styles for accessibility */
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  }

  /* Active state enhancement */
  ${({ $active, theme }) => $active &&
    css `
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background: ${theme.colors?.primary || 'var(--primary-color)'};
        border-radius: 50%;
      }
    `}

  /* Disabled state */
  ${({ $disabled }) => $disabled &&
    css `
      opacity: 0.5;
      pointer-events: none;
    `}
`;
const TabIcon = styled.span `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;
const TabLabel = styled.span `
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '14px'};
`;
/**
 * Default tabs configuration
 */
const defaultTabs = [
    { id: 'summary', label: 'Summary', icon: '📊' },
    { id: 'trades', label: 'Trades', icon: '📈' },
    { id: 'setups', label: 'Setups', icon: '🎯' },
    { id: 'analytics', label: 'Analytics', icon: '🔬' },
];
/**
 * DashboardTabs Component
 *
 * A reusable F1 racing-themed tab navigation component with accessibility,
 * persistence, and URL synchronization capabilities.
 *
 * @example
 * ```typescript
 * <DashboardTabs
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 *   persistState={true}
 *   syncWithUrl={true}
 * />
 * ```
 */
export const DashboardTabs = ({ activeTab, onTabChange, tabs = defaultTabs, className, persistState = true, storageKey = 'trading-dashboard-active-tab', syncWithUrl = false, }) => {
    // Load persisted tab state on mount
    useEffect(() => {
        if (!persistState)
            return;
        try {
            const savedTab = localStorage.getItem(storageKey);
            if (savedTab && tabs.some(tab => tab.id === savedTab)) {
                onTabChange(savedTab);
            }
        }
        catch (error) {
            console.warn('Failed to load persisted tab state:', error);
        }
    }, [persistState, storageKey, onTabChange, tabs]);
    // Persist tab state when it changes
    useEffect(() => {
        if (!persistState)
            return;
        try {
            localStorage.setItem(storageKey, activeTab);
        }
        catch (error) {
            console.warn('Failed to persist tab state:', error);
        }
    }, [activeTab, persistState, storageKey]);
    // URL synchronization (basic implementation)
    useEffect(() => {
        if (!syncWithUrl)
            return;
        const url = new URL(window.location.href);
        url.searchParams.set('tab', activeTab);
        window.history.replaceState({}, '', url.toString());
    }, [activeTab, syncWithUrl]);
    /**
     * Handle tab click with validation
     */
    const handleTabClick = (tabId) => {
        const tab = tabs.find(t => t.id === tabId);
        if (tab && !tab.disabled) {
            onTabChange(tabId);
        }
    };
    /**
     * Handle keyboard navigation
     */
    const handleKeyDown = (event, tabId) => {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            handleTabClick(tabId);
        }
        else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
            event.preventDefault();
            const currentIndex = tabs.findIndex(tab => tab.id === activeTab);
            const direction = event.key === 'ArrowLeft' ? -1 : 1;
            let nextIndex = currentIndex + direction;
            // Wrap around
            if (nextIndex < 0)
                nextIndex = tabs.length - 1;
            if (nextIndex >= tabs.length)
                nextIndex = 0;
            // Skip disabled tabs
            while (tabs[nextIndex]?.disabled && nextIndex !== currentIndex) {
                nextIndex += direction;
                if (nextIndex < 0)
                    nextIndex = tabs.length - 1;
                if (nextIndex >= tabs.length)
                    nextIndex = 0;
            }
            if (!tabs[nextIndex]?.disabled) {
                handleTabClick(tabs[nextIndex].id);
            }
        }
    };
    return (_jsx(TabsContainer, { className: className, role: "tablist", children: tabs.map((tab) => (_jsxs(TabButton, { "$active": activeTab === tab.id, "$disabled": tab.disabled || false, onClick: () => handleTabClick(tab.id), onKeyDown: (e) => handleKeyDown(e, tab.id), role: "tab", "aria-selected": activeTab === tab.id, "aria-controls": `tabpanel-${tab.id}`, tabIndex: activeTab === tab.id ? 0 : -1, disabled: tab.disabled, "aria-label": `${tab.label} tab`, children: [tab.icon && _jsx(TabIcon, { children: tab.icon }), _jsx(TabLabel, { children: tab.label })] }, tab.id))) }));
};
export default DashboardTabs;
//# sourceMappingURL=DashboardTabs.js.map