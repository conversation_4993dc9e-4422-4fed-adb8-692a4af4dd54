{"version": 3, "file": "DashboardTabs.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/components/DashboardTabs.tsx"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;GAYG;AAEH,OAAc,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACzC,OAAO,MAAM,EAAE,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AA4BhD,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAA;;6BAEH,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,uBAAuB;mBACxE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM;;;;;;;;;CAS5D,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAA0C;;;aAG5D,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE;eAC5E,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,MAAM;iBAC1C,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CACpC,OAAO;IACL,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK;IACtC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,IAAI,KACpC;WACS,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE;IACzC,IAAI,SAAS;QAAE,OAAO,KAAK,CAAC,MAAM,EAAE,YAAY,IAAI,uBAAuB,CAAC;IAC5E,OAAO,OAAO;QACZ,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;QACjD,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,IAAI,uBAAuB,CAAC;AAC7D,CAAC;YACS,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;6BACvC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAChD,OAAO;IACL,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;IACjD,CAAC,CAAC,aACN;gBACc,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,eAAe;;;;SAIhE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,KAAK;;;;;aAKrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;;;;;;;oBASvD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;;;;;;;4BAStD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;qBACrE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,IAAI,KAAK;;;;IAI/D,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,CACvB,OAAO;IACP,GAAG,CAAA;;;;;;;;;sBASe,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,sBAAsB;;;KAGhE;;;IAGD,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAClB,SAAS;IACT,GAAG,CAAA;;;KAGF;CACJ,CAAC;AAEF,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAA;eACZ,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,MAAM;CAC1D,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAA;;;eAGb,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,MAAM;CAC1D,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,GAAU;IACzB,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE;IAC/C,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;IAC7C,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;IAC7C,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;CACpD,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAC1D,SAAS,EACT,WAAW,EACX,IAAI,GAAG,WAAW,EAClB,SAAS,EACT,YAAY,GAAG,IAAI,EACnB,UAAU,GAAG,8BAA8B,EAC3C,WAAW,GAAG,KAAK,GACpB,EAAE,EAAE;IACH,oCAAoC;IACpC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAClD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACtD,WAAW,CAAC,QAAmB,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;IAElD,oCAAoC;IACpC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;IAE1C,6CAA6C;IAC7C,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1C,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACvC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtD,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;IAE7B;;OAEG;IACH,MAAM,cAAc,GAAG,CAAC,KAAc,EAAE,EAAE;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;QAC3C,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACzB,WAAW,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC,CAAC;IAEF;;OAEG;IACH,MAAM,aAAa,GAAG,CAAC,KAA0B,EAAE,KAAc,EAAE,EAAE;QACnE,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YAC/C,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,cAAc,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;aAAM,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,YAAY,EAAE,CAAC;YACnE,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;YACjE,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,IAAI,SAAS,GAAG,YAAY,GAAG,SAAS,CAAC;YAEzC,cAAc;YACd,IAAI,SAAS,GAAG,CAAC;gBAAE,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAC/C,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM;gBAAE,SAAS,GAAG,CAAC,CAAC;YAE5C,qBAAqB;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;gBAC/D,SAAS,IAAI,SAAS,CAAC;gBACvB,IAAI,SAAS,GAAG,CAAC;oBAAE,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC/C,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM;oBAAE,SAAS,GAAG,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,CAAC;gBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,aAAa,IAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAC,SAAS,YAChD,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CACjB,MAAC,SAAS,eAEC,SAAS,KAAK,GAAG,CAAC,EAAE,eAClB,GAAG,CAAC,QAAQ,IAAI,KAAK,EAChC,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EACrC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAC1C,IAAI,EAAC,KAAK,mBACK,SAAS,KAAK,GAAG,CAAC,EAAE,mBACpB,YAAY,GAAG,CAAC,EAAE,EAAE,EACnC,QAAQ,EAAE,SAAS,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACvC,QAAQ,EAAE,GAAG,CAAC,QAAQ,gBACV,GAAG,GAAG,CAAC,KAAK,MAAM,aAE7B,GAAG,CAAC,IAAI,IAAI,KAAC,OAAO,cAAE,GAAG,CAAC,IAAI,GAAW,EAC1C,KAAC,QAAQ,cAAE,GAAG,CAAC,KAAK,GAAY,KAb3B,GAAG,CAAC,EAAE,CAcD,CACb,CAAC,GACY,CACjB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,aAAa,CAAC"}