import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Recent Trades Table Component
 *
 * Displays a table of recent trades
 */
import React, { useState, useMemo, useCallback } from 'react';
import styled from 'styled-components';
const TableContainer = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  overflow-x: auto;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const TableTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;
const TableHead = styled.thead `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const TableHeader = styled.th `
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};
  user-select: none;
  position: relative;

  ${({ sortable, theme }) => sortable &&
    `
    &:hover {
      color: ${theme.colors.textPrimary};
      background-color: rgba(255, 255, 255, 0.05);
    }
  `}

  ${({ active, theme }) => active &&
    `
    color: ${theme.colors.primary};
    font-weight: 600;
  `}
`;
const SortIcon = styled.span `
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.7;

  ${({ direction }) => {
    if (direction === 'asc')
        return 'content: "↑";';
    if (direction === 'desc')
        return 'content: "↓";';
    return 'content: "↕"; opacity: 0.3;';
}}

  &::after {
    ${({ direction }) => {
    if (direction === 'asc')
        return 'content: "↑";';
    if (direction === 'desc')
        return 'content: "↓";';
    return 'content: "↕";';
}}
  }
`;
const TableRow = styled.tr `
  border-bottom: 1px solid var(--border-primary);

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;
const DirectionCell = styled(TableCell) `
  color: ${({ theme, direction }) => direction === 'Long' ? theme.colors.success : theme.colors.error};
`;
const ResultCell = styled(TableCell) `
  color: ${({ theme, win }) => (win ? theme.colors.success : theme.colors.error)};
`;
const PnlCell = styled(TableCell) `
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};
`;
const LoadingContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const NoDataContainer = styled.div `
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * Debug function for comprehensive trade sorting analysis
 */
const debugTradesSorting = (trades, sortBy, sortOrder) => {
    console.group('🔍 Recent Trades Sorting Debug');
    // Log raw data
    console.log('📊 Raw trades count:', trades.length);
    console.log('📊 Sort criteria:', { sortBy, sortOrder });
    // Log first few trades to understand structure
    console.log('📋 Sample raw trades (first 3):');
    trades.slice(0, 3).forEach((trade, index) => {
        // Add null checks to prevent runtime errors
        if (!trade || !trade.trade) {
            console.log(`Trade ${index + 1}: INVALID - trade or trade.trade is null/undefined`);
            return;
        }
        console.log(`Trade ${index + 1}:`, {
            id: trade.trade.id,
            market: trade.trade.market,
            date: trade.trade.date,
            direction: trade.trade.direction,
            entry: trade.trade.entry_price,
            exit: trade.trade.exit_price,
            // Log the field we're sorting by
            [sortBy]: trade.trade[sortBy],
        });
    });
    console.groupEnd();
};
/**
 * RecentTradesTable Component
 *
 * Displays a table of recent trades with sorting functionality
 */
export const RecentTradesTable = ({ trades, isLoading = false, }) => {
    // IMMEDIATE DEBUG: Log that the enhanced component is being used
    console.log('🔥 ENHANCED RecentTradesTable component is being executed!');
    const [sortField, setSortField] = useState('date');
    const [sortDirection, setSortDirection] = useState('desc');
    // Debug component mounting and data
    React.useEffect(() => {
        console.log('🚀 RecentTradesTable component mounted/updated');
        console.log('📊 Props received:', {
            tradesCount: trades?.length || 0,
            isLoading,
            tradesType: typeof trades,
            tradesIsArray: Array.isArray(trades),
            tradesValue: trades, // Log the actual trades value
        });
        if (trades && trades.length > 0) {
            // Check if first trade is valid before accessing properties
            const firstTrade = trades[0];
            const lastTrade = trades[trades.length - 1];
            if (firstTrade && firstTrade.trade) {
                console.log('🎯 RecentTradesTable received trades:', {
                    count: trades.length,
                    firstTrade: {
                        id: firstTrade.trade.id,
                        date: firstTrade.trade.date,
                        market: firstTrade.trade.market,
                        // Check for missing properties that cause TypeScript errors
                        setup: firstTrade.trade.setup,
                        session: firstTrade.trade.session,
                    },
                    lastTrade: lastTrade && lastTrade.trade
                        ? {
                            id: lastTrade.trade.id,
                            date: lastTrade.trade.date,
                            market: lastTrade.trade.market,
                        }
                        : 'Invalid last trade',
                });
            }
            else {
                console.log('⚠️ RecentTradesTable: Trades array exists but contains invalid data');
                console.log('🔍 First trade:', firstTrade);
            }
        }
        else {
            console.log('⚠️ RecentTradesTable: No trades data or empty array');
            console.log('🔍 Trades value:', trades);
            console.log('🔍 Is loading:', isLoading);
        }
    }, [trades, isLoading]);
    // Handle column sorting
    const handleSort = useCallback((field) => {
        console.log(`🔄 Sorting by ${field}`);
        if (sortField === field) {
            // Toggle direction if same field
            const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            setSortDirection(newDirection);
            console.log(`🔄 Toggled direction to ${newDirection}`);
        }
        else {
            // Set new field and default direction
            setSortField(field);
            setSortDirection('desc');
            console.log(`🔄 New field ${field} with desc direction`);
        }
    }, [sortField, sortDirection]);
    // Sort trades with comprehensive debugging
    const sortedTrades = useMemo(() => {
        if (!trades || trades.length === 0)
            return [];
        // Filter out any invalid trades before processing
        const validTrades = trades.filter((trade) => trade && trade.trade);
        if (validTrades.length === 0) {
            console.warn('⚠️ No valid trades found after filtering');
            return [];
        }
        debugTradesSorting(validTrades, sortField, sortDirection);
        const sorted = [...validTrades].sort((a, b) => {
            // Additional safety checks
            if (!a || !a.trade || !b || !b.trade) {
                console.warn('⚠️ Invalid trade data in sort function');
                return 0;
            }
            let aValue, bValue;
            // Extract values based on sortField
            switch (sortField) {
                case 'date':
                    aValue = new Date(a.trade.date || '');
                    bValue = new Date(b.trade.date || '');
                    break;
                case 'setup':
                    aValue = a.trade.setup || '';
                    bValue = b.trade.setup || '';
                    break;
                case 'session':
                    aValue = a.trade.session || '';
                    bValue = b.trade.session || '';
                    break;
                case 'direction':
                    aValue = a.trade.direction?.toLowerCase() || '';
                    bValue = b.trade.direction?.toLowerCase() || '';
                    break;
                case 'market':
                    aValue = a.trade.market || '';
                    bValue = b.trade.market || '';
                    break;
                case 'entry':
                    aValue = parseFloat(String(a.trade.entry_price || 0));
                    bValue = parseFloat(String(b.trade.entry_price || 0));
                    break;
                case 'exit':
                    aValue = parseFloat(String(a.trade.exit_price || 0));
                    bValue = parseFloat(String(b.trade.exit_price || 0));
                    break;
                case 'rMultiple':
                    aValue = parseFloat(String(a.trade.r_multiple || 0));
                    bValue = parseFloat(String(b.trade.r_multiple || 0));
                    break;
                case 'pnl':
                    aValue = parseFloat(String(a.trade.achieved_pl || 0));
                    bValue = parseFloat(String(b.trade.achieved_pl || 0));
                    break;
                default:
                    aValue = a.trade[sortField];
                    bValue = b.trade[sortField];
            }
            console.log(`🔄 Comparing: ${aValue} vs ${bValue} (${sortField})`);
            // Handle different data types
            if (aValue instanceof Date && bValue instanceof Date) {
                const comparison = aValue.getTime() - bValue.getTime();
                return sortDirection === 'asc' ? comparison : -comparison;
            }
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                const comparison = aValue - bValue;
                return sortDirection === 'asc' ? comparison : -comparison;
            }
            // String comparison
            const comparison = String(aValue).localeCompare(String(bValue));
            return sortDirection === 'asc' ? comparison : -comparison;
        });
        // Log sorted results
        console.log('✅ Sorted trades (first 3):');
        sorted.slice(0, 3).forEach((trade, index) => {
            if (trade && trade.trade) {
                console.log(`Sorted ${index + 1}:`, {
                    id: trade.trade.id,
                    date: trade.trade.date,
                    [sortField]: trade.trade[sortField],
                });
            }
            else {
                console.log(`Sorted ${index + 1}: INVALID TRADE`);
            }
        });
        return sorted;
    }, [trades, sortField, sortDirection]);
    if (isLoading) {
        return (_jsxs(TableContainer, { children: [_jsx(TableTitle, { children: "Recent Trades" }), _jsx(LoadingContainer, { children: "Loading trades data..." })] }));
    }
    if (!trades || trades.length === 0) {
        return (_jsxs(TableContainer, { children: [_jsx(TableTitle, { children: "Recent Trades" }), _jsx(NoDataContainer, { children: "No trades data available" })] }));
    }
    return (_jsxs(TableContainer, { children: [_jsxs(TableTitle, { children: ["Recent Trades (", sortedTrades.length, ")"] }), _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs("tr", { children: [_jsxs(TableHeader, { sortable: true, active: sortField === 'date', onClick: () => handleSort('date'), children: ["Date", _jsx(SortIcon, { direction: sortField === 'date' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'setup', onClick: () => handleSort('setup'), children: ["Setup", _jsx(SortIcon, { direction: sortField === 'setup' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'session', onClick: () => handleSort('session'), children: ["Session", _jsx(SortIcon, { direction: sortField === 'session' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'direction', onClick: () => handleSort('direction'), children: ["Direction", _jsx(SortIcon, { direction: sortField === 'direction' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'market', onClick: () => handleSort('market'), children: ["Market", _jsx(SortIcon, { direction: sortField === 'market' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'entry', onClick: () => handleSort('entry'), children: ["Entry", _jsx(SortIcon, { direction: sortField === 'entry' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'exit', onClick: () => handleSort('exit'), children: ["Exit", _jsx(SortIcon, { direction: sortField === 'exit' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'rMultiple', onClick: () => handleSort('rMultiple'), children: ["R-Multiple", _jsx(SortIcon, { direction: sortField === 'rMultiple' ? sortDirection : undefined })] }), _jsxs(TableHeader, { sortable: true, active: sortField === 'pnl', onClick: () => handleSort('pnl'), children: ["P&L", _jsx(SortIcon, { direction: sortField === 'pnl' ? sortDirection : undefined })] })] }) }), _jsx("tbody", { children: sortedTrades.map((trade, index) => {
                            // Additional safety check for rendering
                            if (!trade || !trade.trade) {
                                console.warn(`⚠️ Skipping invalid trade at index ${index}`);
                                return null;
                            }
                            return (_jsxs(TableRow, { children: [_jsx(TableCell, { children: trade.trade.date || 'N/A' }), _jsx(TableCell, { children: trade.trade.setup || 'N/A' }), _jsx(TableCell, { children: trade.trade.session || 'N/A' }), _jsx(DirectionCell, { direction: trade.trade.direction, children: trade.trade.direction || 'N/A' }), _jsx(TableCell, { children: trade.trade.market || 'N/A' }), _jsx(TableCell, { children: trade.trade.entry_price || 'N/A' }), _jsx(TableCell, { children: trade.trade.exit_price || 'N/A' }), _jsx(ResultCell, { win: trade.trade.win_loss === 'Win', children: (trade.trade.r_multiple || 0).toFixed(2) }), _jsxs(PnlCell, { value: trade.trade.achieved_pl || 0, children: ["$", (trade.trade.achieved_pl || 0).toFixed(2)] })] }, trade.trade.id || `trade-${index}`));
                        }) })] })] }));
};
export default RecentTradesTable;
//# sourceMappingURL=RecentTradesTable.js.map