{"version": 3, "file": "RecentTradesPanel.js", "sourceRoot": "", "sources": ["../../../../src/features/performance-dashboard/components/RecentTradesPanel.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAQvC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAA;;CAE3B,CAAC;AAEF,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;;;CAGzB,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAA;6BACD,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;CAC9D,CAAC;AAEF,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAA;;wBAEF,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;;;;wBAItC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;;CAExD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAA;;aAEhB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;eAC7B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAA;aACd,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;eAC7B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;CAC/C,CAAC;AAEF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAiC;WAC7D,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,CAChC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;CACpE,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAA0C;WACnE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE;IAC7B,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,KAAK;YACR,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;QAC9B,KAAK,MAAM;YACT,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAC7B;YACE,OAAO,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;IACtC,CAAC;AACH,CAAC;CACF,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAmB;WAC5C,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAC5B,KAAK,GAAG,CAAC;IACP,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;IACtB,CAAC,CAAC,KAAK,GAAG,CAAC;QACX,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;QACrB,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACjC,CAAC;AAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAA;aACtB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;;WAEjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;;CAEnD,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAqC,CAAC,EAClE,MAAM,EACN,SAAS,GAAG,KAAK,GAClB,EAAE,EAAE;IACH,uDAAuD;IACvD,OAAO,CAAC,GAAG,CACT,uCAAuC,EACvC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;QACd,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI;QAClB,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM;QACtB,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS;KAC7B,CAAC,CAAC,CACJ,CAAC;IAEF,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,KAAC,gBAAgB,2CAA4C,CAAC;IACvE,CAAC;IAED,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,OAAO,KAAC,gBAAgB,yCAA0C,CAAC;IACrE,CAAC;IAED,OAAO,CACL,KAAC,SAAS,cACR,MAAC,KAAK,eACJ,KAAC,SAAS,cACR,MAAC,QAAQ,eACP,KAAC,WAAW,uBAAmB,EAC/B,KAAC,WAAW,yBAAqB,EACjC,KAAC,WAAW,4BAAwB,EACpC,KAAC,WAAW,yBAAqB,EACjC,KAAC,WAAW,8BAA0B,IAC7B,GACD,EACZ,0BACG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;wBACtB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC;wBAC5B,MAAM,MAAM,GACV,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;wBAEtF,OAAO,CACL,MAAC,QAAQ,eACP,KAAC,SAAS,cAAE,KAAK,CAAC,IAAI,GAAa,EACnC,KAAC,SAAS,cAAE,KAAK,CAAC,MAAM,IAAI,KAAK,GAAa,EAC9C,KAAC,aAAa,IAAC,SAAS,EAAE,KAAK,CAAC,SAAS,YACtC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,GACpC,EAChB,KAAC,UAAU,IAAC,MAAM,EAAE,MAAM,YACvB,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GACtC,EACb,MAAC,UAAU,IAAC,KAAK,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC,kBACrC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAC1B,KAXA,KAAK,CAAC,EAAE,CAYZ,CACZ,CAAC;oBACJ,CAAC,CAAC,GACI,IACF,GACE,CACb,CAAC;AACJ,CAAC,CAAC"}