{"version": 3, "file": "useDashboardData.js", "sourceRoot": "", "sources": ["../../../../src/features/performance-dashboard/hooks/useDashboardData.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAC9C,OAAO,EAAqB,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AAYxF,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,MAAM,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAW;QACnC,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE;QACnC,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QACxC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;QACzC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE;KACxC,CAAC,CAAC;IAEH,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IACjE,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IAC1E,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IAExD,yCAAyC;IACzC,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,MAAM,IAAI,GAAqB,EAAE,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEjC,IAAI,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;aACpC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,4DAA4D;IAC5D,MAAM,iBAAiB,GAAG,GAAwB,EAAE;QAClD,MAAM,MAAM,GAAwB,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACjC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;YACnC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;YAEnE,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE;oBACL,EAAE,EAAE,CAAC,GAAG,CAAC;oBACT,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC3D,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;oBACjD,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC9D,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;oBACrE,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;oBACzC,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;oBACxC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;oBAClD,WAAW,EAAE,MAAM;oBACnB,UAAU,EAAE,MAAM,GAAG,GAAG;oBACxB,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;oBAChC,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;oBACzD,UAAU,EAAE,OAAO;oBACnB,SAAS,EAAE,OAAO;oBAClB,KAAK,EAAE,cAAc,CAAC,GAAG,CAAC,EAAE;iBAC7B;gBACD,WAAW,EAAE,SAAS;gBACtB,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;QAED,0FAA0F;QAC1F,OAAO,MAAM,CAAC,IAAI,CAChB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAC9E,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAChD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC5C,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,+DAA+D;YAC/D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,cAAc,iBAAiB,CAAC,MAAM,wBAAwB,CAAC,CAAC;YAE5E,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,CACT,gCAAgC,EAChC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CACrE,CAAC;gBAEF,iFAAiF;gBACjF,MAAM,YAAY,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC,IAAI,CAC9C,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAC9E,CAAC;gBACF,OAAO,CAAC,GAAG,CACT,uCAAuC,EACvC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAChE,CAAC;gBAEF,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClD,OAAO,CAAC,GAAG,CACT,gCAAgC,EAChC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CACpE,CAAC;gBAEF,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBAClC,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBAClC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBACjE,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBAClC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACvC,CAAC;YAED,sDAAsD;YACtD,kEAAkE;YAClE,uDAAuD;QACzD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;YACvD,QAAQ,CAAC,+BAA+B,CAAC,CAAC;YAE1C,2CAA2C;YAC3C,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC1D,YAAY,CAAC,iBAAiB,EAAE,CAAC,CAAC;YAClC,eAAe,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACvC,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,OAAO;QACP,SAAS;QACT,YAAY;QACZ,SAAS;QACT,KAAK;QACL,kBAAkB;KACnB,CAAC;AACJ,CAAC,CAAC"}