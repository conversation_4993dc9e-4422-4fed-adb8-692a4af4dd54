{"version": 3, "file": "patternQuality.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-journal/constants/patternQuality.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,SAAS,EAAE,CAAC;IACZ,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,YAAY,EAAE,CAAC;CAChB,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,eAAe,EAAE;IAC9C,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;IACpC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE;IAC1C,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;IACpC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAE;CACrD,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG;IACtC,OAAO,EAAE;QACP,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,4CAA4C;QACzD,SAAS,EAAE,mDAAmD;QAC9D,IAAI,EAAE,2CAA2C;QACjD,OAAO,EAAE,gDAAgD;QACzD,IAAI,EAAE,8DAA8D;QACpE,YAAY,EAAE,wDAAwD;KACvE;IACD,UAAU,EAAE;QACV,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,sDAAsD;QACnE,SAAS,EAAE,iDAAiD;QAC5D,IAAI,EAAE,+CAA+C;QACrD,OAAO,EAAE,uCAAuC;QAChD,IAAI,EAAE,uCAAuC;QAC7C,YAAY,EAAE,uBAAuB;KACtC;IACD,OAAO,EAAE;QACP,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,kEAAkE;QAC/E,SAAS,EAAE,wDAAwD;QACnE,IAAI,EAAE,qDAAqD;QAC3D,OAAO,EAAE,sDAAsD;QAC/D,IAAI,EAAE,mDAAmD;QACzD,YAAY,EAAE,gDAAgD;KAC/D;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,cAAc;QACrB,WAAW,EAAE,8CAA8C;QAC3D,SAAS,EAAE,iDAAiD;QAC5D,IAAI,EAAE,0CAA0C;QAChD,OAAO,EAAE,mDAAmD;QAC5D,IAAI,EAAE,oCAAoC;QAC1C,YAAY,EAAE,+CAA+C;KAC9D;IACD,MAAM,EAAE;QACN,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE,gDAAgD;QAC7D,SAAS,EAAE,oCAAoC;QAC/C,IAAI,EAAE,gCAAgC;QACtC,OAAO,EAAE,oCAAoC;QAC7C,IAAI,EAAE,iCAAiC;QACvC,YAAY,EAAE,6BAA6B;KAC5C;IACD,SAAS,EAAE;QACT,KAAK,EAAE,qBAAqB;QAC5B,WAAW,EAAE,6DAA6D;QAC1E,SAAS,EAAE,iDAAiD;QAC5D,IAAI,EAAE,uCAAuC;QAC7C,OAAO,EAAE,+DAA+D;QACxE,IAAI,EAAE,qCAAqC;QAC3C,YAAY,EAAE,0EAA0E;KACzF;IACD,MAAM,EAAE;QACN,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,sCAAsC;QACnD,SAAS,EAAE,uCAAuC;QAClD,IAAI,EAAE,uCAAuC;QAC7C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE,yCAAyC;QAC/C,YAAY,EAAE,yCAAyC;KACxD;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,QAAoC,EAAU,EAAE;IAClF,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACrD,OAAO,KAAK,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAU,EAAE;IACjE,uDAAuD;IACvD,0BAA0B;IAC1B,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;IAEhE,oCAAoC;IACpC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,MAAc,EAAU,EAAE;IAC7D,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,aAAa,CAAC;IACtC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,MAAM,CAAC;IAC/B,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC;IAClC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,eAAe,CAAC;IACxC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,MAAM,CAAC;IAC/B,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,MAAc,EAAU,EAAE;IACvD,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,sBAAsB,CAAC,CAAC,QAAQ;IACxD,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,cAAc;IACjD,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,QAAQ;IAC3C,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,SAAS;IAC5C,OAAO,oBAAoB,CAAC,CAAC,MAAM;AACrC,CAAC,CAAC"}