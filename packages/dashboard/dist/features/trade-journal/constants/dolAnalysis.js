/**
 * DOL (Draw on Liquidity) Analysis Constants
 *
 * Constants for the DOL analysis feature
 */
// Removed unused imports - will be added back when needed for real data integration
/**
 * DOL Type Options
 */
export const DOL_TYPE_OPTIONS = [
    { value: 'Sweep', label: 'Sweep - Price moves through the liquidity level' },
    { value: 'Tap', label: 'Tap - Price touches the liquidity level exactly' },
    { value: 'Approach', label: "Approach - Price approaches but doesn't quite reach the level" },
    { value: 'Rejection', label: 'Rejection - Price rejects from the liquidity level' },
];
/**
 * DOL Strength Options
 */
export const DOL_STRENGTH_OPTIONS = [
    { value: 'Strong', label: 'Strong - Significant price movement with high volume' },
    { value: 'Moderate', label: 'Moderate - Noticeable price movement with average volume' },
    { value: 'Weak', label: 'Weak - Minimal price movement with low volume' },
];
/**
 * DOL Reaction Options
 */
export const DOL_REACTION_OPTIONS = [
    {
        value: 'Immediate Reversal',
        label: 'Immediate Reversal - Price reverses direction immediately',
    },
    {
        value: 'Delayed Reversal',
        label: 'Delayed Reversal - Price reverses after some consolidation',
    },
    { value: 'Consolidation', label: 'Consolidation - Price consolidates at the level' },
    { value: 'Continuation', label: 'Continuation - Price continues in the same direction' },
];
/**
 * DOL Context Options
 */
export const DOL_CONTEXT_OPTIONS = [
    { value: 'High Volume Node', label: 'High Volume Node' },
    { value: 'Low Volume Node', label: 'Low Volume Node' },
    { value: 'VPOC', label: 'Volume Point of Control (VPOC)' },
    { value: 'VAH/VAL', label: 'Value Area High/Low' },
    { value: 'Previous Day High/Low', label: 'Previous Day High/Low' },
    { value: 'Previous Week High/Low', label: 'Previous Week High/Low' },
    { value: 'Previous Month High/Low', label: 'Previous Month High/Low' },
    { value: 'Round Number', label: 'Round Number (00, 50, etc.)' },
    { value: 'Technical Level', label: 'Technical Level (Support/Resistance)' },
    { value: 'News Event', label: 'News Event' },
    { value: 'Other', label: 'Other' },
];
/**
 * DOL Effectiveness Rating Options
 */
export const DOL_EFFECTIVENESS_OPTIONS = Array.from({ length: 10 }, (_, i) => ({
    value: String(i + 1),
    label: String(i + 1),
}));
/**
 * DOL Price Action Descriptions
 */
export const DOL_PRICE_ACTION_DESCRIPTIONS = {
    Sweep: 'Describe how price moved through the liquidity level. Was it a clean sweep or did it struggle?',
    Tap: 'Describe how price interacted with the liquidity level. Was it a precise tap or did it linger?',
    Approach: 'Describe how price approached the liquidity level. How close did it get and why did it stop?',
    Rejection: 'Describe how price rejected from the liquidity level. Was it a sharp rejection or gradual?',
};
/**
 * DOL Volume Profile Descriptions
 */
export const DOL_VOLUME_PROFILE_DESCRIPTIONS = {
    Strong: 'Describe the volume profile during the liquidity interaction. Was there a volume spike?',
    Moderate: 'Describe the volume profile during the liquidity interaction. Was volume consistent?',
    Weak: 'Describe the volume profile during the liquidity interaction. Why was volume low?',
};
/**
 * DOL Time of Day Significance
 */
export const DOL_TIME_OF_DAY_DESCRIPTION = 'Describe the significance of the time of day for this liquidity interaction. ' +
    'Was it during a key market session or near a session transition?';
/**
 * DOL Market Structure Description
 */
export const DOL_MARKET_STRUCTURE_DESCRIPTION = 'Describe the market structure context for this liquidity interaction. ' +
    'Was price in an uptrend, downtrend, or range? Were there any key levels nearby?';
/**
 * Get description for DOL type
 */
export const getDOLTypeDescription = (dolType) => {
    const option = DOL_TYPE_OPTIONS.find((option) => option.value === dolType);
    return option ? option.label.split(' - ')[1] : '';
};
/**
 * Get description for DOL strength
 */
export const getDOLStrengthDescription = (dolStrength) => {
    const option = DOL_STRENGTH_OPTIONS.find((option) => option.value === dolStrength);
    return option ? option.label.split(' - ')[1] : '';
};
/**
 * Get description for DOL reaction
 */
export const getDOLReactionDescription = (dolReaction) => {
    const option = DOL_REACTION_OPTIONS.find((option) => option.value === dolReaction);
    return option ? option.label.split(' - ')[1] : '';
};
/**
 * Get color for DOL effectiveness rating
 */
export const getDOLEffectivenessColor = (rating) => {
    if (rating >= 8)
        return 'var(--success-color)'; // Green
    if (rating >= 6)
        return '#8BC34A'; // Light Green
    if (rating >= 5)
        return '#FFC107'; // Amber
    if (rating >= 3)
        return '#FF9800'; // Orange
    return 'var(--error-color)'; // Red
};
/**
 * Get description for DOL effectiveness rating
 */
export const getDOLEffectivenessDescription = (rating) => {
    if (rating >= 9)
        return 'Exceptional';
    if (rating >= 8)
        return 'Excellent';
    if (rating >= 7)
        return 'Very Good';
    if (rating >= 6)
        return 'Good';
    if (rating >= 5)
        return 'Average';
    if (rating >= 4)
        return 'Below Average';
    if (rating >= 3)
        return 'Poor';
    if (rating >= 2)
        return 'Very Poor';
    return 'Ineffective';
};
//# sourceMappingURL=dolAnalysis.js.map