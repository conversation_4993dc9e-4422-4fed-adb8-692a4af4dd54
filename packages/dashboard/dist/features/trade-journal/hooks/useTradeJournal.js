/**
 * useTradeJournal Hook
 *
 * Custom hook for managing trade journal data and operations.
 */
import { useState, useEffect } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';
/**
 * useTradeJournal Hook
 *
 * Manages the state and operations for the trade journal feature.
 */
export const useTradeJournal = () => {
    const [state, setState] = useState({
        trades: [],
        isLoading: true,
        error: null,
    });
    useEffect(() => {
        const fetchTrades = async () => {
            try {
                // Set loading state
                setState(prev => ({
                    ...prev,
                    isLoading: true,
                    error: null,
                }));
                // Fetch trades from IndexedDB
                const trades = await tradeStorageService.getAllTrades();
                // Sort trades by date (newest first)
                const sortedTrades = [...trades].sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime());
                setState({
                    trades: sortedTrades, // Type assertion for interface compatibility
                    isLoading: false,
                    error: null,
                });
            }
            catch (error) {
                console.error('Error fetching trades:', error);
                setState(prev => ({
                    ...prev,
                    isLoading: false,
                    error: 'Failed to load trades. Please try again.',
                }));
            }
        };
        fetchTrades();
    }, []);
    // Add function to refresh trades
    const refreshTrades = async () => {
        try {
            setState(prev => ({
                ...prev,
                isLoading: true,
                error: null,
            }));
            const trades = await tradeStorageService.getAllTrades();
            // Sort trades by date (newest first)
            const sortedTrades = [...trades].sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime());
            setState({
                trades: sortedTrades, // Type assertion for interface compatibility
                isLoading: false,
                error: null,
            });
        }
        catch (error) {
            console.error('Error refreshing trades:', error);
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: 'Failed to refresh trades. Please try again.',
            }));
        }
    };
    return {
        ...state,
        refreshTrades,
    };
};
export default useTradeJournal;
//# sourceMappingURL=useTradeJournal.js.map