import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const FilterGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  min-width: 150px;
`;
const FilterLabel = styled.label `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
  cursor: pointer;
  
  /* F1 Racing accent */
  &::before {
    content: '🏎️';
    font-size: 10px;
    margin-right: ${({ theme }) => theme.spacing?.xs || '4px'};
    opacity: 0.7;
  }
`;
const FilterInput = styled.input `
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasValue }) => $hasValue
    ? theme.colors?.primary || 'var(--primary-color)'
    : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    transform: translateY(-1px);
  }
  
  &:hover:not(:disabled) {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
    font-style: italic;
  }
`;
const FilterSelect = styled.select `
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasValue }) => $hasValue
    ? theme.colors?.primary || 'var(--primary-color)'
    : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    transform: translateY(-1px);
  }
  
  &:hover:not(:disabled) {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Style the dropdown arrow */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 32px;
  appearance: none;
`;
const FilterOption = styled.option `
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
/**
 * F1FilterField Component
 *
 * PATTERN: F1 Filter Field Pattern
 * - Racing-inspired styling with red accents
 * - Visual feedback for active filters
 * - Consistent form controls across field types
 * - Accessible with proper labels and focus
 * - Responsive design for mobile
 */
export const F1FilterField = ({ name, label, type, value, onChange, options = [], placeholder, disabled = false, className, }) => {
    const fieldId = `filter-field-${name}`;
    const hasValue = value !== '' && value !== null && value !== undefined;
    const handleChange = (newValue) => {
        if (!disabled) {
            onChange(name, newValue);
        }
    };
    const renderControl = () => {
        switch (type) {
            case 'text':
                return (_jsx(FilterInput, { id: fieldId, type: "text", value: value || '', onChange: (e) => handleChange(e.target.value), placeholder: placeholder, disabled: disabled, "$hasValue": hasValue }));
            case 'number':
                return (_jsx(FilterInput, { id: fieldId, type: "number", value: value || '', onChange: (e) => handleChange(parseFloat(e.target.value) || 0), placeholder: placeholder, disabled: disabled, "$hasValue": hasValue }));
            case 'date':
                return (_jsx(FilterInput, { id: fieldId, type: "date", value: value || '', onChange: (e) => handleChange(e.target.value), disabled: disabled, "$hasValue": hasValue }));
            case 'select':
                return (_jsxs(FilterSelect, { id: fieldId, value: value || '', onChange: (e) => handleChange(e.target.value), disabled: disabled, "$hasValue": hasValue, children: [_jsx(FilterOption, { value: "", children: "All" }), options.map((option) => (_jsx(FilterOption, { value: option.value, children: option.label }, option.value)))] }));
            default:
                return null;
        }
    };
    return (_jsxs(FilterGroup, { className: className, children: [_jsx(FilterLabel, { htmlFor: fieldId, children: label }), renderControl()] }));
};
export default F1FilterField;
//# sourceMappingURL=F1FilterField.js.map