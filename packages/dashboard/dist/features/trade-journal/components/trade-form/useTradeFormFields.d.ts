/**
 * useTradeFormFields Hook
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Enhanced hook for managing trade form fields with validation and calculations.
 *
 * BENEFITS:
 * - Focused responsibility (field management only)
 * - Built-in validation with field-specific rules
 * - Automatic profit/loss calculations
 * - Type-safe field handling
 * - Reusable across different trading forms
 */
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
export interface UseTradeFormFieldsProps {
    /** Current form values */
    formValues: TradeFormValues;
    /** Form values setter */
    setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
    /** Validation errors */
    validationErrors: ValidationErrors;
    /** Validation errors setter */
    setValidationErrors: React.Dispatch<React.SetStateAction<ValidationErrors>>;
    /** Optional profit calculation callback */
    calculateProfitLoss?: () => void;
}
export interface UseTradeFormFieldsReturn {
    /** Standard change handler */
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    /** Price change handler (triggers calculations) */
    handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    /** Validate specific field */
    validateField: (name: string, value: any) => string | null;
    /** Validate all fields */
    validateAllFields: () => boolean;
    /** Calculate profit/loss from current values */
    calculateProfitFromValues: () => number;
    /** Check if field affects calculations */
    isCalculationField: (fieldName: string) => boolean;
}
/**
 * useTradeFormFields Hook
 *
 * Enhanced form field management with validation and calculations.
 */
export declare const useTradeFormFields: ({ formValues, setFormValues, setValidationErrors, calculateProfitLoss, }: UseTradeFormFieldsProps) => UseTradeFormFieldsReturn;
export default useTradeFormFields;
//# sourceMappingURL=useTradeFormFields.d.ts.map