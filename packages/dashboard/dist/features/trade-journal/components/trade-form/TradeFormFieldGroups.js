import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { F1TradeFormField } from './F1TradeFormField';
import { FIELD_GROUPS, getFieldsByGroup } from './tradeFormFieldConfig';
const GroupsContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
`;
const FieldGroup = styled.div `
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;
const GroupHeader = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;
const GroupTitleRow = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const GroupIcon = styled.div `
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;
const GroupTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const GroupDescription = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;
const GroupContent = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const FieldsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing?.md || '12px'};
  }
`;
/**
 * TradeFormFieldGroups Component
 *
 * PATTERN: F1 Form Groups Pattern
 * - Racing-inspired section styling with icons
 * - Organized field groups with clear hierarchy
 * - Responsive grid layout for optimal UX
 * - Consistent F1 theme across all groups
 * - Accessible and keyboard navigable
 */
export const TradeFormFieldGroups = ({ formValues, handleChange, handlePriceChange, validationErrors, disabled = false, className, }) => {
    /**
     * Determine which change handler to use based on field type
     */
    const getChangeHandler = (fieldConfig) => {
        // Use price change handler for fields that affect calculations
        if (['entryPrice', 'exitPrice', 'quantity'].includes(fieldConfig.name)) {
            return (e) => {
                handlePriceChange(e);
            };
        }
        return handleChange;
    };
    return (_jsx(GroupsContainer, { className: className, children: FIELD_GROUPS.map(group => {
            const fields = getFieldsByGroup(group.key);
            if (fields.length === 0)
                return null;
            return (_jsxs(FieldGroup, { children: [_jsx(GroupHeader, { children: _jsxs(GroupTitleRow, { children: [_jsx(GroupIcon, { children: group.icon }), _jsxs("div", { children: [_jsx(GroupTitle, { children: group.title }), _jsx(GroupDescription, { children: group.description })] })] }) }), _jsx(GroupContent, { children: _jsx(FieldsGrid, { children: fields.map(fieldConfig => (_jsx(F1TradeFormField, { name: fieldConfig.name, label: fieldConfig.label, type: fieldConfig.type, value: formValues[fieldConfig.name], onChange: getChangeHandler(fieldConfig), options: fieldConfig.options, inputProps: fieldConfig.inputProps, error: validationErrors[fieldConfig.name], required: fieldConfig.required, disabled: disabled, placeholder: fieldConfig.placeholder }, fieldConfig.name))) }) })] }, group.key));
        }) }));
};
export default TradeFormFieldGroups;
//# sourceMappingURL=TradeFormFieldGroups.js.map