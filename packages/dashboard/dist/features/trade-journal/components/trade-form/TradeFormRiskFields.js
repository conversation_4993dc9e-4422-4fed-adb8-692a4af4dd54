import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import styled from 'styled-components';
const FormRow = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const FormGroup = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;
const Label = styled.label `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const Input = styled.input `
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;
const HelpText = styled.span `
  font-size: 0.8rem;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * Trade Form Risk Fields Component
 */
const TradeFormRiskFields = ({ formValues, handleChange,
// validationErrors, // Removed as unused
 }) => {
    return (_jsxs(_Fragment, { children: [_jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: 'stopLoss', children: "Stop Loss" }), _jsx(Input, { id: 'stopLoss', name: 'stopLoss', type: 'number', step: '0.01', value: formValues.stopLoss || '', onChange: handleChange })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: 'takeProfit', children: "Take Profit" }), _jsx(Input, { id: 'takeProfit', name: 'takeProfit', type: 'number', step: '0.01', value: formValues.takeProfit || '', onChange: handleChange })] })] }), _jsxs(FormRow, { children: [_jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: 'riskPoints', children: "Risk (Points)" }), _jsx(Input, { id: 'riskPoints', name: 'riskPoints', type: 'number', step: '0.01', value: formValues.riskPoints || '', onChange: handleChange })] }), _jsxs(FormGroup, { children: [_jsx(Label, { htmlFor: 'rMultiple', children: "R-Multiple" }), _jsx(Input, { id: 'rMultiple', name: 'rMultiple', type: 'number', step: '0.01', value: formValues.rMultiple || '', onChange: handleChange, disabled: true }), _jsx(HelpText, { children: "Auto-calculated from Risk Points and P/L" })] })] })] }));
};
export default TradeFormRiskFields;
//# sourceMappingURL=TradeFormRiskFields.js.map