import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Bad<PERSON>, Button } from '@adhd-trading-dashboard/shared';
const PlanList = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const PlanItem = styled.div `
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background-color: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid var(--border-primary);
  opacity: ${({ completed }) => (completed ? 0.6 : 1)};
  transition: all 0.2s ease;
  
  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    transform: translateY(-1px);
  }
`;
const CheckboxContainer = styled.div `
  margin-right: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const Checkbox = styled.input `
  cursor: pointer;
  width: 18px;
  height: 18px;
  accent-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;
const ItemContent = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  flex: 1;
`;
const Description = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  text-decoration: ${({ completed }) => (completed ? 'line-through' : 'none')};
  line-height: 1.4;
`;
const ItemMeta = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const ItemActions = styled.div `
  display: flex;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  margin-left: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const RemoveButton = styled(Button) `
  min-width: auto;
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
  
  &:hover {
    background: var(--error-color);
    color: white;
  }
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-style: italic;
  border: 2px dashed var(--border-primary);
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
`;
/**
 * Get the badge variant for a priority
 */
const getPriorityVariant = (priority) => {
    switch (priority) {
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
        default:
            return 'info';
    }
};
/**
 * Get priority color for visual consistency
 */
const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high':
            return 'var(--error-color)';
        case 'medium':
            return '#ff9800';
        case 'low':
        default:
            return '#2196f3';
    }
};
/**
 * PlanItemsList Component
 *
 * Displays a list of trading plan action items with interactive controls
 * for toggling completion status and removing items.
 */
export const PlanItemsList = ({ items, onItemToggle, onItemRemove, className, }) => {
    if (!items || items.length === 0) {
        return (_jsxs(EmptyState, { className: className, children: [_jsx("div", { children: "\uD83D\uDCCB No action items yet" }), _jsx("div", { style: { marginTop: '8px', fontSize: '0.875rem' }, children: "Add items to track your daily trading goals" })] }));
    }
    return (_jsx(PlanList, { className: className, children: items.map((item) => (_jsxs(PlanItem, { completed: item.completed, children: [_jsx(CheckboxContainer, { children: _jsx(Checkbox, { type: "checkbox", checked: !!item.completed, onChange: (e) => onItemToggle?.(item.id, e.target.checked), disabled: !onItemToggle }) }), _jsxs(ItemContent, { children: [_jsx(Description, { completed: item.completed, children: item.description }), _jsxs(ItemMeta, { children: [_jsx(Badge, { variant: getPriorityVariant(item.priority), style: {
                                        backgroundColor: `${getPriorityColor(item.priority)}20`,
                                        color: getPriorityColor(item.priority),
                                        border: `1px solid ${getPriorityColor(item.priority)}40`
                                    }, children: item.priority.toUpperCase() }), item.completed && (_jsx("span", { style: {
                                        fontSize: '0.75rem',
                                        color: 'var(--success-color)',
                                        fontWeight: 600
                                    }, children: "\u2713 COMPLETED" }))] })] }), onItemRemove && (_jsx(ItemActions, { children: _jsx(RemoveButton, { size: "small", onClick: () => onItemRemove(item.id), "aria-label": `Remove item: ${item.description}`, title: "Remove item", children: "\uD83D\uDDD1\uFE0F" }) }))] }, item.id))) }));
};
export default PlanItemsList;
//# sourceMappingURL=PlanItemsList.js.map