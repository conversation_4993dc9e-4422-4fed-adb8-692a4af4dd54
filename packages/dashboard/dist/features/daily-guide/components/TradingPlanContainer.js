import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * TradingPlanContainer Component
 *
 * REFACTORED: Main orchestrator for the trading plan feature
 * Follows the proven TradeAnalysis/TradingDashboard architecture pattern.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Focused components for each responsibility
 * - Reusable form patterns
 * - F1 racing theme consistency
 */
import { Suspense } from 'react';
import styled from 'styled-components';
import { Card, LoadingSpinner } from '@adhd-trading-dashboard/shared';
import { TradingPlanHeader } from './TradingPlanHeader';
import { PlanItemsList } from './PlanItemsList';
import { RiskManagementGrid } from './RiskManagementGrid';
import { AddItemForm } from './AddItemForm';
import { useTradingPlanForm } from '../hooks/useTradingPlanForm';
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const Section = styled.div `
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const SectionTitle = styled.h4 `
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const Notes = styled.div `
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background-color: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  white-space: pre-wrap;
  border: 1px solid var(--border-primary);
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-style: italic;
`;
const ErrorState = styled.div `
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
`;
const LoadingState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
/**
 * Loading Fallback Component
 */
const LoadingFallback = () => (_jsxs(LoadingState, { children: [_jsx(LoadingSpinner, { size: "md" }), _jsx("div", { style: { color: 'var(--text-secondary)' }, children: "Loading Trading Plan..." })] }));
/**
 * TradingPlanContainer Component
 *
 * Main container that orchestrates all trading plan functionality
 * with proper error handling and loading states.
 */
export const TradingPlanContainer = ({ tradingPlan, isLoading = false, error = null, onItemToggle, onItemAdd, onItemRemove, className, }) => {
    const { showAddForm, setShowAddForm, newItem, setNewItem, handleAddItem, resetForm } = useTradingPlanForm(onItemAdd);
    // Loading state
    if (isLoading) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(LoadingFallback, {}) }) }));
    }
    // Error state
    if (error) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsxs(ErrorState, { children: [_jsx("div", { children: "\u274C Error loading trading plan" }), _jsx("div", { children: error })] }) }));
    }
    // Empty state
    if (!tradingPlan) {
        return (_jsx(Card, { title: "Trading Plan", children: _jsxs(EmptyState, { children: ["No trading plan available.", onItemAdd && (_jsx("div", { style: { marginTop: '16px' }, children: _jsx("button", { onClick: () => setShowAddForm(true), style: {
                                padding: '8px 16px',
                                background: 'var(--primary-color)',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }, children: "Create Trading Plan" }) }))] }) }));
    }
    return (_jsx(Card, { title: "Trading Plan", children: _jsxs(Container, { className: className, children: [_jsx(TradingPlanHeader, { onAddItem: onItemAdd ? () => setShowAddForm(true) : undefined, showAddForm: showAddForm }), tradingPlan.strategy && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Strategy" }), _jsx(Notes, { children: tradingPlan.strategy })] })), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "Action Items" }), _jsx(PlanItemsList, { items: tradingPlan.items, onItemToggle: onItemToggle, onItemRemove: onItemRemove })] }), tradingPlan.riskManagement && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Risk Management" }), _jsx(RiskManagementGrid, { riskManagement: tradingPlan.riskManagement })] })), tradingPlan.notes && (_jsxs(Section, { children: [_jsx(SectionTitle, { children: "Notes" }), _jsx(Notes, { children: tradingPlan.notes })] })), showAddForm && (_jsx(Suspense, { fallback: _jsx("div", { children: "Loading form..." }), children: _jsx(AddItemForm, { newItem: newItem, setNewItem: setNewItem, onSubmit: handleAddItem, onCancel: () => {
                            setShowAddForm(false);
                            resetForm();
                        } }) }))] }) }));
};
export default TradingPlanContainer;
//# sourceMappingURL=TradingPlanContainer.js.map