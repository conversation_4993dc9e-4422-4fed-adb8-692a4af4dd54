import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card, DualTimeDisplay } from '@adhd-trading-dashboard/shared';
import { useSessionAnalytics } from '../hooks/useSessionAnalytics';
import { useEnhancedSessionIntelligence } from '../hooks/useEnhancedSessionIntelligence';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;
const CurrentSessionCard = styled.div.attrs({
    className: 'session-card current-session-card',
}) `
  /* Use CSS variables for theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-accent);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md), 0 0 0 1px var(--session-card-accent);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      var(--session-card-accent),
      var(--session-card-active-accent),
      var(--session-card-accent)
    );
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
`;
const SessionHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;
const SessionTitle = styled.h3 `
  color: var(--session-text-primary);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const LiveIndicatorSpan = styled.span `
  background: ${({ theme }) => `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`};
  color: ${({ theme }) => theme.colors.textPrimary};
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: f1Pulse 2s ease-in-out infinite;
  box-shadow: 0 0 8px ${({ theme }) => theme.colors.sessionActive}40;

  @keyframes f1Pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
`;
const RecommendationBadge = styled.div `
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  color: var(--session-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: ${({ level, theme }) => {
    switch (level) {
        case 'high':
            // F1 Racing: Green flag for high recommendation
            return `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`;
        case 'medium':
            // F1 Racing: Yellow flag for caution/medium
            return `linear-gradient(135deg, ${theme.colors.sessionCaution}, ${theme.colors.sessionTransition})`;
        case 'low':
            // F1 Racing: Orange flag for poor conditions
            return `linear-gradient(135deg, ${theme.colors.sessionTransition}, ${theme.colors.performancePoor})`;
        case 'avoid':
            // F1 Racing: Red flag for danger/avoid
            return `linear-gradient(135deg, ${theme.colors.performanceAvoid}, ${theme.colors.error})`;
        default:
            return 'var(--session-card-border)';
    }
}};

  /* F1 Racing: Add racing stripe for high recommendations */
  ${({ level }) => level === 'high' &&
    `
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: linear-gradient(180deg, var(--session-optimal), var(--session-active));
      border-radius: 6px 0 0 6px;
    }
  `}
`;
const MetricsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;
const MetricCard = styled.div `
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  box-shadow: var(--shadow-sm);
`;
const MetricValue = styled.div `
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: 4px;
`;
const MetricLabel = styled.div `
  font-size: 12px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ActionItems = styled.div `
  background: ${({ theme }) => `linear-gradient(135deg, ${theme.colors.sessionActive}20, ${theme.colors.sessionOptimal}10)`};
  border: 2px solid ${({ theme }) => theme.colors.sessionActive};
  border-left: 6px solid ${({ theme }) => theme.colors.sessionActive};
  border-radius: 8px;
  padding: 16px;
  position: relative;

  /* Mercedes F1 racing stripe accent */
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(
      180deg,
      ${({ theme }) => theme.colors.sessionOptimal},
      ${({ theme }) => theme.colors.sessionActive}
    );
    border-radius: 8px 0 0 8px;
  }
`;
const ActionTitle = styled.h4 `
  color: ${({ theme }) => theme.colors.sessionActive};
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;
`;
const ActionList = styled.ul `
  margin: 0;
  padding: 0;
  list-style: none;
`;
const ActionItem = styled.li `
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;

  &::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: ${({ theme }) => theme.colors.sessionActive};
    font-size: 12px;
    text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;
// Unused styled components removed to fix TypeScript errors
// Enhanced ICT Session Components with F1 Racing Theme
const ICTSessionCard = styled.div.attrs(({ isActive, isOptimal }) => ({
    className: `session-card ict-session-card ${isActive ? 'active' : ''} ${isOptimal ? 'optimal' : ''}`,
    'data-session-state': isActive && isOptimal ? 'active-optimal' : isActive ? 'active' : 'inactive',
})) `
  /* Use CSS variables for clean theming */
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-left: 4px solid var(--session-card-accent);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  &[data-session-state='active'] {
    border-left-color: var(--session-active);
    border-color: var(--session-active);
    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-active);
  }

  &[data-session-state='active-optimal'] {
    border-left-color: var(--session-optimal);
    border-color: var(--session-optimal);
    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-optimal), var(--shadow-accent);
  }
`;
const ICTSessionHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;
const ICTSessionName = styled.div `
  font-size: 16px;
  font-weight: 700;
  color: ${({ isActive }) => (isActive ? 'var(--session-active)' : 'var(--session-text-primary)')};
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: none;
`;
const ICTTimeRange = styled.div `
  font-size: 12px;
  color: var(--session-text-secondary);
`;
const ICTPerformanceGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin: 12px 0;
`;
const ICTMetric = styled.div `
  text-align: center;
`;
const ICTMetricValue = styled.div `
  font-size: 18px;
  font-weight: 700;
  color: var(--session-text-primary);
`;
const ICTMetricLabel = styled.div `
  font-size: 10px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ModelPreferenceHeader = styled.div `
  font-size: 12px;
  color: var(--session-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  font-weight: 600;
`;
const OptimalWindowsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
`;
const WindowCard = styled.div.attrs(({ isOptimal }) => ({
    className: `window-card optimal-window-card ${isOptimal ? 'optimal' : ''}`,
})) `
  background: var(--session-card-bg);
  border: 1px solid var(--session-card-border);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  ${({ isOptimal }) => isOptimal &&
    `
    border-left: 3px solid var(--session-optimal);
    border-color: var(--session-optimal);
    box-shadow:
      var(--shadow-sm),
      0 0 0 1px var(--session-optimal);
  `}
`;
const WindowTime = styled.div.attrs({
    className: 'primary-text session-title',
}) `
  font-size: 11px;
  font-weight: 600;
  color: var(--session-text-primary);
`;
const WindowDescription = styled.div.attrs({
    className: 'secondary-text session-description',
}) `
  font-size: 9px;
  color: var(--session-text-secondary);
  margin: 2px 0;
`;
const WindowStats = styled.div.attrs({
    className: 'secondary-text metric-label',
}) `
  font-size: 9px;
  color: var(--session-text-secondary);
`;
// LiveIndicator styled component removed as unused
const WeeklyInsightsCard = styled.div `
  background: var(--session-card-bg);
  border: 1px solid var(--secondary-color);
  border-left: 4px solid var(--secondary-color);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
  box-shadow: var(--shadow-sm);
`;
const InsightsHeader = styled.div `
  font-size: 14px;
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const InsightsList = styled.div `
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
const InsightItem = styled.div `
  font-size: 12px;
  color: var(--session-text-secondary);
  padding: 6px 0;
  border-bottom: 1px solid var(--session-card-border);

  &:last-child {
    border-bottom: none;
  }
`;
/**
 * Session Focus Component
 */
export const SessionFocus = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();
    const { intelligence, isLoading: enhancedLoading, error: enhancedError, } = useEnhancedSessionIntelligence();
    const loading = isLoading || analyticsLoading || enhancedLoading;
    const displayError = error || analyticsError || enhancedError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: '\uD83C\uDFAF Session Focus', children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Analyzing your trading sessions..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: '\uD83C\uDFAF Session Focus', children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: 'var(--error-color)' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: 'var(--session-card-bg)',
                            border: '1px solid var(--session-card-border)',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            color: 'var(--session-text-primary)',
                        }, children: "Retry" }))] }) }));
    }
    // Unused destructured variables removed
    return (_jsx(Card, { title: '\uD83D\uDD58 Enhanced Session Intelligence', actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsxs(CurrentSessionCard, { children: [_jsxs(SessionHeader, { children: [_jsxs(SessionTitle, { children: [intelligence.currentStatus.currentRecommendation, intelligence.currentStatus.activeSession && (_jsx(LiveIndicatorSpan, { children: "LIVE" }))] }), _jsx(RecommendationBadge, { level: intelligence.currentStatus.urgency.toLowerCase(), children: intelligence.currentStatus.urgency })] }), _jsxs(MetricsGrid, { children: [_jsxs(MetricCard, { children: [_jsx(MetricValue, { children: _jsx(DualTimeDisplay, { mode: 'current', format: 'compact', showLive: true }) }), _jsx(MetricLabel, { children: "Current Time" })] }), _jsxs(MetricCard, { children: [_jsx(MetricValue, { children: intelligence.currentStatus.activeSession?.sessionName || 'None' }), _jsx(MetricLabel, { children: "Active Session" })] }), _jsxs(MetricCard, { children: [_jsx(MetricValue, { children: intelligence.currentStatus.timeToNextFormatted }), _jsx(MetricLabel, { children: "Next Session" })] })] })] }), intelligence.currentStatus.activeSession && (_jsxs(ICTSessionCard, { isActive: true, isOptimal: true, children: [_jsx(ICTSessionHeader, { children: _jsxs(ICTSessionName, { isActive: true, children: ["\u23F0 LIVE SESSION INTELLIGENCE", _jsx(LiveIndicatorSpan, { children: "LIVE SESSION" })] }) }), _jsxs("div", { style: { marginBottom: '16px' }, children: [_jsxs("h4", { style: {
                                        color: 'var(--session-text-primary)',
                                        marginBottom: '12px',
                                        fontSize: '16px',
                                    }, children: [intelligence.currentStatus.activeSession.sessionName, " - Active"] }), _jsx("div", { style: { display: 'grid', gap: '12px' }, children: intelligence.sessions
                                        .find(s => s.sessionName === intelligence.currentStatus.activeSession?.sessionName)
                                        ?.optimalWindows.map((window, index) => (_jsxs(WindowCard, { isOptimal: window.winRate >= 70 && window.trades >= 2, children: [_jsx(WindowTime, { children: _jsx(DualTimeDisplay, { mode: 'session', sessionStart: window.start, sessionEnd: window.end, format: 'compact' }) }), _jsx(WindowDescription, { children: window.description }), _jsx(WindowStats, { children: window.trades > 0
                                                    ? `${window.winRate.toFixed(0)}% win rate (${window.trades} trades)`
                                                    : 'No data' })] }, index))) })] })] })), intelligence.sessions.map(session => {
                    const isActive = intelligence.currentStatus.activeSession?.sessionName === session.sessionName;
                    const hasOptimalWindow = session.optimalWindows.some(w => w.winRate >= 70);
                    return (_jsxs(ICTSessionCard, { isActive: isActive, isOptimal: hasOptimalWindow, children: [_jsxs(ICTSessionHeader, { children: [_jsxs(ICTSessionName, { isActive: isActive, children: [session.sessionName, isActive && _jsx(LiveIndicatorSpan, { children: "ACTIVE" })] }), _jsx(ICTTimeRange, { children: session.timeRange ? (_jsx(DualTimeDisplay, { mode: 'session', sessionStart: session.timeRange.split('-')[0], sessionEnd: session.timeRange.split('-')[1], format: 'compact' })) : (_jsx("span", { children: "Time range not available" })) })] }), _jsxs(ICTPerformanceGrid, { children: [_jsxs(ICTMetric, { children: [_jsx(ICTMetricValue, { children: session.performance.totalTrades }), _jsx(ICTMetricLabel, { children: "Trades" })] }), _jsxs(ICTMetric, { children: [_jsxs(ICTMetricValue, { children: [session.performance.winRate.toFixed(0), "%"] }), _jsx(ICTMetricLabel, { children: "Win Rate" })] }), _jsxs(ICTMetric, { children: [_jsxs(ICTMetricValue, { children: [session.performance.avgRMultiple.toFixed(1), "R"] }), _jsx(ICTMetricLabel, { children: "Avg R-Multiple" })] }), _jsxs(ICTMetric, { children: [_jsx(ICTMetricValue, { children: session.performance.avgRisk.toFixed(0) }), _jsx(ICTMetricLabel, { children: "Avg Risk (pts)" })] })] }), _jsxs("div", { children: [_jsx(ModelPreferenceHeader, { children: "Optimal Time Windows" }), _jsx(OptimalWindowsGrid, { children: session.optimalWindows.map((window, index) => (_jsxs(WindowCard, { isOptimal: window.winRate >= 70 && window.trades >= 2, children: [_jsx(WindowTime, { children: _jsx(DualTimeDisplay, { mode: 'session', sessionStart: window.start, sessionEnd: window.end, format: 'compact' }) }), _jsx(WindowDescription, { children: window.description }), _jsx(WindowStats, { children: window.trades > 0
                                                        ? `${window.winRate.toFixed(0)}% (${window.trades} trades)`
                                                        : 'No data' })] }, index))) })] }), session.recommendations.length > 0 && (_jsxs(ActionItems, { children: [_jsx(ActionTitle, { children: "Session Recommendations" }), _jsx(ActionList, { children: session.recommendations.map((rec, index) => (_jsx(ActionItem, { children: rec }, index))) })] }))] }, session.sessionName));
                }), _jsxs(WeeklyInsightsCard, { children: [_jsx(InsightsHeader, { children: "\uD83D\uDCCA Weekly Performance Insights" }), _jsxs(InsightsList, { children: [_jsxs(InsightItem, { children: [_jsx("strong", { children: "Best Session:" }), " ", intelligence.weeklyInsights.bestSession] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Best Model:" }), " ", intelligence.weeklyInsights.bestModel] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Average Quality:" }), " ", intelligence.weeklyInsights.avgQuality.toFixed(1), "/5.0"] }), _jsxs(InsightItem, { children: [_jsx("strong", { children: "Quality Threshold:" }), ' ', `>${intelligence.weeklyInsights.qualityThreshold.toFixed(1)}`, " for optimal performance"] })] }), _jsxs(ActionItems, { style: { marginTop: '12px' }, children: [_jsx(ActionTitle, { children: "Key Recommendations" }), _jsx(ActionList, { children: intelligence.weeklyInsights.recommendations.map((rec, index) => (_jsx(ActionItem, { children: rec }, index))) })] })] })] }) }));
};
//# sourceMappingURL=SessionFocus.js.map