/**
 * Guide Tab Configuration
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * Centralized configuration for guide tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different guide views
 * - Clear content mapping
 */
import React from 'react';
import { GuideTab } from './F1GuideTabs';
export interface GuideTabConfig {
    id: GuideTab;
    title: string;
    description: string;
    icon: string;
    component: React.ComponentType<any>;
    showInMobile: boolean;
    requiresData: boolean;
}
export interface GuideTabContentProps {
    /** Current active tab */
    activeTab: GuideTab;
    /** Guide data */
    data: {
        marketOverview: any;
        tradingPlan: any;
        keyPriceLevels: any;
        selectedDate: string;
        currentDate: string;
    };
    /** Loading state */
    isLoading: boolean;
    /** Error state */
    error: string | null;
    /** Action handlers */
    handlers: {
        onDateChange: (date: string) => void;
        onTradingPlanItemToggle: (id: string, completed: boolean) => void;
        onAddTradingPlanItem: (item: any) => void;
        onRemoveTradingPlanItem: (id: string) => void;
        onRefresh: () => void;
    };
}
/**
 * Tab configuration with components and metadata
 */
export declare const GUIDE_TAB_CONFIG: Record<GuideTab, GuideTabConfig>;
/**
 * Get tab configuration by ID
 */
export declare const getTabConfig: (tabId: GuideTab) => GuideTabConfig;
/**
 * Get all tab configurations
 */
export declare const getAllTabConfigs: () => GuideTabConfig[];
/**
 * Get mobile-friendly tabs
 */
export declare const getMobileTabConfigs: () => GuideTabConfig[];
/**
 * Get tabs that require data
 */
export declare const getDataRequiredTabConfigs: () => GuideTabConfig[];
/**
 * Tab Content Renderer Component
 */
export declare const GuideTabContentRenderer: React.FC<GuideTabContentProps>;
export default GuideTabContentRenderer;
//# sourceMappingURL=guideTabConfig.d.ts.map