import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card } from '@adhd-trading-dashboard/shared';
import styled from 'styled-components';
import { MarketSummary } from './MarketSummary';
import { MarketIndicators } from './MarketIndicators';
import { MarketNews } from './MarketNews';
// Styled components
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
/**
 * Market Overview Component
 *
 * Main container component for displaying market overview information.
 */
export const MarketOverview = ({ marketOverview, isLoading = false, error = null, onRefresh, className, }) => {
    // Loading state
    if (isLoading) {
        return (_jsx(Card, { title: "Market Overview", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Loading market data..." }) }));
    }
    // Error state
    if (error) {
        return (_jsx(Card, { title: "Market Overview", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: 'var(--error-color)' }, children: ["Error: ", error, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    // Empty state
    if (!marketOverview) {
        return (_jsx(Card, { title: "Market Overview", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center' }, children: ["No market data available.", onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Refresh" }))] }) }));
    }
    return (_jsx(Card, { title: "Market Overview", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsxs(Container, { className: className, children: [_jsx(MarketSummary, { sentiment: marketOverview.sentiment, summary: marketOverview.summary, lastUpdated: marketOverview.lastUpdated }), _jsx(MarketIndicators, { indices: marketOverview.indices }), marketOverview.economicEvents && marketOverview.economicEvents.length > 0 && (_jsx(MarketNews, { events: marketOverview.economicEvents }))] }) }));
};
//# sourceMappingURL=MarketOverview.js.map