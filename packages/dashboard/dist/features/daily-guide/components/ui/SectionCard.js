import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const Container = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid var(--border-primary);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const Header = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
const Title = styled.h2 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;
const Content = styled.div ``;
const LoadingPlaceholder = styled.div `
  height: 200px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const ErrorContainer = styled.div `
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.error + '10'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.error};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;
export const SectionCard = ({ title, children, isLoading = false, hasError = false, errorMessage = 'An error occurred while loading data', actionButton, }) => {
    return (_jsxs(Container, { children: [_jsxs(Header, { children: [_jsx(Title, { children: title }), actionButton && actionButton] }), hasError && (_jsx(ErrorContainer, { children: _jsx("p", { children: errorMessage }) })), isLoading ? (_jsx(LoadingPlaceholder, { children: "Loading data..." })) : (_jsx(Content, { children: children }))] }));
};
//# sourceMappingURL=SectionCard.js.map