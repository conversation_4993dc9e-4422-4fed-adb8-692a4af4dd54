import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';
import { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';
import { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';
import { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';
import { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';
// Styled components with F1 theme
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const IntelligenceGrid = styled.div `
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;
const Section = styled.div `
  margin-bottom: 24px;
`;
const SectionTitle = styled.h3 `
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;
const ModelRecommendationCard = styled.div `
  background: var(--model-card-bg);
  border: 1px solid var(--model-card-border);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
`;
const ModelHeader = styled.div `
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
`;
const RecommendedModel = styled.div `
  font-size: 24px;
  font-weight: 800;
  color: var(--model-name-color);
  text-transform: uppercase;
  letter-spacing: 1px;
`;
const ProbabilityBadge = styled.div `
  background: ${({ confidence }) => {
    switch (confidence) {
        case 'HIGH':
            return 'linear-gradient(90deg, #10b981, #059669)';
        case 'MEDIUM':
            return 'linear-gradient(90deg, #f59e0b, #d97706)';
        default:
            return 'linear-gradient(90deg, #6b7280, #4b5563)';
    }
}};
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
`;
const ConditionsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin: 16px 0;
`;
const ConditionItem = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
`;
const ConditionLabel = styled.div `
  color: #9ca3af;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
`;
const ConditionValue = styled.div `
  color: #ffffff;
  font-weight: 600;
`;
const ReasoningText = styled.div `
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`;
const PatternQualityCard = styled.div `
  background: ${({ rating }) => {
    switch (rating) {
        case 'EXCEPTIONAL':
            return 'var(--pattern-excellent-bg)';
        case 'EXCELLENT':
            return 'var(--pattern-excellent-bg)';
        case 'GOOD':
            return 'var(--pattern-good-bg)';
        case 'FAIR':
            return 'var(--pattern-average-bg)';
        default:
            return 'var(--pattern-poor-bg)';
    }
}};
  border: 1px solid
    ${({ rating }) => {
    switch (rating) {
        case 'EXCEPTIONAL':
            return 'var(--pattern-excellent-border)';
        case 'EXCELLENT':
            return 'var(--pattern-excellent-border)';
        case 'GOOD':
            return 'var(--pattern-good-border)';
        case 'FAIR':
            return 'var(--pattern-average-border)';
        default:
            return 'var(--pattern-poor-border)';
    }
}};
  border-radius: 12px;
  padding: 20px;
`;
const QualityScore = styled.div `
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
`;
const ScoreValue = styled.div `
  font-size: 32px;
  font-weight: 800;
  color: #ffffff;
`;
const ScoreMax = styled.div `
  font-size: 16px;
  color: #9ca3af;
`;
const QualityRating = styled.div `
  background: ${({ rating }) => {
    switch (rating) {
        case 'EXCEPTIONAL':
            return 'var(--pattern-excellent-text)';
        case 'EXCELLENT':
            return 'var(--pattern-excellent-text)';
        case 'GOOD':
            return 'var(--pattern-good-text)';
        case 'FAIR':
            return 'var(--pattern-average-text)';
        default:
            return 'var(--pattern-poor-text)';
    }
}};
  color: ${({ rating }) => {
    switch (rating) {
        case 'EXCEPTIONAL':
            return 'var(--bg-primary)';
        case 'EXCELLENT':
            return 'var(--bg-primary)';
        case 'GOOD':
            return 'var(--text-primary)';
        case 'FAIR':
            return 'var(--bg-primary)';
        default:
            return 'var(--text-primary)';
    }
}};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
`;
const BreakdownGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 16px 0;
`;
const BreakdownItem = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 12px;
`;
const BreakdownLabel = styled.div `
  color: #9ca3af;
`;
const BreakdownScore = styled.div `
  color: #ffffff;
  font-weight: 600;
`;
const SessionWindowCard = styled.div `
  background: ${({ isActive, isOptimal }) => {
    if (isActive && isOptimal)
        return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
    if (isActive)
        return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
    if (isOptimal)
        return 'linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05))';
    return 'rgba(255, 255, 255, 0.05)';
}};
  border: 1px solid
    ${({ isActive, isOptimal, theme }) => {
    if (isActive && isOptimal)
        return `${theme.colors.primary}66`; // 40% opacity
    if (isActive)
        return 'rgba(59, 130, 246, 0.4)';
    if (isOptimal)
        return 'rgba(16, 185, 129, 0.3)';
    return 'rgba(255, 255, 255, 0.1)';
}};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`;
const WindowHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;
const WindowLabel = styled.div `
  font-weight: 700;
  color: ${({ isActive, theme }) => (isActive ? theme.colors.primary : '#ffffff')};
  font-size: 14px;
`;
const WindowTime = styled.div `
  color: #9ca3af;
  font-size: 12px;
`;
const WindowStats = styled.div `
  display: flex;
  gap: 16px;
  font-size: 12px;
`;
const WindowStat = styled.div `
  color: #d1d5db;
`;
const LiveIndicator = styled.div `
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: ${({ theme }) => theme.colors.primary};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
const ProbabilityCard = styled.div `
  background: ${({ recommendation }) => {
    switch (recommendation) {
        case 'PRIORITIZE':
            return 'var(--recommendation-prioritize-bg)';
        case 'INCREASE_SIZE':
            return 'var(--recommendation-increase-bg)';
        case 'STANDARD':
            return 'var(--recommendation-standard-bg)';
        case 'REDUCE_SIZE':
            return 'var(--recommendation-reduce-bg)';
        default:
            return 'var(--recommendation-avoid-bg)';
    }
}};
  border: 1px solid var(--setup-success-border);
  border-radius: 12px;
  padding: 20px;
  opacity: 0.9;
`;
const ProbabilityHeader = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;
const ProbabilityValue = styled.div `
  font-size: 32px;
  font-weight: 800;
  color: #ffffff;
`;
const RecommendationBadge = styled.div `
  background: ${({ recommendation }) => {
    switch (recommendation) {
        case 'PRIORITIZE':
            return 'var(--recommendation-prioritize-bg)';
        case 'INCREASE_SIZE':
            return 'var(--recommendation-increase-bg)';
        case 'STANDARD':
            return 'var(--recommendation-standard-bg)';
        case 'REDUCE_SIZE':
            return 'var(--recommendation-reduce-bg)';
        default:
            return 'var(--recommendation-avoid-bg)';
    }
}};
  color: ${({ recommendation }) => {
    switch (recommendation) {
        case 'PRIORITIZE':
            return 'var(--recommendation-prioritize-text)';
        case 'INCREASE_SIZE':
            return 'var(--recommendation-increase-text)';
        case 'STANDARD':
            return 'var(--recommendation-standard-text)';
        case 'REDUCE_SIZE':
            return 'var(--recommendation-reduce-text)';
        default:
            return 'var(--recommendation-avoid-text)';
    }
}};
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
`;
const RiskManagementGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 16px;
`;
const RiskItem = styled.div `
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
`;
const ModelComparisonCard = styled.div `
  background: ${({ isRecommended }) => isRecommended
    ? 'var(--model-card-bg)' // Uses theme CSS variable
    : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isRecommended }) => isRecommended
    ? 'var(--model-card-border)' // Uses theme CSS variable
    : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 8px;
  padding: 12px;
  text-align: center;
`;
const ModelName = styled.div `
  font-size: 14px;
  font-weight: 600;
  color: ${({ isRecommended }) => (isRecommended ? 'var(--model-name-color)' : '#ffffff')};
  margin-bottom: 8px;
`;
const RiskLabel = styled.div `
  color: #9ca3af;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
`;
const RiskValue = styled.div `
  color: #ffffff;
  font-weight: 600;
`;
const PriorityCard = styled.div `
  background: ${({ priority }) => {
    switch (priority) {
        case 'HIGH':
            return 'var(--priority-high-bg)';
        case 'MEDIUM':
            return 'var(--priority-medium-bg)';
        default:
            return 'var(--priority-low-bg)';
    }
}};
  border: 1px solid
    ${({ priority }) => {
    switch (priority) {
        case 'HIGH':
            return 'var(--priority-high-border)';
        case 'MEDIUM':
            return 'var(--priority-medium-border)';
        default:
            return 'var(--priority-low-border)';
    }
}};
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
`;
const PriorityLabel = styled.span `
  color: ${({ priority }) => {
    switch (priority) {
        case 'HIGH':
            return 'var(--priority-high-text)';
        case 'MEDIUM':
            return 'var(--priority-medium-text)';
        default:
            return 'var(--priority-low-text)';
    }
}};
  font-size: 12px;
  font-weight: bold;
`;
const LiquidityCard = styled.div `
  background: var(--setup-intelligence-bg);
  border: 1px solid var(--setup-intelligence-border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
`;
const SetupIntelligenceContainer = styled.div `
  background: var(--setup-intelligence-bg);
  border: 1px solid var(--setup-intelligence-border);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;
/**
 * Elite ICT Intelligence Component
 */
export const EliteICTIntelligence = ({ isLoading = false, error = null, onRefresh, className, }) => {
    const { recommendation: modelRec, modelStats, isLoading: modelLoading, error: modelError, } = useModelSelectionEngine();
    const { analysis: qualityAnalysis, isLoading: qualityLoading, error: qualityError, } = usePatternQualityScoring();
    const { sessionAnalyses, liveGuidance, isLoading: sessionLoading, error: sessionError, } = useGranularSessionIntelligence();
    const { successProbability, isLoading: probabilityLoading, error: probabilityError, } = useSuccessProbabilityCalculator(modelRec, qualityAnalysis.currentScore);
    const { setupIntelligence, isLoading: setupLoading, error: setupError, } = useEnhancedSetupIntelligence();
    const loading = isLoading ||
        modelLoading ||
        qualityLoading ||
        sessionLoading ||
        probabilityLoading ||
        setupLoading;
    const displayError = error || modelError || qualityError || sessionError || setupError;
    // Loading state
    if (loading) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Elite ICT Trading Intelligence", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Analyzing market conditions and generating intelligent recommendations..." }) }));
    }
    // Error state
    if (displayError) {
        return (_jsx(Card, { title: "\uD83C\uDFAF Elite ICT Trading Intelligence", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", displayError, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    const activeSession = liveGuidance.activeSession;
    return (_jsx(Card, { title: "\uD83C\uDFAF Elite ICT Trading Intelligence", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, className: className, children: _jsxs(Container, { children: [_jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83C\uDFAF MODEL RECOMMENDATION ENGINE" }), _jsxs(ModelRecommendationCard, { children: [_jsxs(ModelHeader, { children: [_jsx(RecommendedModel, { children: modelRec.recommendedModel }), _jsxs(ProbabilityBadge, { confidence: modelRec.confidence, children: [modelRec.probability.toFixed(0), "% - ", modelRec.confidence] })] }), _jsxs(ConditionsGrid, { children: [_jsxs(ConditionItem, { children: [_jsx(ConditionLabel, { children: "Volatility" }), _jsx(ConditionValue, { children: modelRec.marketConditions.volatility.toUpperCase() })] }), _jsxs(ConditionItem, { children: [_jsx(ConditionLabel, { children: "Liquidity" }), _jsx(ConditionValue, { children: modelRec.marketConditions.liquidityContext.toUpperCase() })] }), _jsxs(ConditionItem, { children: [_jsx(ConditionLabel, { children: "HTF Trend" }), _jsx(ConditionValue, { children: modelRec.marketConditions.htfTrend.toUpperCase() })] }), _jsxs(ConditionItem, { children: [_jsx(ConditionLabel, { children: "Previous Success" }), _jsx(ConditionValue, { children: modelRec.marketConditions.previousSessionSuccess || 'NONE' })] })] }), _jsxs(ReasoningText, { children: [_jsx("strong", { children: "Reasoning:" }), " ", modelRec.reasoning] }), _jsxs(ReasoningText, { children: [_jsx("strong", { children: "Alternative:" }), " ", modelRec.alternativeModel, ' ', modelRec.alternativeCondition] })] })] }), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA MODEL PERFORMANCE ANALYSIS" }), _jsx("div", { style: { display: 'grid', gap: '16px' }, children: _jsxs("div", { style: {
                                    background: 'rgba(255, 255, 255, 0.05)',
                                    borderRadius: '12px',
                                    padding: '16px',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                }, children: [_jsx("div", { style: {
                                            fontSize: '14px',
                                            color: '#9ca3af',
                                            textTransform: 'uppercase',
                                            letterSpacing: '0.5px',
                                            marginBottom: '12px',
                                        }, children: "Model Performance Comparison" }), _jsxs("div", { style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }, children: [_jsxs(ModelComparisonCard, { isRecommended: modelRec.recommendedModel === 'RD-Cont', children: [_jsx(ModelName, { isRecommended: modelRec.recommendedModel === 'RD-Cont', children: "RD-Cont Model" }), _jsxs("div", { style: { fontSize: '12px', color: '#9ca3af' }, children: ["Historical Performance", _jsx("br", {}), "Based on your trading data"] })] }), _jsxs(ModelComparisonCard, { isRecommended: modelRec.recommendedModel === 'FVG-RD', children: [_jsx(ModelName, { isRecommended: modelRec.recommendedModel === 'FVG-RD', children: "FVG-RD Model" }), _jsxs("div", { style: { fontSize: '12px', color: '#9ca3af' }, children: ["Historical Performance", _jsx("br", {}), "Based on your trading data"] })] })] }), _jsxs("div", { style: {
                                            textAlign: 'center',
                                            marginTop: '12px',
                                            fontSize: '14px',
                                            color: '#dc2626',
                                            fontWeight: '600',
                                        }, children: ["Current Recommendation: ", modelRec.recommendedModel] })] }) })] }), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83C\uDFAF SETUP SUCCESS PROBABILITY" }), _jsxs(ProbabilityCard, { recommendation: successProbability.recommendation, children: [_jsxs(ProbabilityHeader, { children: [_jsxs(ProbabilityValue, { children: [successProbability.finalProbability.toFixed(0), "%"] }), _jsx(RecommendationBadge, { recommendation: successProbability.recommendation, children: successProbability.recommendation.replace('_', ' ') })] }), _jsxs(BreakdownGrid, { children: [_jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "Base Model Win Rate" }), _jsxs(BreakdownScore, { children: [successProbability.breakdown.baseModelWinRate.toFixed(0), "%"] })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "Session Bonus" }), _jsxs(BreakdownScore, { children: [successProbability.breakdown.sessionBonus > 0 ? '+' : '', successProbability.breakdown.sessionBonus.toFixed(0), "%"] })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "Quality Bonus" }), _jsxs(BreakdownScore, { children: [successProbability.breakdown.qualityBonus > 0 ? '+' : '', successProbability.breakdown.qualityBonus.toFixed(0), "%"] })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "News Impact" }), _jsxs(BreakdownScore, { children: [successProbability.breakdown.newsImpact > 0 ? '+' : '', successProbability.breakdown.newsImpact.toFixed(0), "%"] })] })] }), _jsxs(ReasoningText, { children: [_jsx("strong", { children: "Expected R-Multiple:" }), ' ', successProbability.expectedRMultiple.min.toFixed(1), " -", ' ', successProbability.expectedRMultiple.max.toFixed(1), " (avg:", ' ', successProbability.expectedRMultiple.average.toFixed(1), ")"] }), _jsxs(RiskManagementGrid, { children: [_jsxs(RiskItem, { children: [_jsx(RiskLabel, { children: "Position Sizing" }), _jsx(RiskValue, { children: successProbability.riskManagement.positionSizing })] }), _jsxs(RiskItem, { children: [_jsx(RiskLabel, { children: "Max Risk" }), _jsxs(RiskValue, { children: [successProbability.riskManagement.maxRiskPercent, "%"] })] }), _jsxs(RiskItem, { children: [_jsx(RiskLabel, { children: "Stop Loss" }), _jsxs(RiskValue, { children: [successProbability.riskManagement.stopLossMultiplier, "x"] })] }), _jsxs(RiskItem, { children: [_jsx(RiskLabel, { children: "Take Profit" }), _jsxs(RiskValue, { children: [successProbability.riskManagement.takeProfitTargets.join('R, '), "R"] })] })] })] })] }), _jsxs(IntelligenceGrid, { children: [_jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA PATTERN QUALITY ANALYSIS" }), _jsxs(PatternQualityCard, { rating: qualityAnalysis.currentScore.rating, children: [_jsxs(QualityScore, { children: [_jsx(ScoreValue, { children: qualityAnalysis.currentScore.totalScore.toFixed(1) }), _jsx(ScoreMax, { children: "/5.0" }), _jsx(QualityRating, { rating: qualityAnalysis.currentScore.rating, children: qualityAnalysis.currentScore.rating })] }), _jsxs(BreakdownGrid, { children: [_jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "PD Array Confluence" }), _jsx(BreakdownScore, { children: qualityAnalysis.currentScore.breakdown.pdArrayConfluence.toFixed(1) })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "FVG Characteristics" }), _jsx(BreakdownScore, { children: qualityAnalysis.currentScore.breakdown.fvgCharacteristics.toFixed(1) })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "RD Strength" }), _jsx(BreakdownScore, { children: qualityAnalysis.currentScore.breakdown.rdStrength.toFixed(1) })] }), _jsxs(BreakdownItem, { children: [_jsx(BreakdownLabel, { children: "Confirmation" }), _jsx(BreakdownScore, { children: qualityAnalysis.currentScore.breakdown.confirmationSignals.toFixed(1) })] })] }), _jsxs(ReasoningText, { children: [_jsx("strong", { children: "Recommendation:" }), " ", qualityAnalysis.currentScore.recommendation] }), _jsxs(ReasoningText, { children: [_jsx("strong", { children: "Expected Win Probability:" }), ' ', qualityAnalysis.currentScore.expectedWinProbability.toFixed(0), "%"] })] })] }), _jsxs(Section, { children: [_jsx(SectionTitle, { children: "\uD83C\uDFAF SETUP INTELLIGENCE" }), _jsxs("div", { style: { marginBottom: '20px' }, children: [_jsx("h4", { style: { color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }, children: "Current Recommendations" }), _jsxs(SetupIntelligenceContainer, { children: [_jsxs("div", { style: { color: 'var(--text-primary)', marginBottom: '8px' }, children: [_jsx("strong", { children: "Primary Setup:" }), ' ', setupIntelligence.currentRecommendations.primarySetup] }), _jsxs("div", { style: { color: 'var(--text-primary)', marginBottom: '8px' }, children: [_jsx("strong", { children: "Secondary Setup:" }), ' ', setupIntelligence.currentRecommendations.secondarySetup] }), _jsxs("div", { style: { color: 'var(--text-primary)', marginBottom: '8px' }, children: [_jsx("strong", { children: "Liquidity Target:" }), ' ', setupIntelligence.currentRecommendations.liquidityTarget] }), _jsx("div", { style: { color: 'var(--text-secondary)', fontSize: '14px', marginTop: '12px' }, children: setupIntelligence.currentRecommendations.reasoning }), _jsxs("div", { style: {
                                                        display: 'flex',
                                                        gap: '16px',
                                                        marginTop: '12px',
                                                        fontSize: '14px',
                                                    }, children: [_jsxs("span", { style: { color: 'var(--setup-intelligence-accent)' }, children: ["Expected Win Rate:", ' ', setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0), "%"] }), _jsxs("span", { style: { color: 'var(--setup-intelligence-accent)' }, children: ["Expected R-Multiple:", ' ', setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)] })] })] })] }), setupIntelligence.bestCombinations.length > 0 && (_jsxs("div", { style: { marginBottom: '20px' }, children: [_jsx("h4", { style: { color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }, children: "Top Setup Combinations" }), setupIntelligence.bestCombinations.slice(0, 3).map((combo, index) => (_jsxs(PriorityCard, { priority: combo.priority, children: [_jsxs("div", { style: {
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        marginBottom: '8px',
                                                    }, children: [_jsxs(PriorityLabel, { priority: combo.priority, children: [combo.priority, " PRIORITY"] }), _jsxs("div", { style: { display: 'flex', gap: '12px', fontSize: '12px' }, children: [_jsxs("span", { style: { color: 'var(--success-color)' }, children: [combo.performance.winRate.toFixed(0), "% Win Rate"] }), _jsxs("span", { style: { color: 'var(--setup-intelligence-accent)' }, children: [combo.performance.avgRMultiple.toFixed(1), "R Avg"] }), _jsxs("span", { style: { color: 'var(--text-muted)' }, children: [combo.performance.totalTrades, " Trades"] })] })] }), _jsxs("div", { style: {
                                                        color: 'var(--text-primary)',
                                                        fontSize: '14px',
                                                        marginBottom: '4px',
                                                    }, children: [_jsx("strong", { children: combo.primary }), " + ", _jsx("strong", { children: combo.secondary })] }), _jsxs("div", { style: {
                                                        color: 'var(--text-secondary)',
                                                        fontSize: '13px',
                                                        marginBottom: '8px',
                                                    }, children: ["Liquidity: ", combo.liquidity] }), _jsx("div", { style: { color: 'var(--text-secondary)', fontSize: '12px' }, children: combo.recommendation })] }, index)))] })), setupIntelligence.liquidityIntelligence.length > 0 && (_jsxs("div", { children: [_jsx("h4", { style: { color: 'var(--text-primary)', marginBottom: '12px', fontSize: '16px' }, children: "Liquidity Target Intelligence" }), setupIntelligence.liquidityIntelligence.slice(0, 3).map((liquidity, index) => (_jsxs(LiquidityCard, { children: [_jsxs("div", { style: {
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        marginBottom: '8px',
                                                    }, children: [_jsx("span", { style: { color: 'var(--text-primary)', fontWeight: 'bold' }, children: liquidity.liquidityTarget }), _jsxs("div", { style: { display: 'flex', gap: '12px', fontSize: '12px' }, children: [_jsxs("span", { style: { color: 'var(--success-color)' }, children: [liquidity.performance.winRate.toFixed(0), "% Success"] }), _jsxs("span", { style: { color: 'var(--setup-intelligence-accent)' }, children: [liquidity.performance.avgRMultiple.toFixed(1), "R Avg"] }), _jsxs("span", { style: { color: 'var(--text-muted)' }, children: [liquidity.performance.totalTrades, " Trades"] })] })] }), liquidity.bestModels.length > 0 && (_jsxs("div", { style: {
                                                        color: 'var(--text-secondary)',
                                                        fontSize: '13px',
                                                        marginBottom: '4px',
                                                    }, children: ["Best Models: ", liquidity.bestModels.join(', ')] })), liquidity.bestSessions.length > 0 && (_jsxs("div", { style: {
                                                        color: 'var(--text-secondary)',
                                                        fontSize: '13px',
                                                        marginBottom: '8px',
                                                    }, children: ["Best Sessions: ", liquidity.bestSessions.join(', ')] })), _jsx("div", { style: { color: 'var(--text-secondary)', fontSize: '12px' }, children: liquidity.recommendation })] }, index)))] }))] })] })] }) }));
};
//# sourceMappingURL=EliteICTIntelligence.js.map