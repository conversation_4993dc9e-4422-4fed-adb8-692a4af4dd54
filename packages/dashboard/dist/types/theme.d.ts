/**
 * Theme System Types
 *
 * TypeScript definitions for the ADHD Trading Dashboard theme system
 * supporting Mercedes Green, F1 Official, and Dark themes.
 */
export type ThemeName = 'mercedes-green' | 'f1-official' | 'dark';
export type ConfidenceLevel = 'HIGH' | 'MEDIUM' | 'LOW';
export type RecommendationType = 'PRIORITIZE' | 'INCREASE_SIZE' | 'STANDARD' | 'REDUCE_SIZE' | 'AVOID';
export type PatternQualityRating = 'EXCEPTIONAL' | 'EXCELLENT' | 'GOOD' | 'FAIR' | 'AVERAGE' | 'POOR';
export type PriorityLevel = 'HIGH' | 'MEDIUM' | 'LOW';
export interface ThemeColors {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    textPrimary: string;
    textSecondary: string;
    textMuted: string;
    textInverse: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    sessionActive: string;
    sessionOptimal: string;
    sessionCaution: string;
    sessionTransition: string;
    sessionInactive: string;
    hover: string;
    active: string;
    focus: string;
    borderPrimary: string;
    borderSecondary: string;
    borderAccent: string;
}
export interface SessionBackgroundStates {
    activeBg: string;
    optimalBg: string;
    activeBorder: string;
    optimalBorder: string;
}
export interface RecommendationColors {
    prioritize: {
        bg: string;
        text: string;
    };
    increase: {
        bg: string;
        text: string;
    };
    standard: {
        bg: string;
        text: string;
    };
    reduce: {
        bg: string;
        text: string;
    };
    avoid: {
        bg: string;
        text: string;
    };
}
export interface PatternQualityColors {
    excellent: {
        bg: string;
        text: string;
        border: string;
    };
    good: {
        bg: string;
        text: string;
        border: string;
    };
    average: {
        bg: string;
        text: string;
        border: string;
    };
    poor: {
        bg: string;
        text: string;
        border: string;
    };
}
export interface ThemeConfig {
    name: ThemeName;
    displayName: string;
    colors: ThemeColors;
    sessionStates: SessionBackgroundStates;
    recommendations: RecommendationColors;
    patternQuality: PatternQualityColors;
}
export interface StyledComponentProps {
    theme?: ThemeConfig;
}
export interface ProbabilityBadgeProps extends StyledComponentProps {
    confidence: ConfidenceLevel;
}
export interface SessionWindowCardProps extends StyledComponentProps {
    isActive: boolean;
    isOptimal: boolean;
}
export interface RecommendationBadgeProps extends StyledComponentProps {
    recommendation: RecommendationType;
}
export interface PatternQualityBadgeProps extends StyledComponentProps {
    rating: PatternQualityRating;
}
/**
 * CSS Variable Names used in the theme system
 */
export declare const CSS_VARIABLES: {
    readonly PRIMARY_COLOR: "--primary-color";
    readonly SECONDARY_COLOR: "--secondary-color";
    readonly ACCENT_COLOR: "--accent-color";
    readonly BG_PRIMARY: "--bg-primary";
    readonly BG_CARD: "--bg-card";
    readonly TEXT_PRIMARY: "--text-primary";
    readonly TEXT_SECONDARY: "--text-secondary";
    readonly TEXT_MUTED: "--text-muted";
    readonly TEXT_INVERSE: "--text-inverse";
    readonly SUCCESS_COLOR: "--success-color";
    readonly WARNING_COLOR: "--warning-color";
    readonly ERROR_COLOR: "--error-color";
    readonly INFO_COLOR: "--info-color";
    readonly SESSION_ACTIVE_BG: "--session-active-bg";
    readonly SESSION_OPTIMAL_BG: "--session-optimal-bg";
    readonly SESSION_ACTIVE_BORDER: "--session-active-border";
    readonly SESSION_OPTIMAL_BORDER: "--session-optimal-border";
    readonly RECOMMENDATION_PRIORITIZE_BG: "--recommendation-prioritize-bg";
    readonly RECOMMENDATION_PRIORITIZE_TEXT: "--recommendation-prioritize-text";
    readonly RECOMMENDATION_INCREASE_BG: "--recommendation-increase-bg";
    readonly RECOMMENDATION_INCREASE_TEXT: "--recommendation-increase-text";
    readonly RECOMMENDATION_STANDARD_BG: "--recommendation-standard-bg";
    readonly RECOMMENDATION_STANDARD_TEXT: "--recommendation-standard-text";
    readonly RECOMMENDATION_REDUCE_BG: "--recommendation-reduce-bg";
    readonly RECOMMENDATION_REDUCE_TEXT: "--recommendation-reduce-text";
    readonly RECOMMENDATION_AVOID_BG: "--recommendation-avoid-bg";
    readonly RECOMMENDATION_AVOID_TEXT: "--recommendation-avoid-text";
    readonly BORDER_PRIMARY: "--border-primary";
    readonly BORDER_SECONDARY: "--border-secondary";
    readonly BORDER_ACCENT: "--border-accent";
};
/**
 * Theme validation utility
 */
export declare function isValidTheme(theme: string): theme is ThemeName;
/**
 * Get CSS variable value
 */
export declare function getCSSVariable(variableName: string): string;
//# sourceMappingURL=theme.d.ts.map