/**
 * Theme System Types
 *
 * TypeScript definitions for the ADHD Trading Dashboard theme system
 * supporting Mercedes Green, F1 Official, and Dark themes.
 */
/**
 * CSS Variable Names used in the theme system
 */
export const CSS_VARIABLES = {
    // Core Colors
    PRIMARY_COLOR: '--primary-color',
    SECONDARY_COLOR: '--secondary-color',
    ACCENT_COLOR: '--accent-color',
    BG_PRIMARY: '--bg-primary',
    BG_CARD: '--bg-card',
    // Text Colors
    TEXT_PRIMARY: '--text-primary',
    TEXT_SECONDARY: '--text-secondary',
    TEXT_MUTED: '--text-muted',
    TEXT_INVERSE: '--text-inverse',
    // Status Colors
    SUCCESS_COLOR: '--success-color',
    WARNING_COLOR: '--warning-color',
    ERROR_COLOR: '--error-color',
    INFO_COLOR: '--info-color',
    // Session Background States
    SESSION_ACTIVE_BG: '--session-active-bg',
    SESSION_OPTIMAL_BG: '--session-optimal-bg',
    SESSION_ACTIVE_BORDER: '--session-active-border',
    SESSION_OPTIMAL_BORDER: '--session-optimal-border',
    // Recommendation Colors
    RECOMMENDATION_PRIORITIZE_BG: '--recommendation-prioritize-bg',
    RECOMMENDATION_PRIORITIZE_TEXT: '--recommendation-prioritize-text',
    RECOMMENDATION_INCREASE_BG: '--recommendation-increase-bg',
    RECOMMENDATION_INCREASE_TEXT: '--recommendation-increase-text',
    RECOMMENDATION_STANDARD_BG: '--recommendation-standard-bg',
    RECOMMENDATION_STANDARD_TEXT: '--recommendation-standard-text',
    RECOMMENDATION_REDUCE_BG: '--recommendation-reduce-bg',
    RECOMMENDATION_REDUCE_TEXT: '--recommendation-reduce-text',
    RECOMMENDATION_AVOID_BG: '--recommendation-avoid-bg',
    RECOMMENDATION_AVOID_TEXT: '--recommendation-avoid-text',
    // Border Colors
    BORDER_PRIMARY: '--border-primary',
    BORDER_SECONDARY: '--border-secondary',
    BORDER_ACCENT: '--border-accent',
};
/**
 * Theme validation utility
 */
export function isValidTheme(theme) {
    return ['mercedes-green', 'f1-official', 'dark'].includes(theme);
}
/**
 * Get CSS variable value
 */
export function getCSSVariable(variableName) {
    if (typeof window !== 'undefined') {
        return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
    }
    return '';
}
//# sourceMappingURL=theme.js.map