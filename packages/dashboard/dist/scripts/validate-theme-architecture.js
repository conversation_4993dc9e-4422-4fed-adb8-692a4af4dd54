#!/usr/bin/env node
/**
 * Theme Architecture Validation Script
 *
 * Validates that the theme architecture refactoring was successful:
 * 1. No hardcoded colors remain in components
 * 2. CSS variables are properly defined
 * 3. Theme switching works correctly
 * 4. No muddy layering effects
 */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Colors that should NOT appear in component files
const HARDCODED_COLORS = [
    'rgba(0, 210, 190', // Mercedes green hardcoded
    'rgba(6, 0, 239', // Racing blue hardcoded
    'rgba(59, 130, 246', // FVG blue hardcoded
    'rgba(16, 185, 129', // NWOG green hardcoded
    'rgba(220, 38, 38', // RD red hardcoded
    'rgba(168, 85, 247', // Liquidity purple hardcoded
    '#3b82f6', // Direct hex colors
    '#10b981',
    '#dc2626',
    '#a855f7',
    '#f59e0b',
];
// CSS variables that should be used instead
const REQUIRED_CSS_VARIABLES = [
    '--primary-color',
    '--session-card-bg',
    '--session-card-border',
    '--session-card-accent',
    '--session-active',
    '--session-optimal',
    '--success-color',
    '--warning-color',
    '--error-color',
    '--info-color',
    '--session-text-primary',
    '--session-text-secondary',
];
// Files to check for hardcoded colors
const COMPONENT_FILES = [
    'src/features/daily-guide/components/SessionFocus.tsx',
    'src/features/daily-guide/components/PDArrayLevels.tsx',
    'src/features/daily-guide/components/EliteIntelligence.tsx',
];
// CSS files to validate
const CSS_FILES = [
    'src/styles/theme-variables.css',
    'src/styles/components/session-cards.css',
    'src/styles/components/pd-arrays.css',
];
function validateNoHardcodedColors() {
    console.log('🔍 Checking for hardcoded colors in components...');
    let violations = [];
    COMPONENT_FILES.forEach((filePath) => {
        const fullPath = path.join(__dirname, '../../../..', filePath);
        if (!fs.existsSync(fullPath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return;
        }
        const content = fs.readFileSync(fullPath, 'utf8');
        HARDCODED_COLORS.forEach((color) => {
            if (content.includes(color)) {
                violations.push({
                    file: filePath,
                    color: color,
                    line: content.split('\n').findIndex((line) => line.includes(color)) + 1,
                });
            }
        });
    });
    if (violations.length === 0) {
        console.log('✅ No hardcoded colors found in components!');
        return true;
    }
    else {
        console.log('❌ Found hardcoded colors:');
        violations.forEach((v) => {
            console.log(`   ${v.file}:${v.line} - ${v.color}`);
        });
        return false;
    }
}
function validateCSSVariables() {
    console.log('🔍 Checking CSS variables are defined...');
    const themeVarsPath = path.join(__dirname, '../../../..', 'src/styles/theme-variables.css');
    if (!fs.existsSync(themeVarsPath)) {
        console.log('❌ theme-variables.css not found!');
        return false;
    }
    const content = fs.readFileSync(themeVarsPath, 'utf8');
    let missing = [];
    REQUIRED_CSS_VARIABLES.forEach((variable) => {
        if (!content.includes(variable)) {
            missing.push(variable);
        }
    });
    if (missing.length === 0) {
        console.log('✅ All required CSS variables are defined!');
        return true;
    }
    else {
        console.log('❌ Missing CSS variables:');
        missing.forEach((v) => console.log(`   ${v}`));
        return false;
    }
}
function validateThemeFiles() {
    console.log('🔍 Checking theme files exist...');
    let allExist = true;
    CSS_FILES.forEach((filePath) => {
        const fullPath = path.join(__dirname, '../../../..', filePath);
        if (!fs.existsSync(fullPath)) {
            console.log(`❌ Missing: ${filePath}`);
            allExist = false;
        }
        else {
            console.log(`✅ Found: ${filePath}`);
        }
    });
    return allExist;
}
function validateDataThemeAttributes() {
    console.log('🔍 Checking for data-theme attribute usage...');
    const componentFiles = [
        'src/features/daily-guide/components/SessionFocus.tsx',
        'src/features/daily-guide/components/PDArrayLevels.tsx',
    ];
    let hasDataAttributes = false;
    componentFiles.forEach((filePath) => {
        const fullPath = path.join(__dirname, '../../../..', filePath);
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            if (content.includes('data-array-type') || content.includes('data-session-state')) {
                hasDataAttributes = true;
                console.log(`✅ Found data attributes in: ${filePath}`);
            }
        }
    });
    return hasDataAttributes;
}
function validateNoMercedesCleanCSS() {
    console.log('🔍 Checking mercedes-clean-f1.css was removed...');
    const mercedesCleanPath = path.join(__dirname, '../../../..', 'src/styles/mercedes-clean-f1.css');
    if (!fs.existsSync(mercedesCleanPath)) {
        console.log('✅ mercedes-clean-f1.css successfully removed!');
        return true;
    }
    else {
        console.log('❌ mercedes-clean-f1.css still exists - should be removed');
        return false;
    }
}
function main() {
    console.log('🏎️  THEME ARCHITECTURE VALIDATION');
    console.log('=====================================\n');
    const results = [
        validateNoHardcodedColors(),
        validateCSSVariables(),
        validateThemeFiles(),
        validateDataThemeAttributes(),
        validateNoMercedesCleanCSS(),
    ];
    const passed = results.filter(Boolean).length;
    const total = results.length;
    console.log('\n=====================================');
    console.log(`🏁 VALIDATION COMPLETE: ${passed}/${total} checks passed`);
    if (passed === total) {
        console.log('🎉 Theme architecture refactoring SUCCESSFUL!');
        console.log('✅ Root causes eliminated');
        console.log('✅ Clean CSS variable system implemented');
        console.log('✅ No hardcoded colors remain');
        console.log('✅ Professional styling preserved');
        process.exit(0);
    }
    else {
        console.log('❌ Theme architecture needs attention');
        process.exit(1);
    }
}
main();
//# sourceMappingURL=validate-theme-architecture.js.map