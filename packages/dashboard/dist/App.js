import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
// React import handled by JS<PERSON> transform
import { useState, useEffect } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';
import { ThemeTestPanel } from './components/ThemeTestPanel';
import './styles/unified-theme.css';
import './styles/components/session-cards.css';
import './styles/components/pd-arrays.css';
/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
    const [showThemeTest, setShowThemeTest] = useState(false);
    // Show theme test panel on Ctrl+T
    const handleKeyDown = (e) => {
        if (e.ctrlKey && e.key === 't') {
            e.preventDefault();
            setShowThemeTest(!showThemeTest);
        }
    };
    // Add keyboard listener
    useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [showThemeTest]);
    return (_jsx(AppErrorBoundary, { children: _jsx(ThemeProvider, { initialTheme: "mercedes-green", children: _jsxs(BrowserRouter, { children: [_jsx(AppRoutes, {}), showThemeTest && _jsx(ThemeTestPanel, { onClose: () => setShowThemeTest(false) })] }) }) }));
}
export default App;
//# sourceMappingURL=App.js.map