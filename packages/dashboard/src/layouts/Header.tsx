/**
 * Header Component
 *
 * This component displays the application header with navigation controls.
 */

import React from 'react';
import styled from 'styled-components';

// Header container
const HeaderContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-bottom: 1px solid var(--border-primary);
  color: ${({ theme }) => theme.colors.textPrimary};
`;

// Left section
const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

// Hamburger menu button
const MenuButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: ${({ theme }) => theme.colors.textPrimary};
  cursor: pointer;
  transition: color ${({ theme }) => theme.transitions.fast};

  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

// F1 Racing Logo
const Logo = styled.div`
  display: flex;
  align-items: center;
  margin-left: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Orbitron', 'Inter', sans-serif;

  &::before {
    content: '🏎️';
    margin-right: ${({ theme }) => theme.spacing.xs};
    font-size: 1.2em;
  }
`;

// Session Info
const SessionInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: ${({ theme }) => theme.spacing.md};

  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    display: none;
  }
`;

// Session Title
const SessionTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1;
`;

// Session Year
const SessionYear = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.primary};
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1;
`;

// Right section
const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`;

// User menu
const UserMenu = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

// Avatar
const Avatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
`;

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

/**
 * Header Component - F1 Racing Style
 */
const Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {
  return (
    <HeaderContainer>
      <LeftSection>
        <MenuButton onClick={toggleSidebar} aria-label="Toggle sidebar">
          {sidebarOpen ? <span>☰</span> : <span>☰</span>}
        </MenuButton>
        <Logo>TRADING</Logo>
        <SessionInfo>
          <SessionTitle>2025 SESSION 1</SessionTitle>
          <SessionYear>LIVE DASHBOARD</SessionYear>
        </SessionInfo>
      </LeftSection>

      <RightSection>
        <UserMenu>
          <Avatar>JD</Avatar>
        </UserMenu>
      </RightSection>
    </HeaderContainer>
  );
};

export default Header;
