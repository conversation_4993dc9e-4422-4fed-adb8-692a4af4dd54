/**
 * F1GuideContainer Component
 * 
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * Main orchestrator for daily guide with F1 container pattern.
 * 
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense } from 'react';
import styled from 'styled-components';
import { useDailyGuide } from '../hooks';
import { F1GuideHeader } from './F1GuideHeader';
import { F1GuideTabs } from './F1GuideTabs';
import { useGuideNavigation } from './useGuideNavigation';
import { GuideTabContentRenderer } from './guideTabConfig';

export interface F1GuideContainerProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'overview' | 'plan' | 'levels' | 'news';
  /** Custom title */
  title?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  min-height: 100vh;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  max-width: 1400px;
  margin: 0 auto;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  flex: 1;
`;

const TabContentContainer = styled.div`
  animation: fadeIn 0.3s ease-in-out;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 400px;
`;

const RetryButton = styled.button`
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>📅</LoadingIcon>
    <LoadingText>Loading Daily Guide...</LoadingText>
  </LoadingState>
);

/**
 * ErrorFallback Component
 */
const ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <ErrorState>
    <ErrorIcon>⚠️</ErrorIcon>
    <ErrorTitle>Guide Error</ErrorTitle>
    <ErrorMessage>{error}</ErrorMessage>
    <RetryButton onClick={onRetry}>
      Try Again
    </RetryButton>
  </ErrorState>
);

/**
 * GuideContent Component
 */
const GuideContent: React.FC<F1GuideContainerProps> = ({ initialTab, title }) => {
  const {
    selectedDate,
    marketOverview,
    tradingPlan,
    keyPriceLevels,
    isLoading,
    error,
    currentDate,
    onDateChange,
    onTradingPlanItemToggle,
    onAddTradingPlanItem,
    onRemoveTradingPlanItem,
    onRefresh,
  } = useDailyGuide();
  
  const { activeTab, setActiveTab } = useGuideNavigation({
    defaultTab: initialTab || 'overview',
  });
  
  // Prepare data and handlers for tab content
  const tabContentProps = {
    activeTab,
    data: {
      marketOverview,
      tradingPlan,
      keyPriceLevels,
      selectedDate,
      currentDate,
    },
    isLoading,
    error,
    handlers: {
      onDateChange,
      onTradingPlanItemToggle,
      onAddTradingPlanItem,
      onRemoveTradingPlanItem,
      onRefresh,
    },
  };
  
  if (error) {
    return <ErrorFallback error={error} onRetry={onRefresh} />;
  }
  
  return (
    <Container>
      {/* F1 Racing Header */}
      <F1GuideHeader
        isLoading={isLoading}
        currentDate={currentDate}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onRefresh={onRefresh}
        title={title}
      />
      
      {/* F1 Racing Tabs */}
      <F1GuideTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        disabled={isLoading}
      />
      
      {/* Tab Content */}
      <ContentArea>
        <TabContentContainer>
          <Suspense fallback={<LoadingFallback />}>
            <GuideTabContentRenderer {...tabContentProps} />
          </Suspense>
        </TabContentContainer>
      </ContentArea>
    </Container>
  );
};

/**
 * F1GuideContainer Component
 * 
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const F1GuideContainer: React.FC<F1GuideContainerProps> = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <GuideContent {...props} />
    </Suspense>
  );
};

export default F1GuideContainer;
