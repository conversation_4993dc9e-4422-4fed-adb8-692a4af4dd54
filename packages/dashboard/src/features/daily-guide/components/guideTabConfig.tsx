/**
 * Guide Tab Configuration
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * Centralized configuration for guide tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different guide views
 * - Clear content mapping
 */

import React from 'react';
import styled from 'styled-components';
import { GuideTab } from './F1GuideTabs';
import { SessionFocus } from './SessionFocus';
import { EliteICTIntelligence } from './EliteICTIntelligence';
import { PDArrayLevels } from './PDArrayLevels';

export interface GuideTabConfig {
  id: GuideTab;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType<any>;
  showInMobile: boolean;
  requiresData: boolean;
}

export interface GuideTabContentProps {
  /** Current active tab */
  activeTab: GuideTab;
  /** Guide data */
  data: {
    marketOverview: any;
    tradingPlan: any;
    keyPriceLevels: any;
    selectedDate: string;
    currentDate: string;
  };
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Action handlers */
  handlers: {
    onDateChange: (date: string) => void;
    onTradingPlanItemToggle: (id: string, completed: boolean) => void;
    onAddTradingPlanItem: (item: any) => void;
    onRemoveTradingPlanItem: (id: string) => void;
    onRefresh: () => void;
  };
}

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 300px;
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
`;

const EmptyTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const EmptyMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 400px;
`;

const NewsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const NewsCard = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const NewsTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const NewsTime = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const NewsContent = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.sm || '8px'} 0 0 0;
  line-height: 1.6;
`;

/**
 * Session Focus Tab Content
 */
const OverviewTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {
  if (error) {
    return (
      <EmptyState>
        <EmptyIcon>⚠️</EmptyIcon>
        <EmptyTitle>Error Loading Session Analytics</EmptyTitle>
        <EmptyMessage>{error}</EmptyMessage>
      </EmptyState>
    );
  }

  return <SessionFocus isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />;
};

/**
 * Trading Plan Tab Content - Now Elite ICT Intelligence System
 */
const PlanTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {
  if (error) {
    return (
      <EmptyState>
        <EmptyIcon>⚠️</EmptyIcon>
        <EmptyTitle>Error Loading Elite ICT Intelligence</EmptyTitle>
        <EmptyMessage>{error}</EmptyMessage>
      </EmptyState>
    );
  }

  return (
    <EliteICTIntelligence isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />
  );
};

/**
 * PD Array Levels Tab Content - ICT-Focused Intelligence
 */
const LevelsTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {
  if (error) {
    return (
      <EmptyState>
        <EmptyIcon>⚠️</EmptyIcon>
        <EmptyTitle>Error Loading PD Array Intelligence</EmptyTitle>
        <EmptyMessage>{error}</EmptyMessage>
      </EmptyState>
    );
  }

  return <PDArrayLevels isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />;
};

/**
 * Market News Tab Content
 */
const NewsTabContent: React.FC<GuideTabContentProps> = ({ isLoading }) => {
  if (isLoading) {
    return (
      <EmptyState>
        <EmptyIcon>📰</EmptyIcon>
        <EmptyTitle>Loading Market News</EmptyTitle>
        <EmptyMessage>Fetching the latest market updates...</EmptyMessage>
      </EmptyState>
    );
  }

  // Mock news data for now
  const mockNews = [
    {
      id: 1,
      title: 'Federal Reserve Maintains Interest Rates',
      time: '2 hours ago',
      content:
        'The Federal Reserve announced it will maintain current interest rates, citing ongoing economic stability and controlled inflation metrics.',
    },
    {
      id: 2,
      title: 'Tech Sector Shows Strong Pre-Market Activity',
      time: '4 hours ago',
      content:
        'Major technology stocks are showing positive momentum in pre-market trading, with several companies reporting better-than-expected earnings.',
    },
    {
      id: 3,
      title: 'Oil Prices Stabilize After Recent Volatility',
      time: '6 hours ago',
      content:
        'Crude oil prices have stabilized following recent geopolitical tensions, with WTI trading within expected ranges.',
    },
  ];

  return (
    <NewsContainer>
      {mockNews.map((news) => (
        <NewsCard key={news.id}>
          <NewsTitle>{news.title}</NewsTitle>
          <NewsTime>{news.time}</NewsTime>
          <NewsContent>{news.content}</NewsContent>
        </NewsCard>
      ))}
    </NewsContainer>
  );
};

/**
 * Tab configuration with components and metadata
 */
export const GUIDE_TAB_CONFIG: Record<GuideTab, GuideTabConfig> = {
  overview: {
    id: 'overview',
    title: 'Session Focus',
    description: 'Personalized session analytics and trading recommendations',
    icon: '🎯',
    component: OverviewTabContent,
    showInMobile: true,
    requiresData: true,
  },
  plan: {
    id: 'plan',
    title: 'Elite Intelligence',
    description:
      'Advanced ICT trading intelligence with model selection, pattern scoring, and session analysis',
    icon: '🧠',
    component: PlanTabContent,
    showInMobile: true,
    requiresData: false,
  },
  levels: {
    id: 'levels',
    title: 'PD Array Levels',
    description: 'ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis',
    icon: '🎯',
    component: LevelsTabContent,
    showInMobile: true,
    requiresData: false,
  },
  news: {
    id: 'news',
    title: 'Market News',
    description: 'Latest market news and economic events',
    icon: '📰',
    component: NewsTabContent,
    showInMobile: false,
    requiresData: false,
  },
};

/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId: GuideTab): GuideTabConfig => {
  return GUIDE_TAB_CONFIG[tabId];
};

/**
 * Get all tab configurations
 */
export const getAllTabConfigs = (): GuideTabConfig[] => {
  return Object.values(GUIDE_TAB_CONFIG);
};

/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = (): GuideTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.showInMobile);
};

/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = (): GuideTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.requiresData);
};

/**
 * Tab Content Renderer Component
 */
export const GuideTabContentRenderer: React.FC<GuideTabContentProps> = (props) => {
  const { activeTab } = props;
  const config = getTabConfig(activeTab);

  if (!config) {
    return (
      <EmptyState>
        <EmptyIcon>❌</EmptyIcon>
        <EmptyTitle>Unknown Tab</EmptyTitle>
        <EmptyMessage>Tab "{activeTab}" not found.</EmptyMessage>
      </EmptyState>
    );
  }

  const TabComponent = config.component;

  return (
    <div id={`guide-panel-${activeTab}`} role="tabpanel" aria-labelledby={`guide-tab-${activeTab}`}>
      <TabComponent {...props} />
    </div>
  );
};

export default GuideTabContentRenderer;
