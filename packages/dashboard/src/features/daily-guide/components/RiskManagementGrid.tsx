/**
 * RiskManagementGrid Component
 * 
 * Focused component for displaying risk management metrics.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */

import React from 'react';
import styled from 'styled-components';
import { RiskManagement } from '../types';

export interface RiskManagementGridProps {
  /** The risk management data */
  riskManagement: RiskManagement;
  /** Custom className */
  className?: string;
}

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  margin-top: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const GridItem = styled.div`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background-color: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
  }
`;

const Label = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const Value = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Icon = styled.span`
  font-size: 1.2em;
  opacity: 0.8;
`;

const PercentageValue = styled(Value)<{ isHigh?: boolean }>`
  color: ${({ isHigh }) => isHigh ? 'var(--error-color)' : 'var(--success-color)'};
`;

const NumberValue = styled(Value)`
  color: var(--info-color);
`;

const TextValue = styled(Value)`
  color: #8b5cf6;
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  text-transform: capitalize;
`;

/**
 * Format percentage values with appropriate styling
 */
const formatPercentage = (value: number): { formatted: string; isHigh: boolean } => {
  const formatted = `${value}%`;
  const isHigh = value > 5; // Consider >5% as high risk
  return { formatted, isHigh };
};

/**
 * Format number values with appropriate styling
 */
const formatNumber = (value: number): string => {
  return value.toString();
};

/**
 * RiskManagementGrid Component
 * 
 * Displays risk management metrics in a responsive grid layout
 * with F1-themed styling and hover effects.
 */
export const RiskManagementGrid: React.FC<RiskManagementGridProps> = ({
  riskManagement,
  className,
}) => {
  const maxRiskPerTrade = formatPercentage(riskManagement.maxRiskPerTrade);
  const maxDailyLoss = formatPercentage(riskManagement.maxDailyLoss);

  return (
    <Grid className={className}>
      <GridItem>
        <Label>
          <Icon>⚠️</Icon>
          Max Risk Per Trade
        </Label>
        <PercentageValue isHigh={maxRiskPerTrade.isHigh}>
          {maxRiskPerTrade.formatted}
        </PercentageValue>
      </GridItem>

      <GridItem>
        <Label>
          <Icon>🛡️</Icon>
          Max Daily Loss
        </Label>
        <PercentageValue isHigh={maxDailyLoss.isHigh}>
          {maxDailyLoss.formatted}
        </PercentageValue>
      </GridItem>

      <GridItem>
        <Label>
          <Icon>📊</Icon>
          Max Trades
        </Label>
        <NumberValue>
          {formatNumber(riskManagement.maxTrades)}
        </NumberValue>
      </GridItem>

      <GridItem>
        <Label>
          <Icon>💰</Icon>
          Position Sizing
        </Label>
        <TextValue>
          {riskManagement.positionSizing}
        </TextValue>
      </GridItem>
    </Grid>
  );
};

export default RiskManagementGrid;
