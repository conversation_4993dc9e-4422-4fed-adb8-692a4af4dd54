/**
 * TradingPlanContainer Component
 * 
 * REFACTORED: Main orchestrator for the trading plan feature
 * Follows the proven TradeAnalysis/TradingDashboard architecture pattern.
 * 
 * BENEFITS:
 * - Clean separation of concerns
 * - Focused components for each responsibility
 * - Reusable form patterns
 * - F1 racing theme consistency
 */

import React, { Suspense } from 'react';
import styled from 'styled-components';
import { Card, LoadingSpinner } from '@adhd-trading-dashboard/shared';
import { TradingPlan as TradingPlanType, TradingPlanItem } from '../types';
import { TradingPlanHeader } from './TradingPlanHeader';
import { PlanItemsList } from './PlanItemsList';
import { RiskManagementGrid } from './RiskManagementGrid';
import { AddItemForm } from './AddItemForm';
import { useTradingPlanForm } from '../hooks/useTradingPlanForm';

export interface TradingPlanContainerProps {
  /** The trading plan data */
  tradingPlan: TradingPlanType | null;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when a trading plan item is toggled */
  onItemToggle?: (id: string, completed: boolean) => void;
  /** Function called when a trading plan item is added */
  onItemAdd?: (item: TradingPlanItem) => void;
  /** Function called when a trading plan item is removed */
  onItemRemove?: (id: string) => void;
  /** Additional class name */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const Section = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const SectionTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const Notes = styled.div`
  padding: ${({ theme }) => theme.spacing?.md || '12px'};
  background-color: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  line-height: 1.5;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  white-space: pre-wrap;
  border: 1px solid var(--border-primary);
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-style: italic;
`;

const ErrorState = styled.div`
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

/**
 * Loading Fallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingSpinner size="md" />
    <div style={{ color: 'var(--text-secondary)' }}>Loading Trading Plan...</div>
  </LoadingState>
);

/**
 * TradingPlanContainer Component
 * 
 * Main container that orchestrates all trading plan functionality
 * with proper error handling and loading states.
 */
export const TradingPlanContainer: React.FC<TradingPlanContainerProps> = ({
  tradingPlan,
  isLoading = false,
  error = null,
  onItemToggle,
  onItemAdd,
  onItemRemove,
  className,
}) => {
  const {
    showAddForm,
    setShowAddForm,
    newItem,
    setNewItem,
    handleAddItem,
    resetForm
  } = useTradingPlanForm(onItemAdd);

  // Loading state
  if (isLoading) {
    return (
      <Card title="Trading Plan">
        <Suspense fallback={<LoadingFallback />}>
          <LoadingFallback />
        </Suspense>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card title="Trading Plan">
        <ErrorState>
          <div>❌ Error loading trading plan</div>
          <div>{error}</div>
        </ErrorState>
      </Card>
    );
  }

  // Empty state
  if (!tradingPlan) {
    return (
      <Card title="Trading Plan">
        <EmptyState>
          No trading plan available.
          {onItemAdd && (
            <div style={{ marginTop: '16px' }}>
              <button
                onClick={() => setShowAddForm(true)}
                style={{
                  padding: '8px 16px',
                  background: 'var(--primary-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Create Trading Plan
              </button>
            </div>
          )}
        </EmptyState>
      </Card>
    );
  }

  return (
    <Card title="Trading Plan">
      <Container className={className}>
        {/* Header with Actions */}
        <TradingPlanHeader
          onAddItem={onItemAdd ? () => setShowAddForm(true) : undefined}
          showAddForm={showAddForm}
        />

        {/* Strategy Section */}
        {tradingPlan.strategy && (
          <Section>
            <SectionTitle>Strategy</SectionTitle>
            <Notes>{tradingPlan.strategy}</Notes>
          </Section>
        )}

        {/* Action Items */}
        <Section>
          <SectionTitle>Action Items</SectionTitle>
          <PlanItemsList
            items={tradingPlan.items}
            onItemToggle={onItemToggle}
            onItemRemove={onItemRemove}
          />
        </Section>

        {/* Risk Management */}
        {tradingPlan.riskManagement && (
          <Section>
            <SectionTitle>Risk Management</SectionTitle>
            <RiskManagementGrid riskManagement={tradingPlan.riskManagement} />
          </Section>
        )}

        {/* Notes */}
        {tradingPlan.notes && (
          <Section>
            <SectionTitle>Notes</SectionTitle>
            <Notes>{tradingPlan.notes}</Notes>
          </Section>
        )}

        {/* Add Item Form */}
        {showAddForm && (
          <Suspense fallback={<div>Loading form...</div>}>
            <AddItemForm
              newItem={newItem}
              setNewItem={setNewItem}
              onSubmit={handleAddItem}
              onCancel={() => {
                setShowAddForm(false);
                resetForm();
              }}
            />
          </Suspense>
        )}
      </Container>
    </Card>
  );
};

export default TradingPlanContainer;
