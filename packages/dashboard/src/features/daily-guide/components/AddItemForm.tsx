/**
 * AddItemForm Component
 * 
 * Focused form component for adding new trading plan items.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */

import React from 'react';
import styled from 'styled-components';
import { Button, Input, FormField } from '@adhd-trading-dashboard/shared';
import { TradingPlanItem, TradingPlanPriority } from '../types';

export interface AddItemFormProps {
  /** The new item being created */
  newItem: Omit<TradingPlanItem, 'id'>;
  /** Function to update the new item */
  setNewItem: (item: Omit<TradingPlanItem, 'id'>) => void;
  /** Function called when form is submitted */
  onSubmit: (e: React.FormEvent) => void;
  /** Function called when form is cancelled */
  onCancel: () => void;
  /** Custom className */
  className?: string;
}

const FormContainer = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background-color: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  position: relative;
  
  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark), var(--primary-color));
    border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'} ${({ theme }) => theme.borderRadius?.md || '6px'} 0 0;
  }
`;

const FormHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  margin-bottom: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const FormTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const FormIcon = styled.span`
  font-size: 1.2em;
`;

const PrioritySelect = styled.select`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid var(--border-primary);
  background-color: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  width: 100%;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
  }
  
  option {
    background-color: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '12px'};
  border-top: 1px solid var(--border-primary);
`;

const CancelButton = styled(Button)`
  background: transparent;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  border: 1px solid var(--border-primary);
  
  &:hover {
    background: var(--border-primary);
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;

const SubmitButton = styled(Button)`
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  min-width: 100px;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: var(--border-primary);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
  }
`;

/**
 * AddItemForm Component
 * 
 * Form for adding new trading plan action items with validation
 * and F1-themed styling.
 */
export const AddItemForm: React.FC<AddItemFormProps> = ({
  newItem,
  setNewItem,
  onSubmit,
  onCancel,
  className,
}) => {
  const isValid = newItem.description.trim().length > 0;

  return (
    <FormContainer onSubmit={onSubmit} className={className}>
      <FormHeader>
        <FormIcon>➕</FormIcon>
        <FormTitle>Add Action Item</FormTitle>
      </FormHeader>

      <FormField label="Description">
        <Input
          value={newItem.description}
          onChange={(value) => setNewItem({ ...newItem, description: value })}
          placeholder="Enter task description (e.g., 'Review market analysis')"
          required
          fullWidth
          autoFocus
        />
      </FormField>

      <FormField label="Priority">
        <PrioritySelect
          value={newItem.priority}
          onChange={(e) =>
            setNewItem({ ...newItem, priority: e.target.value as TradingPlanPriority })
          }
        >
          <option value="high">🔴 High Priority</option>
          <option value="medium">🟡 Medium Priority</option>
          <option value="low">🟢 Low Priority</option>
        </PrioritySelect>
      </FormField>

      <FormActions>
        <CancelButton 
          type="button" 
          onClick={onCancel}
          size="small"
        >
          Cancel
        </CancelButton>
        <SubmitButton 
          type="submit" 
          disabled={!isValid}
          size="small"
        >
          Add Item
        </SubmitButton>
      </FormActions>
    </FormContainer>
  );
};

export default AddItemForm;
