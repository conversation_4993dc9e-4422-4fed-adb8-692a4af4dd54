/**
 * TradingPlanHeader Component
 * 
 * F1-themed header for Trading Plan following the proven header pattern.
 * Provides consistent branding and actions across the application.
 */

import React from 'react';
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';

export interface TradingPlanHeaderProps {
  /** Function called when add item is clicked */
  onAddItem?: () => void;
  /** Whether the add form is currently shown */
  showAddForm?: boolean;
  /** Custom className */
  className?: string;
}

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.md || '12px'} 0;
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Title = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;
  
  /* F1 Racing aesthetic */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;

const Subtitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const AddButton = styled(Button)<{ isActive?: boolean }>`
  min-width: 120px;
  background: ${({ isActive, theme }) => 
    isActive ? theme.colors?.primaryDark || 'var(--primary-dark)' : theme.colors?.primary || 'var(--primary-color)'};
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: var(--border-primary);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid var(--success-color);
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  color: var(--success-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const StatusDot = styled.div`
  width: 6px;
  height: 6px;
  background: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

/**
 * TradingPlanHeader Component
 * 
 * F1-themed header that provides consistent branding and actions
 * for the Trading Plan feature.
 */
export const TradingPlanHeader: React.FC<TradingPlanHeaderProps> = ({
  onAddItem,
  showAddForm = false,
  className,
}) => {
  return (
    <HeaderContainer className={className}>
      <TitleSection>
        <Title>Daily Trading Plan</Title>
        <Subtitle>Strategy & Risk Management</Subtitle>
      </TitleSection>

      <ActionsSection>
        <StatusIndicator>
          <StatusDot />
          ACTIVE PLAN
        </StatusIndicator>
        
        {onAddItem && (
          <AddButton
            variant="primary"
            size="small"
            onClick={onAddItem}
            isActive={showAddForm}
          >
            {showAddForm ? 'Form Open' : '➕ Add Item'}
          </AddButton>
        )}
      </ActionsSection>
    </HeaderContainer>
  );
};

export default TradingPlanHeader;
