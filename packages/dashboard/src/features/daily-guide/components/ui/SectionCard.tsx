/**
 * Section Card Component
 *
 * A card component for displaying sections in the daily guide
 */

import React, { ReactNode } from 'react';
import styled from 'styled-components';

interface SectionCardProps {
  /** The title of the section */
  title: string;
  /** The content of the section */
  children: ReactNode;
  /** Whether the section is in a loading state */
  isLoading?: boolean;
  /** Whether the section has an error */
  hasError?: boolean;
  /** Error message to display */
  errorMessage?: string;
  /** Action button to display in the header */
  actionButton?: ReactNode;
}

const Container = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid var(--border-primary);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Title = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const Content = styled.div``;

const LoadingPlaceholder = styled.div`
  height: 200px;
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const ErrorContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.error + '10'};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.error};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

export const SectionCard: React.FC<SectionCardProps> = ({
  title,
  children,
  isLoading = false,
  hasError = false,
  errorMessage = 'An error occurred while loading data',
  actionButton,
}) => {
  return (
    <Container>
      <Header>
        <Title>{title}</Title>
        {actionButton && actionButton}
      </Header>

      {hasError && (
        <ErrorContainer>
          <p>{errorMessage}</p>
        </ErrorContainer>
      )}

      {isLoading ? (
        <LoadingPlaceholder>Loading data...</LoadingPlaceholder>
      ) : (
        <Content>{children}</Content>
      )}
    </Container>
  );
};
