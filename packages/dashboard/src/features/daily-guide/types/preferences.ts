/**
 * Preferences Types
 * 
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * User preferences and configuration types.
 * 
 * BENEFITS:
 * - Isolated user preferences logic
 * - Easy to extend and maintain
 * - Clear configuration management
 * - F1 theme integration ready
 */

import type { RiskManagement } from './trading';

/**
 * Daily Guide User Preferences
 * 
 * User-configurable preferences for the daily guide.
 */
export interface DailyGuidePreferences {
  /** Default watchlist symbols */
  defaultWatchlist: string[];
  /** Whether to show economic events */
  showEconomicEvents: boolean;
  /** Whether to show market news */
  showMarketNews: boolean;
  /** The default risk management settings */
  defaultRiskManagement: RiskManagement;
  /** Theme preferences */
  theme: DailyGuideThemePreferences;
  /** Display preferences */
  display: DailyGuideDisplayPreferences;
}

/**
 * Daily Guide Theme Preferences
 * 
 * F1 racing theme specific preferences.
 */
export interface DailyGuideThemePreferences {
  /** Whether to use F1 racing theme */
  useF1Theme: boolean;
  /** Primary accent color (F1 red by default) */
  primaryColor: string;
  /** Secondary accent color (F1 silver by default) */
  secondaryColor: string;
  /** Whether to show live indicators */
  showLiveIndicators: boolean;
  /** Animation preferences */
  enableAnimations: boolean;
}

/**
 * Daily Guide Display Preferences
 * 
 * Display and layout preferences.
 */
export interface DailyGuideDisplayPreferences {
  /** Default view mode */
  defaultView: 'compact' | 'detailed' | 'cards';
  /** Whether to auto-refresh data */
  autoRefresh: boolean;
  /** Auto-refresh interval in seconds */
  autoRefreshInterval: number;
  /** Whether to show timestamps */
  showTimestamps: boolean;
  /** Date format preference */
  dateFormat: 'US' | 'EU' | 'ISO';
  /** Time format preference */
  timeFormat: '12h' | '24h';
}

/**
 * Default Daily Guide Preferences
 * 
 * Provides sensible defaults with F1 theme integration.
 */
export const DEFAULT_DAILY_GUIDE_PREFERENCES: DailyGuidePreferences = {
  defaultWatchlist: ['SPY', 'QQQ', 'IWM', 'MNQ', 'ES'],
  showEconomicEvents: true,
  showMarketNews: true,
  defaultRiskManagement: {
    maxRiskPerTrade: 1,
    maxDailyLoss: 500,
    maxTrades: 5,
    positionSizing: 'fixed-dollar',
  },
  theme: {
    useF1Theme: true,
    primaryColor: 'var(--primary-color)', // F1 red
    secondaryColor: 'var(--border-primary)', // F1 silver
    showLiveIndicators: true,
    enableAnimations: true,
  },
  display: {
    defaultView: 'detailed',
    autoRefresh: true,
    autoRefreshInterval: 300, // 5 minutes
    showTimestamps: true,
    dateFormat: 'US',
    timeFormat: '12h',
  },
};

/**
 * Preferences Validation
 * 
 * Validates user preferences for consistency.
 */
export function validatePreferences(preferences: Partial<DailyGuidePreferences>): string[] {
  const errors: string[] = [];
  
  // Validate watchlist
  if (preferences.defaultWatchlist) {
    if (preferences.defaultWatchlist.length === 0) {
      errors.push('Default watchlist cannot be empty');
    }
    
    // Check for valid symbols (basic validation)
    const invalidSymbols = preferences.defaultWatchlist.filter(
      symbol => !/^[A-Z]{1,5}$/.test(symbol)
    );
    if (invalidSymbols.length > 0) {
      errors.push(`Invalid symbols in watchlist: ${invalidSymbols.join(', ')}`);
    }
  }
  
  // Validate display preferences
  if (preferences.display?.autoRefreshInterval) {
    if (preferences.display.autoRefreshInterval < 30) {
      errors.push('Auto-refresh interval must be at least 30 seconds');
    }
    if (preferences.display.autoRefreshInterval > 3600) {
      errors.push('Auto-refresh interval cannot exceed 1 hour');
    }
  }
  
  // Validate theme colors
  if (preferences.theme?.primaryColor) {
    if (!/^#[0-9a-fA-F]{6}$/.test(preferences.theme.primaryColor)) {
      errors.push('Primary color must be a valid hex color');
    }
  }
  
  if (preferences.theme?.secondaryColor) {
    if (!/^#[0-9a-fA-F]{6}$/.test(preferences.theme.secondaryColor)) {
      errors.push('Secondary color must be a valid hex color');
    }
  }
  
  return errors;
}

/**
 * Preferences Storage Key
 * 
 * Local storage key for persisting preferences.
 */
export const PREFERENCES_STORAGE_KEY = 'adhd-trading-dashboard:daily-guide:preferences';

/**
 * Load Preferences
 * 
 * Loads preferences from local storage with fallback to defaults.
 */
export function loadPreferences(): DailyGuidePreferences {
  try {
    const stored = localStorage.getItem(PREFERENCES_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Merge with defaults to ensure all properties exist
      return {
        ...DEFAULT_DAILY_GUIDE_PREFERENCES,
        ...parsed,
        theme: {
          ...DEFAULT_DAILY_GUIDE_PREFERENCES.theme,
          ...parsed.theme,
        },
        display: {
          ...DEFAULT_DAILY_GUIDE_PREFERENCES.display,
          ...parsed.display,
        },
        defaultRiskManagement: {
          ...DEFAULT_DAILY_GUIDE_PREFERENCES.defaultRiskManagement,
          ...parsed.defaultRiskManagement,
        },
      };
    }
  } catch (error) {
    console.warn('Failed to load daily guide preferences:', error);
  }
  
  return DEFAULT_DAILY_GUIDE_PREFERENCES;
}

/**
 * Save Preferences
 * 
 * Saves preferences to local storage.
 */
export function savePreferences(preferences: DailyGuidePreferences): void {
  try {
    localStorage.setItem(PREFERENCES_STORAGE_KEY, JSON.stringify(preferences));
  } catch (error) {
    console.error('Failed to save daily guide preferences:', error);
  }
}

/**
 * Reset Preferences
 * 
 * Resets preferences to defaults.
 */
export function resetPreferences(): DailyGuidePreferences {
  try {
    localStorage.removeItem(PREFERENCES_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear preferences from storage:', error);
  }
  
  return DEFAULT_DAILY_GUIDE_PREFERENCES;
}
