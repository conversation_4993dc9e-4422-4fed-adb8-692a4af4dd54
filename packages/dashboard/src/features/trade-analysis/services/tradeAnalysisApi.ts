/**
 * Trade Analysis API
 *
 * API functions for the trade analysis feature
 * Updated to support both real data and mock data
 */

import { Trade } from '@adhd-trading-dashboard/shared';
import {
  TradeAnalysisData,
  TradeFilters,
  TradeDirection,
  TradeStatus,
  TradeTimeframe,
  TradingSession,
  PerformanceMetrics,
  CategoryPerformance,
  TimePerformance,
} from '../types';

// Import real data service
import {
  fetchRealTradeAnalysisData,
  getFilterOptions,
  getTradeStatistics,
} from './realTradeAnalysisApi';

/**
 * Feature flag to control whether to use real data or mock data
 * Set to true to use real trade data from IndexedDB
 * Set to false to use mock data for development/testing
 */
const USE_REAL_DATA = true;

/**
 * Fetch trade analysis data
 *
 * Uses real trade data from IndexedDB when USE_REAL_DATA is true,
 * otherwise falls back to mock data generation.
 */
export const fetchTradeAnalysisData = async (filters: TradeFilters): Promise<TradeAnalysisData> => {
  console.log(`🔄 Fetching trade analysis data (Real Data: ${USE_REAL_DATA})`);

  if (USE_REAL_DATA) {
    try {
      return await fetchRealTradeAnalysisData(filters);
    } catch (error) {
      console.error('❌ Error fetching real trade data, falling back to mock data:', error);
      // Fall back to mock data if real data fails
      return generateMockData(filters);
    }
  } else {
    // Simulate API call delay for mock data
    await new Promise(resolve => setTimeout(resolve, 800));

    // Randomly decide if we should throw an error (for testing error handling)
    const shouldError = Math.random() < 0.05; // 5% chance of error
    if (shouldError) {
      throw new Error('Failed to fetch trade analysis data');
    }

    return generateMockData(filters);
  }
};

/**
 * Get available filter options
 */
export const fetchFilterOptions = async () => {
  if (USE_REAL_DATA) {
    return await getFilterOptions();
  } else {
    // Return mock filter options
    return {
      symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NFLX', 'NVDA'],
      strategies: [
        'Breakout',
        'Reversal',
        'Trend Following',
        'Gap and Go',
        'VWAP Bounce',
        'Support/Resistance',
      ],
      sessions: ['pre-market', 'regular', 'after-hours'],
      setups: ['FVG', 'OB', 'Liquidity Sweep', 'BOS', 'CHoCH'],
    };
  }
};

/**
 * Get trade statistics for dashboard
 */
export const fetchTradeStatistics = async () => {
  if (USE_REAL_DATA) {
    return await getTradeStatistics();
  } else {
    // Return mock statistics
    return {
      totalTrades: 150,
      todayTrades: 3,
      weekTrades: 12,
      monthTrades: 45,
      lastTradeDate: new Date().toISOString(),
    };
  }
};

/**
 * Generate mock data for development and testing
 */
const generateMockData = (filters: TradeFilters): TradeAnalysisData => {
  // Generate trades
  const trades = generateMockTrades(filters);

  // Calculate metrics
  const metrics = calculateMetrics(trades);

  // Generate performance by category
  const symbolPerformance = calculateCategoryPerformance(trades, 'symbol');
  const strategyPerformance = calculateCategoryPerformance(trades, 'strategy');
  const timeframePerformance = calculateCategoryPerformance(trades, 'timeframe');
  const sessionPerformance = calculateCategoryPerformance(trades, 'session');

  // Generate performance by time
  const timeOfDayPerformance = calculateTimePerformance(trades, 'timeOfDay');
  const dayOfWeekPerformance = calculateTimePerformance(trades, 'dayOfWeek');

  return {
    trades,
    metrics,
    symbolPerformance,
    strategyPerformance,
    timeframePerformance,
    sessionPerformance,
    timeOfDayPerformance,
    dayOfWeekPerformance,
  };
};

/**
 * Generate mock trades
 */
const generateMockTrades = (filters: TradeFilters): Trade[] => {
  const { dateRange } = filters;
  const startDate = new Date(dateRange.startDate);
  const endDate = new Date(dateRange.endDate);

  // Calculate number of days in the date range
  const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Generate between 1-5 trades per day
  const numTrades = Math.max(1, daysDiff) * (1 + Math.floor(Math.random() * 5));

  const trades: Trade[] = [];
  const symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NFLX', 'NVDA'];
  const strategies = [
    'Breakout',
    'Reversal',
    'Trend Following',
    'Gap and Go',
    'VWAP Bounce',
    'Support/Resistance',
  ];
  const timeframes: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];
  const sessions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];
  const tags = [
    'High Volume',
    'Low Float',
    'Earnings',
    'News',
    'Technical',
    'Momentum',
    'Oversold',
    'Overbought',
  ];

  for (let i = 0; i < numTrades; i++) {
    // Generate random trade date within the range
    const tradeDate = new Date(
      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
    );

    // Generate entry time (market hours)
    const entryHour = 9 + Math.floor(Math.random() * 7); // 9 AM to 4 PM
    const entryMinute = Math.floor(Math.random() * 60);
    const entryTime = new Date(tradeDate);
    entryTime.setHours(entryHour, entryMinute, 0, 0);

    // Generate exit time (after entry time)
    const durationMinutes = 5 + Math.floor(Math.random() * 120); // 5 to 120 minutes
    const exitTime = new Date(entryTime.getTime() + durationMinutes * 60 * 1000);

    // Generate random trade details
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const direction: TradeDirection = Math.random() > 0.5 ? 'long' : 'short';
    const entryPrice = 100 + Math.random() * 900; // $100 to $1000

    // Determine if win or loss (60% win rate)
    const isWin = Math.random() < 0.6;
    const isBreakeven = !isWin && Math.random() < 0.1; // 10% of losses are breakeven

    // Calculate exit price based on win/loss
    let exitPrice, profitLoss, profitLossPercent, status: TradeStatus;

    if (isBreakeven) {
      exitPrice = entryPrice + (Math.random() * 0.2 - 0.1); // Small fluctuation around entry
      status = 'breakeven';
    } else if (isWin) {
      const winPercent = 0.5 + Math.random() * 4.5; // 0.5% to 5% win
      exitPrice =
        direction === 'long'
          ? entryPrice * (1 + winPercent / 100)
          : entryPrice * (1 - winPercent / 100);
      status = 'win';
    } else {
      const lossPercent = 0.5 + Math.random() * 2.5; // 0.5% to 3% loss
      exitPrice =
        direction === 'long'
          ? entryPrice * (1 - lossPercent / 100)
          : entryPrice * (1 + lossPercent / 100);
      status = 'loss';
    }

    // Calculate P&L
    const quantity = 10 + Math.floor(Math.random() * 90); // 10 to 100 shares

    if (direction === 'long') {
      profitLoss = (exitPrice - entryPrice) * quantity;
      profitLossPercent = (exitPrice / entryPrice - 1) * 100;
    } else {
      profitLoss = (entryPrice - exitPrice) * quantity;
      profitLossPercent = (entryPrice / exitPrice - 1) * 100;
    }

    // Round to 2 decimal places
    profitLoss = Math.round(profitLoss * 100) / 100;
    profitLossPercent = Math.round(profitLossPercent * 100) / 100;

    // Generate random trade metadata
    const timeframe = timeframes[Math.floor(Math.random() * timeframes.length)];
    const session = sessions[Math.floor(Math.random() * sessions.length)];
    const strategy = strategies[Math.floor(Math.random() * strategies.length)];

    // Generate random tags (0-3 tags)
    const numTags = Math.floor(Math.random() * 4);
    const tradeTags: string[] = [];
    for (let j = 0; j < numTags; j++) {
      const tag = tags[Math.floor(Math.random() * tags.length)];
      if (!tradeTags.includes(tag)) {
        tradeTags.push(tag);
      }
    }

    // Create trade object - using any to bypass interface mismatch in deprecated mock API
    const trade: any = {
      id: `trade-${i}`,
      symbol,
      direction,
      entryPrice,
      exitPrice,
      quantity,
      entryTime: entryTime.toISOString(),
      exitTime: exitTime.toISOString(),
      status,
      profitLoss,
      profitLossPercent,
      timeframe,
      session,
      strategy,
      tags: tradeTags,
      notes: Math.random() > 0.7 ? `Sample note for ${symbol} ${direction} trade` : undefined,
    };

    trades.push(trade);
  }

  // Sort trades by date (newest first) - using any to bypass interface mismatch
  return trades.sort(
    (a: any, b: any) => new Date(b.entryTime).getTime() - new Date(a.entryTime).getTime()
  );
};

/**
 * Calculate performance metrics from trades
 */
const calculateMetrics = (trades: any[]): PerformanceMetrics => {
  const winningTrades = trades.filter(trade => trade.status === 'win');
  const losingTrades = trades.filter(trade => trade.status === 'loss');
  const breakeven = trades.filter(trade => trade.status === 'breakeven');

  const totalProfitLoss = trades.reduce((sum, trade) => sum + trade.profitLoss, 0);
  const totalWinAmount = winningTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);
  const totalLossAmount = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profitLoss, 0));

  const averageWin = winningTrades.length > 0 ? totalWinAmount / winningTrades.length : 0;

  const averageLoss = losingTrades.length > 0 ? totalLossAmount / losingTrades.length : 0;

  const largestWin =
    winningTrades.length > 0 ? Math.max(...winningTrades.map(trade => trade.profitLoss)) : 0;

  const largestLoss =
    losingTrades.length > 0 ? Math.min(...losingTrades.map(trade => trade.profitLoss)) : 0;

  // Calculate average duration in minutes
  const durations = trades.map(trade => {
    const entryTime = new Date(trade.entryTime).getTime();
    const exitTime = new Date(trade.exitTime).getTime();
    return (exitTime - entryTime) / (1000 * 60); // Convert to minutes
  });

  const averageDuration =
    durations.length > 0
      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
      : 0;

  // Calculate profit factor and expectancy
  const profitFactor =
    totalLossAmount > 0 ? totalWinAmount / totalLossAmount : totalWinAmount > 0 ? Infinity : 0;

  const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;

  const expectancy = winRate * averageWin - (1 - winRate) * averageLoss;

  return {
    totalTrades: trades.length,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    breakeven: breakeven.length,
    winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places
    averageWin: Math.round(averageWin * 100) / 100,
    averageLoss: Math.round(averageLoss * 100) / 100,
    profitFactor: Math.round(profitFactor * 100) / 100,
    totalProfitLoss: Math.round(totalProfitLoss * 100) / 100,
    largestWin: Math.round(largestWin * 100) / 100,
    largestLoss: Math.round(largestLoss * 100) / 100,
    averageDuration: Math.round(averageDuration * 100) / 100,
    expectancy: Math.round(expectancy * 100) / 100,
  };
};

/**
 * Calculate performance by category
 */
const calculateCategoryPerformance = (
  trades: any[],
  category: 'symbol' | 'strategy' | 'timeframe' | 'session'
): CategoryPerformance[] => {
  // Group trades by category
  const categories = new Map<string, any[]>();

  trades.forEach(trade => {
    const categoryValue = trade[category] as string;
    if (!categories.has(categoryValue)) {
      categories.set(categoryValue, []);
    }
    categories.get(categoryValue)!.push(trade);
  });

  // Calculate performance for each category
  const performance: CategoryPerformance[] = [];

  categories.forEach((categoryTrades, categoryValue) => {
    const winningTrades = categoryTrades.filter(trade => trade.status === 'win');
    const totalProfitLoss = categoryTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);
    const winRate = categoryTrades.length > 0 ? winningTrades.length / categoryTrades.length : 0;
    const averageProfitLoss =
      categoryTrades.length > 0 ? totalProfitLoss / categoryTrades.length : 0;

    performance.push({
      category,
      value: categoryValue,
      trades: categoryTrades.length,
      winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places
      profitLoss: Math.round(totalProfitLoss * 100) / 100,
      averageProfitLoss: Math.round(averageProfitLoss * 100) / 100,
    });
  });

  // Sort by profit/loss (descending)
  return performance.sort((a, b) => b.profitLoss - a.profitLoss);
};

/**
 * Calculate performance by time
 */
const calculateTimePerformance = (
  trades: any[],
  timeType: 'timeOfDay' | 'dayOfWeek'
): TimePerformance[] => {
  // Define time slots
  let timeSlots: string[];

  if (timeType === 'timeOfDay') {
    timeSlots = [
      '9:30-10:30',
      '10:30-11:30',
      '11:30-12:30',
      '12:30-13:30',
      '13:30-14:30',
      '14:30-15:30',
      '15:30-16:00',
    ];
  } else {
    timeSlots = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  }

  // Group trades by time slot
  const timePerformance: TimePerformance[] = timeSlots.map(timeSlot => ({
    timeSlot,
    trades: 0,
    winRate: 0,
    profitLoss: 0,
  }));

  trades.forEach(trade => {
    const entryTime = new Date(trade.entryTime);
    let slotIndex: number;

    if (timeType === 'timeOfDay') {
      // Get hour and determine slot
      const hour = entryTime.getHours();
      const minute = entryTime.getMinutes();
      const timeValue = hour + minute / 60;

      if (timeValue < 9.5 || timeValue >= 16) {
        return; // Outside regular market hours
      }

      if (timeValue < 10.5) slotIndex = 0;
      else if (timeValue < 11.5) slotIndex = 1;
      else if (timeValue < 12.5) slotIndex = 2;
      else if (timeValue < 13.5) slotIndex = 3;
      else if (timeValue < 14.5) slotIndex = 4;
      else if (timeValue < 15.5) slotIndex = 5;
      else slotIndex = 6;
    } else {
      // Get day of week (0 = Sunday, 1 = Monday, etc.)
      const dayOfWeek = entryTime.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        return; // Weekend
      }
      slotIndex = dayOfWeek - 1;
    }

    // Update time slot data
    const slot = timePerformance[slotIndex];
    slot.trades++;
    slot.profitLoss += trade.profitLoss;

    // Recalculate win rate
    const slotTrades = trades.filter(t => {
      const tEntryTime = new Date(t.entryTime);
      if (timeType === 'timeOfDay') {
        const tHour = tEntryTime.getHours();
        const tMinute = tEntryTime.getMinutes();
        const tTimeValue = tHour + tMinute / 60;

        if (slotIndex === 0) return tTimeValue >= 9.5 && tTimeValue < 10.5;
        if (slotIndex === 1) return tTimeValue >= 10.5 && tTimeValue < 11.5;
        if (slotIndex === 2) return tTimeValue >= 11.5 && tTimeValue < 12.5;
        if (slotIndex === 3) return tTimeValue >= 12.5 && tTimeValue < 13.5;
        if (slotIndex === 4) return tTimeValue >= 13.5 && tTimeValue < 14.5;
        if (slotIndex === 5) return tTimeValue >= 14.5 && tTimeValue < 15.5;
        if (slotIndex === 6) return tTimeValue >= 15.5 && tTimeValue < 16;
        return false;
      } else {
        return tEntryTime.getDay() === slotIndex + 1;
      }
    });

    const winningSlotTrades = slotTrades.filter(t => t.status === 'win');
    slot.winRate = slotTrades.length > 0 ? (winningSlotTrades.length / slotTrades.length) * 100 : 0;
  });

  // Round numbers and filter out empty slots
  return timePerformance
    .filter(slot => slot.trades > 0)
    .map(slot => ({
      ...slot,
      winRate: Math.round(slot.winRate * 100) / 100,
      profitLoss: Math.round(slot.profitLoss * 100) / 100,
    }));
};
