/**
 * Performance Cache Service
 *
 * This service provides caching and optimization for trade analysis calculations
 * to improve performance when dealing with large datasets.
 */

import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import {
  PerformanceMetrics,
  CategoryPerformance,
  TimePerformance,
  EquityPoint,
  DistributionBar,
} from '../types';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  hash: string;
}

interface CacheConfig {
  maxAge: number; // Cache expiry time in milliseconds
  maxSize: number; // Maximum number of cache entries
}

/**
 * Performance Cache Manager
 */
class PerformanceCacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig = {
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxSize: 100, // 100 cache entries
  };

  /**
   * Generate a hash key for cache lookup
   */
  private generateCacheKey(trades: CompleteTradeData[], operation: string, params?: any): string {
    // Create a simple hash based on trade IDs, operation, and parameters
    const tradeIds = trades
      .map(t => t.trade.id)
      .sort()
      .join(',');
    const paramsStr = params ? JSON.stringify(params) : '';
    return `${operation}:${tradeIds}:${paramsStr}`;
  }

  /**
   * Get cached result if available and not expired
   */
  private getCached<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > this.config.maxAge) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  /**
   * Store result in cache
   */
  private setCached<T>(key: string, data: T): void {
    // Clean up old entries if cache is full
    if (this.cache.size >= this.config.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hash: key,
    });
  }

  /**
   * Clear all cache entries
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.maxAge) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry: number | null;
  } {
    let oldestTimestamp: number | null = null;

    for (const entry of this.cache.values()) {
      if (oldestTimestamp === null || entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: 0, // TODO: Implement hit rate tracking
      oldestEntry: oldestTimestamp,
    };
  }

  /**
   * Cached performance metrics calculation
   */
  async getCachedPerformanceMetrics(
    trades: CompleteTradeData[],
    calculator: (trades: CompleteTradeData[]) => PerformanceMetrics
  ): Promise<PerformanceMetrics> {
    const cacheKey = this.generateCacheKey(trades, 'performance-metrics');

    // Try to get from cache first
    const cached = this.getCached<PerformanceMetrics>(cacheKey);
    if (cached) {
      console.log('📊 Performance metrics retrieved from cache');
      return cached;
    }

    // Calculate and cache the result
    console.log('🧮 Calculating performance metrics...');
    const result = calculator(trades);
    this.setCached(cacheKey, result);

    return result;
  }

  /**
   * Cached category performance calculation
   */
  async getCachedCategoryPerformance(
    trades: CompleteTradeData[],
    category: string,
    calculator: (trades: CompleteTradeData[], category: any) => CategoryPerformance[]
  ): Promise<CategoryPerformance[]> {
    const cacheKey = this.generateCacheKey(trades, 'category-performance', { category });

    // Try to get from cache first
    const cached = this.getCached<CategoryPerformance[]>(cacheKey);
    if (cached) {
      console.log(`📈 Category performance (${category}) retrieved from cache`);
      return cached;
    }

    // Calculate and cache the result
    console.log(`📈 Calculating category performance for ${category}...`);
    const result = calculator(trades, category as any);
    this.setCached(cacheKey, result);

    return result;
  }

  /**
   * Cached time performance calculation
   */
  async getCachedTimePerformance(
    trades: CompleteTradeData[],
    timeType: string,
    calculator: (trades: CompleteTradeData[], timeType: any) => TimePerformance[]
  ): Promise<TimePerformance[]> {
    const cacheKey = this.generateCacheKey(trades, 'time-performance', { timeType });

    // Try to get from cache first
    const cached = this.getCached<TimePerformance[]>(cacheKey);
    if (cached) {
      console.log(`⏰ Time performance (${timeType}) retrieved from cache`);
      return cached;
    }

    // Calculate and cache the result
    console.log(`⏰ Calculating time performance for ${timeType}...`);
    const result = calculator(trades, timeType as any);
    this.setCached(cacheKey, result);

    return result;
  }

  /**
   * Cached equity curve calculation
   */
  async getCachedEquityCurve(
    trades: CompleteTradeData[],
    calculator: (trades: CompleteTradeData[]) => EquityPoint[]
  ): Promise<EquityPoint[]> {
    const cacheKey = this.generateCacheKey(trades, 'equity-curve');

    // Try to get from cache first
    const cached = this.getCached<EquityPoint[]>(cacheKey);
    if (cached) {
      console.log('📊 Equity curve retrieved from cache');
      return cached;
    }

    // Calculate and cache the result
    console.log('📊 Calculating equity curve...');
    const result = calculator(trades);
    this.setCached(cacheKey, result);

    return result;
  }

  /**
   * Cached distribution data calculation
   */
  async getCachedDistributionData(
    trades: CompleteTradeData[],
    calculator: (trades: CompleteTradeData[]) => DistributionBar[]
  ): Promise<DistributionBar[]> {
    const cacheKey = this.generateCacheKey(trades, 'distribution-data');

    // Try to get from cache first
    const cached = this.getCached<DistributionBar[]>(cacheKey);
    if (cached) {
      console.log('📊 Distribution data retrieved from cache');
      return cached;
    }

    // Calculate and cache the result
    console.log('📊 Calculating distribution data...');
    const result = calculator(trades);
    this.setCached(cacheKey, result);

    return result;
  }
}

// Export singleton instance
export const performanceCache = new PerformanceCacheManager();

/**
 * Batch calculation optimization
 *
 * This function optimizes multiple calculations by batching them together
 * and using cached results where possible.
 */
export const batchCalculateAnalytics = async (
  trades: CompleteTradeData[],
  calculators: {
    performanceMetrics: (trades: CompleteTradeData[]) => PerformanceMetrics;
    categoryPerformance: (trades: CompleteTradeData[], category: any) => CategoryPerformance[];
    timePerformance: (trades: CompleteTradeData[], timeType: any) => TimePerformance[];
    equityCurve: (trades: CompleteTradeData[]) => EquityPoint[];
    distributionData: (trades: CompleteTradeData[]) => DistributionBar[];
  }
): Promise<{
  metrics: PerformanceMetrics;
  symbolPerformance: CategoryPerformance[];
  strategyPerformance: CategoryPerformance[];
  sessionPerformance: CategoryPerformance[];
  setupPerformance: CategoryPerformance[];
  directionPerformance: CategoryPerformance[];
  timeOfDayPerformance: TimePerformance[];
  dayOfWeekPerformance: TimePerformance[];
  monthlyPerformance: TimePerformance[];
  equityCurve: EquityPoint[];
  distributionData: DistributionBar[];
}> => {
  console.log('🚀 Starting batch analytics calculation...');

  // Use Promise.all for parallel execution of cached calculations
  const [
    metrics,
    symbolPerformance,
    strategyPerformance,
    sessionPerformance,
    setupPerformance,
    directionPerformance,
    timeOfDayPerformance,
    dayOfWeekPerformance,
    monthlyPerformance,
    equityCurve,
    distributionData,
  ] = await Promise.all([
    performanceCache.getCachedPerformanceMetrics(trades, calculators.performanceMetrics),
    performanceCache.getCachedCategoryPerformance(
      trades,
      'market',
      calculators.categoryPerformance
    ),
    performanceCache.getCachedCategoryPerformance(
      trades,
      'model_type',
      calculators.categoryPerformance
    ),
    performanceCache.getCachedCategoryPerformance(
      trades,
      'session',
      calculators.categoryPerformance
    ),
    performanceCache.getCachedCategoryPerformance(trades, 'setup', calculators.categoryPerformance),
    performanceCache.getCachedCategoryPerformance(
      trades,
      'direction',
      calculators.categoryPerformance
    ),
    performanceCache.getCachedTimePerformance(trades, 'timeOfDay', calculators.timePerformance),
    performanceCache.getCachedTimePerformance(trades, 'dayOfWeek', calculators.timePerformance),
    performanceCache.getCachedTimePerformance(trades, 'monthly', calculators.timePerformance),
    performanceCache.getCachedEquityCurve(trades, calculators.equityCurve),
    performanceCache.getCachedDistributionData(trades, calculators.distributionData),
  ]);

  console.log('✅ Batch analytics calculation completed');

  return {
    metrics,
    symbolPerformance,
    strategyPerformance,
    sessionPerformance,
    setupPerformance,
    directionPerformance,
    timeOfDayPerformance,
    dayOfWeekPerformance,
    monthlyPerformance,
    equityCurve,
    distributionData,
  };
};

/**
 * Performance monitoring utilities
 */
export const performanceMonitor = {
  /**
   * Measure execution time of a function
   */
  async measureTime<T>(name: string, fn: () => Promise<T> | T): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);
    return result;
  },

  /**
   * Log memory usage (if available)
   */
  logMemoryUsage(context: string): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`🧠 Memory usage (${context}):`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
      });
    }
  },

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return performanceCache.getCacheStats();
  },
};
