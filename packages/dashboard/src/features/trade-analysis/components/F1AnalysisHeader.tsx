/**
 * F1AnalysisHeader Component
 *
 * REFACTORED FROM: TradeAnalysis.tsx (144 lines → focused components)
 * F1 racing-themed header for the trade analysis feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with red accents
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across analysis contexts
 */

import React from 'react';
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';

export interface F1AnalysisHeaderProps {
  /** Custom className */
  className?: string;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Whether refresh is in progress */
  isRefreshing?: boolean;
  /** Number of trades */
  tradeCount?: number;
  /** Refresh handler */
  onRefresh?: () => void;
  /** Export handler */
  onExport?: () => void;
}

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
  border-bottom: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};
  position: relative;

  /* F1 Racing accent line */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80 50%,
      transparent 100%
    );
    border-radius: 2px;
  }
`;

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;

  /* F1 Racing style */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;

const Subtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  font-weight: 400;
`;

const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const StatusIndicator = styled.div<{ $isLive: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  ${({ $isLive, theme }) =>
    $isLive
      ? `
        background: ${theme.colors?.success || 'var(--success-color)'}20;
        color: ${theme.colors?.success || 'var(--success-color)'};
        border: 1px solid ${theme.colors?.success || 'var(--success-color)'}40;
      `
      : `
        background: ${theme.colors?.textSecondary || 'var(--text-secondary)'}20;
        color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        border: 1px solid ${theme.colors?.textSecondary || 'var(--text-secondary)'}40;
      `}
`;

const StatusDot = styled.div<{ $isLive: boolean }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${({ $isLive, theme }) =>
    $isLive ? theme.colors?.success || 'var(--success-color)' : theme.colors?.textSecondary || 'var(--text-secondary)'};
  animation: ${({ $isLive }) => ($isLive ? 'pulse 2s infinite' : 'none')};

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const TradeCounter = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const ActionButton = styled(Button)<{ $variant: 'primary' | 'secondary' }>`
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 100px;

  ${({ $variant, theme }) =>
    $variant === 'primary'
      ? `
        background: ${theme.colors?.primary || 'var(--primary-color)'};
        color: white;
        border-color: ${theme.colors?.primary || 'var(--primary-color)'};

        &:hover:not(:disabled) {
          background: ${theme.colors?.primaryDark || 'var(--primary-dark)'};
          border-color: ${theme.colors?.primaryDark || 'var(--primary-dark)'};
          transform: translateY(-1px);
          box-shadow: 0 4px 8px ${theme.colors?.primary || 'var(--primary-color)'}40;
        }
      `
      : `
        background: transparent;
        color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
        border-color: ${theme.colors?.border || 'var(--border-primary)'};

        &:hover:not(:disabled) {
          color: ${theme.colors?.textPrimary || '#ffffff'};
          border-color: ${theme.colors?.textPrimary || '#ffffff'};
          background: ${theme.colors?.surface || 'var(--bg-secondary)'};
        }
      `}

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

/**
 * F1AnalysisHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with red accents
 * - Status indicators with animations
 * - Action buttons with hover effects
 * - Consistent with F1 design system
 * - Accessible and responsive
 */
export const F1AnalysisHeader: React.FC<F1AnalysisHeaderProps> = ({
  className,
  isLoading = false,
  isRefreshing = false,
  tradeCount = 0,
  onRefresh,
  onExport,
}) => {
  const hasData = tradeCount > 0;

  return (
    <HeaderContainer className={className}>
      <TitleSection>
        <Title>Trade Analysis</Title>
        <Subtitle>Performance metrics and insights for data-driven trading decisions</Subtitle>
      </TitleSection>

      <ActionsSection>
        <StatusIndicator $isLive={hasData && !isLoading}>
          <StatusDot $isLive={hasData && !isLoading} />
          {hasData ? 'LIVE DATA' : 'NO DATA'}
        </StatusIndicator>

        {hasData && <TradeCounter>📊 {tradeCount.toLocaleString()} trades</TradeCounter>}

        <ActionButton
          $variant="secondary"
          variant="outline"
          onClick={onRefresh}
          disabled={isLoading}
          startIcon={<span>{isRefreshing ? '⏳' : '🔄'}</span>}
          title="Refresh analysis data"
        >
          {isRefreshing ? 'Refreshing' : 'Refresh'}
        </ActionButton>

        <ActionButton
          $variant="primary"
          onClick={onExport}
          disabled={isLoading || !hasData}
          startIcon={<span>📊</span>}
          title="Export analysis data"
        >
          Export
        </ActionButton>
      </ActionsSection>
    </HeaderContainer>
  );
};

export default F1AnalysisHeader;
