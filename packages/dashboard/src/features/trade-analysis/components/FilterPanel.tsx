/**
 * Enhanced Filter Panel Component
 *
 * Modern, structured filtering interface for trade analysis
 * with improved visual hierarchy and responsive design.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import {
  TradeFilters,
  TradeDirection,
  TradeStatus,
  TradeTimeframe,
  TradingSession,
} from '../types';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { <PERSON><PERSON>, Card, Tag } from '@adhd-trading-dashboard/shared';

interface FilterPanelProps {
  className?: string;
}

// Main container with F1-inspired design
const Container = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
`;

// Header section with title and actions
const FilterHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--bg-primary) 0%, #2a2a2a 100%);
  border-bottom: 1px solid var(--border-primary);
`;

const HeaderTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Title = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
`;

const LiveIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--error-color);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--error-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 12px;
`;

// Filter content area
const FilterContent = styled.div`
  padding: 24px;
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
`;

const FilterGroup = styled.div`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 16px;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: var(--text-secondary);
  }
`;

const FilterLabel = styled.div`
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 3px;
    height: 12px;
    background: var(--error-color);
    border-radius: 2px;
  }
`;

const DateRangeContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const DateInput = styled.input`
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  padding: 10px 12px;
  color: #ffffff;
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
  }

  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
  }
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

const FilterTag = styled(Tag)<{ selected: boolean }>`
  cursor: pointer;
  opacity: ${({ selected }) => (selected ? 1 : 0.6)};
  background: ${({ selected, variant }) => {
    if (!selected) return 'var(--border-primary)';
    switch (variant) {
      case 'success':
        return 'var(--success-color)';
      case 'error':
        return 'var(--error-color)';
      case 'info':
        return 'var(--info-color)';
      case 'primary':
        return '#8b5cf6';
      case 'secondary':
        return 'var(--warning-color)';
      default:
        return 'var(--text-secondary)';
    }
  }};
  color: ${({ selected }) => (selected ? '#ffffff' : 'var(--text-secondary)')};
  border: 1px solid
    ${({ selected, variant }) => {
      if (!selected) return 'var(--border-primary)';
      switch (variant) {
        case 'success':
          return 'var(--success-color)';
        case 'error':
          return 'var(--error-color)';
        case 'info':
          return 'var(--info-color)';
        case 'primary':
          return '#8b5cf6';
        case 'secondary':
          return 'var(--warning-color)';
        default:
          return 'var(--text-secondary)';
      }
    }};
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.8;
    transform: translateY(-1px);
  }
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-primary);
`;

const FilterStats = styled.div`
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button<{ variant: 'primary' | 'secondary' }>`
  background: ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'transparent')};
  color: ${({ variant }) => (variant === 'primary' ? '#ffffff' : 'var(--text-secondary)')};
  border: 1px solid ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'var(--border-primary)')};
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;

  &:hover {
    background: ${({ variant }) => (variant === 'primary' ? 'var(--primary-color)' : 'var(--border-primary)')};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

export const FilterPanel: React.FC<FilterPanelProps> = ({ className }) => {
  const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();

  // Local state for filter values
  const [localFilters, setLocalFilters] = useState<TradeFilters>(filters);

  // Available options from data
  const availableSymbols = data?.trades
    ? [...new Set(data.trades.map((trade) => trade.symbol))]
    : [];

  const availableStrategies = data?.trades
    ? [...new Set(data.trades.map((trade) => trade.strategy))]
    : [];

  const availableTags = data?.trades
    ? [...new Set(data.trades.flatMap((trade) => trade.tags || []))]
    : [];

  // Direction options
  const directionOptions: TradeDirection[] = ['long', 'short'];

  // Status options
  const statusOptions: TradeStatus[] = ['win', 'loss', 'breakeven'];

  // Timeframe options
  const timeframeOptions: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];

  // Session options
  const sessionOptions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];

  // Calculate filter stats
  const activeFilterCount = Object.values(localFilters).filter((value) => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) return Object.values(value).some((v) => v);
    return value !== undefined && value !== null && value !== '';
  }).length;

  const totalTrades = data?.trades?.length || 0;

  // Handle date range change
  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: value,
      },
    }));
  };

  // Handle array filter toggle
  const handleToggleFilter = <T extends string>(
    field: keyof Pick<
      TradeFilters,
      'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'
    >,
    value: T
  ) => {
    setLocalFilters((prev) => {
      const currentValues = (prev[field] as T[]) || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter((v) => v !== value)
        : [...currentValues, value];

      return {
        ...prev,
        [field]: newValues.length > 0 ? newValues : undefined,
      };
    });
  };

  // Apply filters
  const applyFilters = () => {
    updateFilters(localFilters);
  };

  // Reset filters
  const handleResetFilters = () => {
    resetFilters();
    setLocalFilters(filters);
  };

  // Check if a filter value is selected
  const isSelected = <T extends string>(
    field: keyof Pick<
      TradeFilters,
      'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'
    >,
    value: T
  ): boolean => {
    const values = localFilters[field] as T[] | undefined;
    return values ? values.includes(value) : false;
  };

  return (
    <Container className={className}>
      <FilterHeader>
        <HeaderTitle>
          <Title>Trade Analysis</Title>
          <LiveIndicator>LIVE</LiveIndicator>
        </HeaderTitle>
        <HeaderActions>
          <ActionButton variant="secondary" onClick={handleResetFilters}>
            Reset
          </ActionButton>
        </HeaderActions>
      </FilterHeader>

      <FilterContent>
        <FilterGrid>
          <FilterGroup>
            <FilterLabel>Date Range</FilterLabel>
            <DateRangeContainer>
              <DateInput
                type="date"
                value={localFilters.dateRange.startDate}
                onChange={(e) => handleDateChange('startDate', e.target.value)}
                placeholder="Start Date"
              />
              <DateInput
                type="date"
                value={localFilters.dateRange.endDate}
                onChange={(e) => handleDateChange('endDate', e.target.value)}
                placeholder="End Date"
              />
            </DateRangeContainer>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Direction</FilterLabel>
            <TagsContainer>
              {directionOptions.map((direction) => (
                <FilterTag
                  key={direction}
                  variant={direction === 'long' ? 'success' : 'error'}
                  selected={isSelected('directions', direction)}
                  onClick={() => handleToggleFilter('directions', direction)}
                >
                  {direction}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Status</FilterLabel>
            <TagsContainer>
              {statusOptions.map((status) => (
                <FilterTag
                  key={status}
                  variant={status === 'win' ? 'success' : status === 'loss' ? 'error' : 'info'}
                  selected={isSelected('statuses', status)}
                  onClick={() => handleToggleFilter('statuses', status)}
                >
                  {status}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterGroup>

          {availableSymbols.length > 0 && (
            <FilterGroup>
              <FilterLabel>Symbols</FilterLabel>
              <TagsContainer>
                {availableSymbols.map((symbol) => (
                  <FilterTag
                    key={symbol}
                    variant="primary"
                    selected={isSelected('symbols', symbol)}
                    onClick={() => handleToggleFilter('symbols', symbol)}
                  >
                    {symbol}
                  </FilterTag>
                ))}
              </TagsContainer>
            </FilterGroup>
          )}

          {availableStrategies.length > 0 && (
            <FilterGroup>
              <FilterLabel>Strategies</FilterLabel>
              <TagsContainer>
                {availableStrategies.map((strategy) => (
                  <FilterTag
                    key={strategy}
                    variant="secondary"
                    selected={isSelected('strategies', strategy)}
                    onClick={() => handleToggleFilter('strategies', strategy)}
                  >
                    {strategy}
                  </FilterTag>
                ))}
              </TagsContainer>
            </FilterGroup>
          )}

          <FilterGroup>
            <FilterLabel>Timeframe</FilterLabel>
            <TagsContainer>
              {timeframeOptions.map((timeframe) => (
                <FilterTag
                  key={timeframe}
                  variant="default"
                  selected={isSelected('timeframes', timeframe)}
                  onClick={() => handleToggleFilter('timeframes', timeframe)}
                >
                  {timeframe}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Session</FilterLabel>
            <TagsContainer>
              {sessionOptions.map((session) => (
                <FilterTag
                  key={session}
                  variant="default"
                  selected={isSelected('sessions', session)}
                  onClick={() => handleToggleFilter('sessions', session)}
                >
                  {session}
                </FilterTag>
              ))}
            </TagsContainer>
          </FilterGroup>

          {availableTags.length > 0 && (
            <FilterGroup>
              <FilterLabel>Tags</FilterLabel>
              <TagsContainer>
                {availableTags.map((tag) => (
                  <FilterTag
                    key={tag}
                    variant="info"
                    selected={isSelected('tags', tag)}
                    onClick={() => handleToggleFilter('tags', tag)}
                  >
                    {tag}
                  </FilterTag>
                ))}
              </TagsContainer>
            </FilterGroup>
          )}
        </FilterGrid>
      </FilterContent>

      <ActionBar>
        <FilterStats>
          {activeFilterCount > 0
            ? `${activeFilterCount} filter${
                activeFilterCount > 1 ? 's' : ''
              } active • ${totalTrades} trades`
            : `${totalTrades} trades • No filters applied`}
        </FilterStats>
        <ActionButtons>
          <ActionButton variant="primary" onClick={applyFilters}>
            Apply Filters
          </ActionButton>
        </ActionButtons>
      </ActionBar>
    </Container>
  );
};
