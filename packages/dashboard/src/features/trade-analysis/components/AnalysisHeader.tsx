/**
 * AnalysisHeader Component
 *
 * F1-themed header for Trade Analysis following the proven F1Header pattern.
 * Provides consistent branding and navigation across the application.
 */

import React from 'react';
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';

export interface AnalysisHeaderProps {
  /** Function called when refresh is clicked */
  onRefresh?: () => void;
  /** Whether refresh is in progress */
  isRefreshing?: boolean;
  /** Custom className */
  className?: string;
}

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'} 0;
  border-bottom: 2px solid var(--border-primary);
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;

  /* F1 Racing aesthetic */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;

const Subtitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const RefreshButton = styled(Button)<{ isRefreshing?: boolean }>`
  min-width: 100px;
  position: relative;

  ${({ isRefreshing }) =>
    isRefreshing &&
    `
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: var(--model-card-bg);
  border: 1px solid var(--model-card-border);
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  color: var(--model-name-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const StatusDot = styled.div`
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;

/**
 * AnalysisHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Trade Analysis feature.
 */
export const AnalysisHeader: React.FC<AnalysisHeaderProps> = ({
  onRefresh,
  isRefreshing = false,
  className,
}) => {
  return (
    <HeaderContainer className={className}>
      <TitleSection>
        <Title>Trade Analysis</Title>
        <Subtitle>Performance Metrics & Insights</Subtitle>
      </TitleSection>

      <ActionsSection>
        <StatusIndicator>
          <StatusDot />
          LIVE DATA
        </StatusIndicator>

        {onRefresh && (
          <RefreshButton
            variant="outline"
            size="small"
            onClick={onRefresh}
            disabled={isRefreshing}
            isRefreshing={isRefreshing}
          >
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </RefreshButton>
        )}
      </ActionsSection>
    </HeaderContainer>
  );
};

export default AnalysisHeader;
