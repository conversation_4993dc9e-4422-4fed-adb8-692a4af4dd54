/**
 * TradesTableBody Component
 * 
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Table body with optimized row rendering and F1 styling.
 * 
 * BENEFITS:
 * - Focused responsibility (body rendering only)
 * - Optimized for performance with React.memo
 * - F1 racing theme with hover effects
 * - Clean separation from header logic
 * - Reusable row components
 */

import React from 'react';
import styled from 'styled-components';
import { Trade } from '../types';
import { TradesTableRow } from './TradesTableRow';

export interface TradesTableBodyProps {
  /** Array of trades to display */
  trades: Trade[];
  /** Currently selected trade ID */
  selectedTradeId: string | null;
  /** Row click handler */
  onRowClick: (tradeId: string) => void;
  /** Formatting utilities */
  formatters: {
    formatDate: (dateString: string) => string;
    formatCurrency: (value: number) => string;
    formatPercent: (value: number) => string;
  };
  /** Event handlers */
  handlers: {
    getDirectionVariant: (direction: string) => string;
    getStatusVariant: (status: string) => string;
  };
}

const TableBody = styled.tbody`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
`;

const EmptyRow = styled.tr`
  background: transparent;
`;

const EmptyCell = styled.td`
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  text-align: center;
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-style: italic;
  colspan: 9;
`;

/**
 * TradesTableBody Component
 * 
 * PATTERN: F1 Component Pattern
 * - Optimized rendering with React.memo
 * - Consistent F1 racing theme
 * - Performance-focused row rendering
 * - Clean prop interface
 * - Accessible table structure
 */
export const TradesTableBody: React.FC<TradesTableBodyProps> = React.memo(({
  trades,
  selectedTradeId,
  onRowClick,
  formatters,
  handlers,
}) => {
  // Handle empty state
  if (!trades || trades.length === 0) {
    return (
      <TableBody>
        <EmptyRow>
          <EmptyCell colSpan={9}>
            No trades found for the selected filters.
          </EmptyCell>
        </EmptyRow>
      </TableBody>
    );
  }

  return (
    <TableBody>
      {trades.map((trade) => (
        <TradesTableRow
          key={trade.id}
          trade={trade}
          isSelected={trade.id === selectedTradeId}
          onClick={() => onRowClick(trade.id)}
          formatters={formatters}
          handlers={handlers}
        />
      ))}
    </TableBody>
  );
});

// Display name for debugging
TradesTableBody.displayName = 'TradesTableBody';

export default TradesTableBody;
