/**
 * TradesTableRow Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Individual table row with F1 racing theme and optimized rendering.
 *
 * BENEFITS:
 * - Focused responsibility (single row rendering)
 * - Optimized with React.memo for performance
 * - F1 racing theme with smooth animations
 * - Reusable across different table contexts
 * - Clean prop interface and type safety
 */

import React from 'react';
import styled from 'styled-components';
import { Trade, TradeDirection } from '../types';
import { Badge, Tag } from '@adhd-trading-dashboard/shared';

export interface TradesTableRowProps {
  /** Trade data */
  trade: Trade;
  /** Whether this row is selected */
  isSelected: boolean;
  /** Click handler */
  onClick: () => void;
  /** Formatting utilities */
  formatters: {
    formatDate: (dateString: string) => string;
    formatCurrency: (value: number) => string;
    formatPercent: (value: number) => string;
  };
  /** Event handlers */
  handlers: {
    getDirectionVariant: (direction: string) => string;
    getStatusVariant: (status: string) => string;
  };
}

const TableRow = styled.tr<{ $isSelected?: boolean }>`
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme, $isSelected }) =>
    $isSelected ? `${theme.colors?.primary || 'var(--primary-color)'}15` : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;

  /* F1 Racing hover effect */
  &:hover {
    background: ${({ theme, $isSelected }) =>
      $isSelected
        ? `${theme.colors?.primary || 'var(--primary-color)'}20`
        : `${theme.colors?.primary || 'var(--primary-color)'}08`};
    transform: translateY(-1px);
    box-shadow: 0 2px 4px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  }

  &:active {
    transform: translateY(0);
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  vertical-align: middle;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  border-right: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'}40;

  &:last-child {
    border-right: none;
  }
`;

const DirectionBadge = styled(Badge)<{ $direction: TradeDirection }>`
  text-transform: capitalize;
  font-weight: 600;
  min-width: 60px;
  justify-content: center;
`;

const StatusBadge = styled(Badge)<{ $status: string }>`
  text-transform: capitalize;
  font-weight: 600;
  min-width: 70px;
  justify-content: center;
`;

const ProfitLoss = styled.span<{ $value: number }>`
  color: ${({ theme, $value }) =>
    $value > 0
      ? theme.colors?.success || 'var(--success-color)'
      : $value < 0
      ? theme.colors?.error || 'var(--error-color)'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: ${({ $value }) => ($value !== 0 ? 600 : 400)};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
`;

const PriceRange = styled.span`
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  max-width: 150px;
`;

const SymbolCell = styled(TableCell)`
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
`;

const DateCell = styled(TableCell)`
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  min-width: 140px;
`;

/**
 * TradesTableRow Component
 *
 * PATTERN: F1 Component Pattern
 * - Racing-inspired hover effects and animations
 * - Optimized rendering with React.memo
 * - Consistent F1 color scheme
 * - Accessible interaction patterns
 * - Clean separation of concerns
 */
export const TradesTableRow: React.FC<TradesTableRowProps> = React.memo(
  ({ trade, isSelected, onClick, formatters, handlers }) => {
    const { formatDate, formatCurrency } = formatters;
    const { getDirectionVariant, getStatusVariant } = handlers;

    const getTradeStatus = (profitLoss: number): string => {
      if (profitLoss > 0) return 'win';
      if (profitLoss < 0) return 'loss';
      return 'breakeven';
    };

    return (
      <TableRow
        $isSelected={isSelected}
        onClick={onClick}
        role='button'
        tabIndex={0}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        }}
        aria-selected={isSelected}
        title={`Trade ${trade.symbol} - Click to ${isSelected ? 'deselect' : 'select'}`}
      >
        {/* Date/Time */}
        <DateCell>{formatDate(trade.date)}</DateCell>

        {/* Symbol */}
        <SymbolCell>{trade.symbol}</SymbolCell>

        {/* Direction */}
        <TableCell>
          <DirectionBadge
            $direction={trade.direction.toLowerCase() as TradeDirection}
            variant={getDirectionVariant(trade.direction) as any}
            size='small'
          >
            {trade.direction}
          </DirectionBadge>
        </TableCell>

        {/* Entry/Exit Prices */}
        <TableCell>
          <PriceRange>
            {trade.entry} → {trade.exit}
          </PriceRange>
        </TableCell>

        {/* Profit/Loss */}
        <TableCell>
          <ProfitLoss $value={trade.profitLoss}>{formatCurrency(trade.profitLoss)}</ProfitLoss>
        </TableCell>

        {/* Profit/Loss Percentage */}
        <TableCell>
          <ProfitLoss $value={trade.profitLoss || 0}>
            {formatCurrency(trade.profitLoss || 0)}
          </ProfitLoss>
        </TableCell>

        {/* Status */}
        <TableCell>
          <StatusBadge
            $status={getTradeStatus(trade.profitLoss)}
            variant={getStatusVariant(getTradeStatus(trade.profitLoss)) as any}
            size='small'
          >
            {getTradeStatus(trade.profitLoss)}
          </StatusBadge>
        </TableCell>

        {/* Strategy */}
        <TableCell>{trade.strategy || '-'}</TableCell>

        {/* Tags */}
        <TableCell>
          <TagsContainer>
            {trade.tags?.slice(0, 3).map((tag, index) => (
              <Tag key={index} size='small' variant='default'>
                {tag}
              </Tag>
            ))}
            {trade.tags?.length > 3 && (
              <Tag size='small' variant='secondary'>
                +{trade.tags.length - 3}
              </Tag>
            )}
          </TagsContainer>
        </TableCell>
      </TableRow>
    );
  }
);

// Display name for debugging
TradesTableRow.displayName = 'TradesTableRow';

export default TradesTableRow;
