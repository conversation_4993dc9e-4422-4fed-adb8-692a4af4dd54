/**
 * TradeAnalysisContainer Component
 *
 * REFACTORED: Main orchestrator for the trade analysis feature
 * Follows the proven TradingDashboard architecture pattern for consistency.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - F1 racing theme consistency
 * - Performance optimized with memoization
 * - Reusable tab navigation pattern
 */

import React, { Suspense } from 'react';
import styled from 'styled-components';
import { LoadingSpinner } from '@adhd-trading-dashboard/shared';
import { useTradeAnalysis } from '../hooks/TradeAnalysisContext';
import { AnalysisHeader } from './AnalysisHeader';
import { AnalysisTabs } from './AnalysisTabs';
import { FilterPanel } from './FilterPanel';
import { TabContentRenderer } from './TabContentRenderer';

export interface TradeAnalysisContainerProps {
  /** Custom className */
  className?: string;
}

const AnalysisLayout = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1400px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  min-height: 100vh;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const TabContentContainer = styled.div`
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const ErrorFallback = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const RetryButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;

/**
 * Loading Fallback Component
 */
const LoadingFallback: React.FC = () => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '400px',
      gap: '16px',
    }}
  >
    <LoadingSpinner size="lg" />
    <div style={{ color: 'var(--text-secondary)' }}>Loading Trade Analysis...</div>
  </div>
);

/**
 * Analysis Content Component (uses context)
 */
const AnalysisContent: React.FC = () => {
  const { data, isLoading, error, preferences, updatePreferences, fetchData } = useTradeAnalysis();

  // Handle tab changes with preference persistence
  const handleTabChange = (tab: string) => {
    updatePreferences({ defaultView: tab });
  };

  if (error) {
    return (
      <ErrorFallback>
        <div>❌ Analysis Loading Error</div>
        <div>{error}</div>
        <RetryButton onClick={fetchData}>Retry Analysis</RetryButton>
      </ErrorFallback>
    );
  }

  return (
    <AnalysisLayout>
      {/* F1 Racing Header */}
      <AnalysisHeader onRefresh={fetchData} isRefreshing={isLoading} />

      {/* Filter Panel */}
      <FilterPanel />

      {/* Tab Navigation */}
      <AnalysisTabs
        activeTab={preferences.defaultView || 'summary'}
        onTabChange={handleTabChange}
      />

      {/* Tab Content */}
      <ContentArea>
        <Suspense fallback={<LoadingFallback />}>
          <TabContentContainer>
            <TabContentRenderer
              activeTab={preferences.defaultView || 'summary'}
              data={data}
              isLoading={isLoading}
              error={error}
            />
          </TabContentContainer>
        </Suspense>
      </ContentArea>
    </AnalysisLayout>
  );
};

/**
 * TradeAnalysisContainer Component
 *
 * Main container that provides layout management and error boundaries
 * for the refactored trade analysis. Follows TradingDashboard patterns.
 */
export const TradeAnalysisContainer: React.FC<TradeAnalysisContainerProps> = ({ className }) => {
  return (
    <div className={className}>
      <Suspense fallback={<LoadingFallback />}>
        <AnalysisContent />
      </Suspense>
    </div>
  );
};

export default TradeAnalysisContainer;
