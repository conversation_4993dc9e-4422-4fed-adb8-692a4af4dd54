/**
 * Trade Analysis Table Component
 *
 * A component for displaying trade analysis data in a table.
 */
import React from 'react';
import { Table, Card, Badge } from '@adhd-trading-dashboard/shared';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { TradeSort } from '../types/index';

export interface TradeAnalysisTableProps {
  /** The trades to display */
  trades: CompleteTradeData[];
  /** The current sort */
  sort: TradeSort;
  /** Function called when the sort is changed */
  onSort: (field: string, direction: 'asc' | 'desc') => void;
  /** The current page */
  page: number;
  /** Function called when the page is changed */
  onPageChange: (page: number) => void;
  /** The page size */
  pageSize: number;
  /** Function called when the page size is changed */
  onPageSizeChange: (pageSize: number) => void;
  /** The total number of pages */
  totalPages: number;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** Function called when a row is clicked */
  onRowClick?: (trade: CompleteTradeData) => void;
}

/**
 * Trade Analysis Table Component
 *
 * A component for displaying trade analysis data in a table.
 */
export const TradeAnalysisTable: React.FC<TradeAnalysisTableProps> = ({
  trades,
  sort,
  onSort,
  page,
  onPageChange,
  pageSize,
  onPageSizeChange,
  totalPages,
  isLoading = false,
  onRowClick,
}) => {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get profit badge variant
  const getProfitBadgeVariant = (profit: number) => {
    if (profit > 0) return 'success';
    if (profit < 0) return 'error';
    return 'neutral';
  };

  // Table columns
  const columns = [
    {
      id: 'date',
      header: 'Date',
      cell: (row: CompleteTradeData) => formatDate(row.trade.date),
      sortable: true,
    },
    {
      id: 'symbol',
      header: 'Symbol',
      cell: (row: CompleteTradeData) => row.trade.market || 'N/A',
      sortable: true,
    },
    {
      id: 'direction',
      header: 'Direction',
      cell: (row: CompleteTradeData) => (
        <Badge variant={row.trade.direction === 'Long' ? 'primary' : 'secondary'} solid>
          {row.trade.direction}
        </Badge>
      ),
      sortable: true,
      align: 'center' as const,
    },
    {
      id: 'entry_price',
      header: 'Entry Price',
      cell: (row: CompleteTradeData) => formatCurrency(row.trade.entry_price || 0),
      sortable: true,
      align: 'right' as const,
    },
    {
      id: 'exit_price',
      header: 'Exit Price',
      cell: (row: CompleteTradeData) => formatCurrency(row.trade.exit_price || 0),
      sortable: true,
      align: 'right' as const,
    },
    {
      id: 'no_of_contracts',
      header: 'Contracts',
      cell: (row: CompleteTradeData) => row.trade.no_of_contracts || 0,
      sortable: true,
      align: 'right' as const,
    },
    {
      id: 'profit',
      header: 'P&L',
      cell: (row: CompleteTradeData) => (
        <Badge variant={getProfitBadgeVariant(row.trade.achieved_pl || 0)} solid>
          {formatCurrency(row.trade.achieved_pl || 0)}
        </Badge>
      ),
      sortable: true,
      align: 'right' as const,
    },
    {
      id: 'r_multiple',
      header: 'R-Multiple',
      cell: (row: CompleteTradeData) => (row.trade.r_multiple || 0).toFixed(2),
      sortable: true,
      align: 'right' as const,
    },
  ];

  return (
    <Card title='Trades'>
      <Table
        columns={columns}
        data={trades}
        isLoading={isLoading}
        bordered
        striped
        hoverable
        pagination
        currentPage={page}
        pageSize={pageSize}
        totalRows={totalPages * pageSize}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        onSort={(columnId, direction) => onSort(columnId, direction)}
        sortColumn={sort.field}
        sortDirection={sort.direction}
        onRowClick={onRowClick ? row => onRowClick(row as CompleteTradeData) : undefined}
        emptyMessage='No trades found. Try adjusting your filters.'
      />
    </Card>
  );
};
