/**
 * useTradeAnalysis Hook (Deprecated)
 *
 * This hook is deprecated in favor of TradeAnalysisContext.
 * It provides a basic stub for backward compatibility.
 *
 * @deprecated Use TradeAnalysisContext instead
 */

import { useState, useCallback } from 'react';
import { PerformanceMetrics, EquityPoint, DistributionBar } from '../types';

/**
 * useTradeAnalysis Hook (Deprecated)
 *
 * Provides basic trade analysis functionality for backward compatibility.
 * New code should use TradeAnalysisContext instead.
 */
export const useTradeAnalysis = () => {
  // Basic state for backward compatibility
  const [trades] = useState<any[]>([]);
  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);
  const [filter, setFilter] = useState<any>({});
  const [sort, setSort] = useState({ field: 'date', direction: 'desc' as const });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Basic derived state
  const totalPages = Math.ceil(trades.length / pageSize);
  const paginatedTrades = trades.slice((page - 1) * pageSize, page * pageSize);
  const tradeSummary = {
    totalTrades: trades.length,
    winningTrades: 0,
    losingTrades: 0,
    winRate: 0,
    netProfit: 0,
    profitFactor: 0,
  };

  // Chart data
  const [equityCurveData] = useState<EquityPoint[]>([]);
  const [distributionData] = useState<DistributionBar[]>([]);
  const [metrics] = useState<PerformanceMetrics | null>(null);

  // Stub functions for backward compatibility
  const fetchTrades = useCallback(async () => {
    console.warn('useTradeAnalysis is deprecated. Use TradeAnalysisContext instead.');
    // No-op for backward compatibility - setIsLoading removed as state is read-only
  }, []);

  const exportTrades = useCallback(() => {
    console.warn('useTradeAnalysis is deprecated. Use TradeAnalysisContext instead.');
  }, []);

  const clearFilters = useCallback(() => {
    setFilter({});
  }, []);

  return {
    // Legacy data
    metrics,
    equityCurveData,
    distributionData,

    // New state
    trades: paginatedTrades,
    filter,
    sort,
    page,
    pageSize,
    totalPages,
    tradeSummary,
    isLoading,
    error,

    // Actions
    setFilter,
    clearFilters,
    setSort,
    setPage,
    setPageSize,

    // API actions
    fetchTrades,
    exportTrades,
  };
};
