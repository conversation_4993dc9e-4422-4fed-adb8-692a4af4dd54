/**
 * useQuickTradeForm Hook
 *
 * Custom hook for managing quick trade form state and logic.
 * Extracted from the original QuickTradeForm component for better separation of concerns.
 *
 * FEATURES:
 * - Form field management with validation
 * - Auto-save functionality
 * - Error handling
 * - Performance optimization
 */

import {
  TradeFormData,
  useFormField,
  useLoadingState,
  validationRules,
} from '@adhd-trading-dashboard/shared';
import { useCallback, useEffect, useState } from 'react';

export interface UseQuickTradeFormConfig {
  /** Callback when trade is successfully submitted */
  onSubmit?: (trade: TradeFormData) => Promise<void>;
  /** Initial form values */
  initialValues?: Partial<TradeFormData>;
  /** Whether to enable auto-save */
  autoSave?: boolean;
  /** Auto-save interval in milliseconds */
  autoSaveInterval?: number;
}

export interface UseQuickTradeFormReturn {
  // Form fields
  dateField: ReturnType<typeof useFormField<string>>;
  symbolField: ReturnType<typeof useFormField<string>>;
  directionField: ReturnType<typeof useFormField<'long' | 'short'>>;
  quantityField: ReturnType<typeof useFormField<number>>;
  entryPriceField: ReturnType<typeof useFormField<number>>;
  exitPriceField: ReturnType<typeof useFormField<number>>;

  // Form state
  isSubmitting: boolean;
  error: string | null;
  success: string | null;

  // Form actions
  handleSubmit: () => Promise<void>;
  handleClear: () => void;
  validateForm: () => boolean;

  // Auto-save
  lastSaved: Date | null;
}

/**
 * Default form values
 */
const getDefaultFormValues = (): TradeFormData => ({
  date: new Date().toISOString().split('T')[0],
  symbol: 'MNQ',
  direction: 'long',
  quantity: '1',
  entryPrice: '0',
  exitPrice: '0',
  profit: '0',
  model: '',
  session: '',
  setup: '',
  patternQuality: '',
  dolTarget: '',
  rdType: '',
  drawOnLiquidity: '',
  entryVersion: '',
  notes: '',
  tags: [],
  result: 'win',
});

/**
 * useQuickTradeForm Hook
 *
 * Manages form state and validation for the quick trade entry form.
 */
export const useQuickTradeForm = (
  config: UseQuickTradeFormConfig = {}
): UseQuickTradeFormReturn => {
  const { onSubmit, initialValues = {}, autoSave = true, autoSaveInterval = 30000 } = config;

  const { isLoading: isSubmitting, withLoading } = useLoadingState();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Form fields with validation
  const dateField = useFormField({
    initialValue: initialValues.date || getDefaultFormValues().date,
    required: true,
    validationRules: [validationRules.required('Date is required')],
  });

  const symbolField = useFormField({
    initialValue: initialValues.symbol || getDefaultFormValues().symbol,
    required: true,
    validationRules: [validationRules.required('Symbol is required')],
  });

  const directionField = useFormField({
    initialValue: initialValues.direction || getDefaultFormValues().direction,
    required: true,
    validationRules: [validationRules.required('Direction is required')],
  });

  const quantityField = useFormField({
    initialValue: Number(initialValues.quantity) || Number(getDefaultFormValues().quantity),
    required: true,
    type: 'number',
    validationRules: [
      validationRules.required('Quantity is required'),
      validationRules.min(1, 'Quantity must be at least 1'),
    ],
  });

  const entryPriceField = useFormField({
    initialValue: Number(initialValues.entryPrice) || Number(getDefaultFormValues().entryPrice),
    required: true,
    type: 'number',
    validationRules: [
      validationRules.required('Entry price is required'),
      validationRules.min(0.01, 'Entry price must be greater than 0'),
    ],
  });

  const exitPriceField = useFormField({
    initialValue: Number(initialValues.exitPrice) || Number(getDefaultFormValues().exitPrice),
    required: true,
    type: 'number',
    validationRules: [
      validationRules.required('Exit price is required'),
      validationRules.min(0.01, 'Exit price must be greater than 0'),
    ],
  });

  /**
   * Get current form values
   */
  const getFormValues = useCallback(
    (): TradeFormData => ({
      date: String(dateField.value),
      symbol: String(symbolField.value),
      direction: directionField.value as 'long' | 'short',
      quantity: String(quantityField.value),
      entryPrice: String(entryPriceField.value),
      exitPrice: String(exitPriceField.value),
      profit: '0', // Calculated automatically
      model: '',
      session: '',
      setup: '',
      patternQuality: '',
      dolTarget: '',
      rdType: '',
      drawOnLiquidity: '',
      entryVersion: '',
      notes: '',
      tags: [],
      result: 'win',
    }),
    [
      dateField.value,
      symbolField.value,
      directionField.value,
      quantityField.value,
      entryPriceField.value,
      exitPriceField.value,
    ]
  );

  /**
   * Validate entire form
   */
  const validateForm = useCallback((): boolean => {
    return (
      dateField.valid &&
      symbolField.valid &&
      directionField.valid &&
      quantityField.valid &&
      entryPriceField.valid &&
      exitPriceField.valid
    );
  }, [
    dateField.valid,
    symbolField.valid,
    directionField.valid,
    quantityField.valid,
    entryPriceField.valid,
    exitPriceField.valid,
  ]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async () => {
    // Clear previous messages
    setError(null);
    setSuccess(null);

    // Validate all fields
    const isValid = await Promise.all([
      dateField.validate(),
      symbolField.validate(),
      directionField.validate(),
      quantityField.validate(),
      entryPriceField.validate(),
      exitPriceField.validate(),
    ]).then(results => results.every(Boolean));

    if (!isValid) {
      setError('Please fix validation errors before submitting');
      return;
    }

    await withLoading(async () => {
      try {
        const formValues = getFormValues();

        if (onSubmit) {
          await onSubmit(formValues);
        } else {
          // Default submission logic
          console.log('Submitting trade:', formValues);
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        setSuccess('Trade submitted successfully!');

        // Clear form after successful submission
        setTimeout(() => {
          handleClear();
          setSuccess(null);
        }, 2000);
      } catch (error) {
        console.error('Failed to submit trade:', error);
        setError(error instanceof Error ? error.message : 'Failed to submit trade');
      }
    });
  }, [
    dateField,
    symbolField,
    directionField,
    quantityField,
    entryPriceField,
    exitPriceField,
    getFormValues,
    onSubmit,
    withLoading,
  ]);

  /**
   * Clear form
   */
  const handleClear = useCallback(() => {
    const defaults = getDefaultFormValues();
    dateField.setValue(defaults.date);
    symbolField.setValue(defaults.symbol);
    directionField.setValue(defaults.direction);
    quantityField.setValue(Number(defaults.quantity));
    entryPriceField.setValue(Number(defaults.entryPrice));
    exitPriceField.setValue(Number(defaults.exitPrice));
    setError(null);
    setSuccess(null);
  }, [dateField, symbolField, directionField, quantityField, entryPriceField, exitPriceField]);

  /**
   * Auto-save functionality
   */
  useEffect(() => {
    if (!autoSave) return;

    const interval = setInterval(() => {
      const formValues = getFormValues();

      // Only auto-save if form has meaningful data
      if (formValues.symbol && parseFloat(formValues.entryPrice) > 0) {
        // Simulate auto-save
        console.log('Auto-saving form data:', formValues);
        setLastSaved(new Date());
      }
    }, autoSaveInterval);

    return () => clearInterval(interval);
  }, [autoSave, autoSaveInterval, getFormValues]);

  return {
    // Form fields
    dateField,
    symbolField,
    directionField,
    quantityField,
    entryPriceField,
    exitPriceField,

    // Form state
    isSubmitting,
    error,
    success,

    // Form actions
    handleSubmit,
    handleClear,
    validateForm,

    // Auto-save
    lastSaved,
  };
};

export default useQuickTradeForm;
