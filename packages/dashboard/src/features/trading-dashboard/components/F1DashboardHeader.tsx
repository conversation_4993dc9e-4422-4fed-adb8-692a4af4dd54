/**
 * F1DashboardHeader Component
 * 
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * F1 racing-themed header for the trading dashboard feature.
 * 
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with live session indicators
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across dashboard contexts
 */

import React from 'react';
import styled from 'styled-components';

export interface F1DashboardHeaderProps {
  /** Custom className */
  className?: string;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Whether refresh is in progress */
  isRefreshing?: boolean;
  /** Current session number */
  sessionNumber?: number;
  /** Whether session is live */
  isLiveSession?: boolean;
  /** Refresh handler */
  onRefresh?: () => void;
  /** Custom title */
  title?: string;
}

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const F1Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  position: relative;
  overflow: hidden;

  /* F1 Racing accent line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg, 
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%, 
      transparent 100%
    );
  }
`;

const F1Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  span {
    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    font-weight: 800;
  }
`;

const LiveIndicator = styled.div<{ $isLive: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ $isLive, theme }) => 
    $isLive 
      ? theme.colors?.primary || 'var(--primary-color)'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  border: 1px solid ${({ $isLive, theme }) => 
    $isLive 
      ? theme.colors?.primary || 'var(--primary-color)'
      : theme.colors?.border || 'var(--border-primary)'};
  background: ${({ $isLive, theme }) => 
    $isLive 
      ? `${theme.colors?.primary || 'var(--primary-color)'}20`
      : 'transparent'};

  &::before {
    content: '●';
    animation: ${({ $isLive }) => $isLive ? 'pulse 2s infinite' : 'none'};
    font-size: 12px;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;

const SubHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const SubTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};
  margin: 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
`;

const StatusBadge = styled.span<{ $sessionNumber: number }>`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  padding: ${({ theme }) => theme.spacing?.xxs || '2px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const RefreshButton = styled.button<{ $isLoading: boolean }>`
  background: ${({ $isLoading, theme }) => 
    $isLoading 
      ? theme.colors?.border || 'var(--border-primary)'
      : theme.colors?.primary || 'var(--primary-color)'};
  color: ${({ theme }) => theme.colors?.textInverse || '#ffffff'};
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  cursor: ${({ $isLoading }) => $isLoading ? 'not-allowed' : 'pointer'};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 120px;
  justify-content: center;

  &:hover:not(:disabled) {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
    box-shadow: 0 4px 8px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
  }
`;

const RefreshIcon = styled.span<{ $isLoading: boolean }>`
  display: inline-block;
  animation: ${({ $isLoading }) => $isLoading ? 'spin 1s linear infinite' : 'none'};
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

/**
 * F1DashboardHeader Component
 * 
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with live session indicators
 * - Animated status indicators and refresh button
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading dashboard appearance
 */
export const F1DashboardHeader: React.FC<F1DashboardHeaderProps> = ({
  className,
  isLoading = false,
  isRefreshing = false,
  sessionNumber = 1,
  isLiveSession = true,
  onRefresh,
  title = 'Trading Dashboard',
}) => {
  return (
    <HeaderContainer className={className}>
      {/* F1 Racing Header */}
      <F1Header>
        <F1Title>
          🏎️ TRADING <span>2025</span> DASHBOARD
        </F1Title>
        <LiveIndicator $isLive={isLiveSession}>
          {isLiveSession ? 'LIVE SESSION' : 'OFFLINE'}
        </LiveIndicator>
      </F1Header>

      {/* Sub Header */}
      <SubHeader>
        <TitleSection>
          <SubTitle>{title}</SubTitle>
          <StatusBadge $sessionNumber={sessionNumber}>
            SESSION {sessionNumber}
          </StatusBadge>
        </TitleSection>
        
        {onRefresh && (
          <RefreshButton
            onClick={onRefresh}
            disabled={isLoading}
            $isLoading={isLoading}
            title={isLoading ? 'Refreshing data...' : 'Refresh dashboard data'}
          >
            <RefreshIcon $isLoading={isLoading || isRefreshing}>
              {isLoading || isRefreshing ? '⏳' : '🔄'}
            </RefreshIcon>
            {isLoading ? 'Refreshing...' : 'Refresh Data'}
          </RefreshButton>
        )}
      </SubHeader>
    </HeaderContainer>
  );
};

export default F1DashboardHeader;
