/**
 * TradingDashboardContainer Component
 *
 * FINAL CONTAINER: Main orchestrator for the refactored trading dashboard
 * Provides error boundaries, layout management, and component composition.
 *
 * BENEFITS:
 * - Clean separation of concerns
 * - Comprehensive error boundaries
 * - Responsive layout management
 * - Feature flag support for gradual migration
 * - Performance optimized composition
 */

import { LoadingSpinner } from '@adhd-trading-dashboard/shared';
import React, { Suspense } from 'react';
import styled from 'styled-components';
import {
  TradingDashboardProvider,
  useTradingDashboardDataFromContext,
  useTradingDashboardSession,
  useTradingDashboardTabs,
} from '../context/TradingDashboardContext';
import { DashboardTabs, F1Header } from './index';
import { QuickTradeForm } from './QuickTradeForm';

// Import existing dashboard components
import MetricsPanel from '../components/MetricsPanel';
import PerformanceChart from '../components/PerformanceChart';
import RecentTradesTable from '../components/RecentTradesTable';
import SetupAnalysis from '../components/SetupAnalysis';

export interface TradingDashboardContainerProps {
  /** Custom className */
  className?: string;
  /** Whether to enable feature flags (unused but kept for compatibility) */
  _enableFeatureFlags?: boolean;
  /** Initial tab to display */
  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}

const DashboardLayout = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
  max-width: 1400px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  min-height: 100vh;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const TabContentContainer = styled.div`
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const AnalyticsLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing?.xl || '24px'};

  @media (max-width: ${({ theme }) => theme.breakpoints?.lg || '1024px'}) {
    grid-template-columns: 1fr;
  }
`;

const ChartsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const ErrorFallback = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '24px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  text-align: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const RetryButton = styled.button`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '16px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  cursor: pointer;
  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};
  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;

/**
 * Error Boundary Component
 */
class DashboardErrorBoundary extends React.Component<
  {
    children: React.ReactNode;
    fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('TradingDashboard Error:', error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;

      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} retry={this.retry} />;
      }

      return (
        <ErrorFallback>
          <div>🚨 Dashboard Error</div>
          <div>Something went wrong with the trading dashboard.</div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>
            {this.state.error?.message || 'Unknown error occurred'}
          </div>
          <RetryButton onClick={this.retry}>Retry Dashboard</RetryButton>
        </ErrorFallback>
      );
    }

    return this.props.children;
  }
}

/**
 * Loading Fallback Component
 */
const LoadingFallback: React.FC = () => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '400px',
      gap: '16px',
    }}
  >
    <LoadingSpinner size='lg' />
    <div style={{ color: 'var(--text-secondary)' }}>Loading Trading Dashboard...</div>
  </div>
);

/**
 * Dashboard Content Component (uses context)
 */
const DashboardContent: React.FC = () => {
  const { activeTab, setActiveTab } = useTradingDashboardTabs();
  const { isLive, sessionName } = useTradingDashboardSession();
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    refreshData,
    isLoading,
    error,
  } = useTradingDashboardDataFromContext();

  const handleTradeSubmit = async (tradeData: any) => {
    console.log('Quick trade submitted:', tradeData);
    // In a real implementation, this would save the trade and refresh data
    await refreshData();
  };

  const renderTabContent = () => {
    console.log('🔄 TradingDashboardContainer renderTabContent called with activeTab:', activeTab);
    console.log('📊 Available data:', {
      tradesCount: trades?.length || 0,
      isLoading,
      error,
      performanceMetricsCount: performanceMetrics?.length || 0,
    });

    switch (activeTab) {
      case 'summary':
        // DEBUG: Log the trades data being passed to RecentTradesTable
        const recentTrades = trades.slice(0, 5);
        console.log(
          '🎯 TradingDashboardContainer passing recent trades to RecentTradesTable:',
          recentTrades.map(t => ({
            id: t.trade.id,
            date: t.trade.date,
            market: t.trade.market,
            direction: t.trade.direction,
          }))
        );
        console.log('🚀 About to render RecentTradesTable with', recentTrades.length, 'trades');

        return (
          <TabContentContainer>
            <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
            <PerformanceChart data={chartData} isLoading={isLoading} />
            <RecentTradesTable trades={recentTrades} isLoading={isLoading} />
          </TabContentContainer>
        );

      case 'trades':
        return (
          <TabContentContainer>
            <RecentTradesTable trades={trades} isLoading={isLoading} />
          </TabContentContainer>
        );

      case 'setups':
        return (
          <TabContentContainer>
            <SetupAnalysis
              setupPerformance={setupPerformance}
              sessionPerformance={sessionPerformance}
              isLoading={isLoading}
            />
          </TabContentContainer>
        );

      case 'analytics':
        return (
          <TabContentContainer>
            <AnalyticsLayout>
              <ChartsSection>
                <MetricsPanel metrics={performanceMetrics} isLoading={isLoading} />
                <PerformanceChart data={chartData} isLoading={isLoading} />
                <SetupAnalysis
                  setupPerformance={setupPerformance}
                  sessionPerformance={sessionPerformance}
                  isLoading={isLoading}
                />
              </ChartsSection>

              <QuickTradeForm onSubmit={handleTradeSubmit} />
            </AnalyticsLayout>
          </TabContentContainer>
        );

      default:
        return (
          <TabContentContainer>
            <div style={{ textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }}>
              Unknown tab: {activeTab}
            </div>
          </TabContentContainer>
        );
    }
  };

  if (error) {
    return (
      <ErrorFallback>
        <div>❌ Data Loading Error</div>
        <div>{error}</div>
        <RetryButton onClick={refreshData}>Retry</RetryButton>
      </ErrorFallback>
    );
  }

  return (
    <DashboardLayout>
      {/* F1 Racing Header */}
      <F1Header
        isLive={isLive}
        sessionName={sessionName}
        onRefresh={refreshData}
        isRefreshing={isLoading}
      />

      {/* Tab Navigation */}
      <DashboardTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        persistState={true}
        syncWithUrl={false}
      />

      {/* Tab Content */}
      <ContentArea>
        <Suspense fallback={<LoadingFallback />}>{renderTabContent()}</Suspense>
      </ContentArea>
    </DashboardLayout>
  );
};

/**
 * TradingDashboardContainer Component
 *
 * Main container that provides context, error boundaries, and layout management
 * for the refactored trading dashboard. This is the final orchestrator component.
 */
export const TradingDashboardContainer: React.FC<TradingDashboardContainerProps> = ({
  className,
  _enableFeatureFlags: _unused = false, // Renamed to indicate unused parameter
  initialTab = 'summary',
}) => {
  return (
    <div className={className}>
      <DashboardErrorBoundary>
        <TradingDashboardProvider initialState={{ activeTab: initialTab }}>
          <Suspense fallback={<LoadingFallback />}>
            <DashboardContent />
          </Suspense>
        </TradingDashboardProvider>
      </DashboardErrorBoundary>
    </div>
  );
};

export default TradingDashboardContainer;
