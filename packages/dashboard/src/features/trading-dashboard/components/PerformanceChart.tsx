/**
 * Performance Chart Component
 *
 * Displays a chart showing trading performance over time
 */

import React from 'react';
import styled from 'styled-components';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts';
import { ChartDataPoint } from '../types';

interface PerformanceChartProps {
  data: ChartDataPoint[];
  isLoading?: boolean;
}

const ChartContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  height: 300px;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const ChartTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NoDataContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * Custom tooltip for the chart
 */
const CustomTooltip: React.FC<any> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: '#252a37',
          padding: '10px',
          border: '1px solid #333',
          borderRadius: '4px',
        }}
      >
        <p style={{ margin: 0 }}>{`Date: ${label}`}</p>
        <p style={{ margin: 0, color: 'var(--primary-color)' }}>
          {`Daily P&L: $${payload[0].value.toFixed(2)}`}
        </p>
        <p style={{ margin: 0, color: 'var(--info-color)' }}>
          {`Cumulative P&L: $${payload[1].value.toFixed(2)}`}
        </p>
      </div>
    );
  }

  return null;
};

/**
 * PerformanceChart Component
 * 
 * Displays a chart showing trading performance over time
 */
export const PerformanceChart: React.FC<PerformanceChartProps> = ({
  data,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <ChartContainer>
        <ChartTitle>Performance</ChartTitle>
        <LoadingContainer>Loading chart data...</LoadingContainer>
      </ChartContainer>
    );
  }

  if (!data || data.length === 0) {
    return (
      <ChartContainer>
        <ChartTitle>Performance</ChartTitle>
        <NoDataContainer>No performance data available</NoDataContainer>
      </ChartContainer>
    );
  }

  return (
    <ChartContainer>
      <ChartTitle>Performance</ChartTitle>
      <ResponsiveContainer width="100%" height="90%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
          <XAxis 
            dataKey="date" 
            stroke="#aaaaaa" 
            tick={{ fill: '#aaaaaa' }} 
          />
          <YAxis 
            stroke="#aaaaaa" 
            tick={{ fill: '#aaaaaa' }} 
            tickFormatter={(value) => `$${value}`} 
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Line
            type="monotone"
            dataKey="pnl"
            name="Daily P&L"
            stroke="var(--primary-color)"
            activeDot={{ r: 8 }}
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="cumulative"
            name="Cumulative P&L"
            stroke="var(--info-color)"
            strokeWidth={2}
          />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export default PerformanceChart;
