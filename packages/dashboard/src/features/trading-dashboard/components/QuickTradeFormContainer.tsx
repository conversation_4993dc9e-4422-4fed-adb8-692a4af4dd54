/**
 * QuickTradeFormContainer Component
 * 
 * REFACTORED: Main orchestrator for the quick trade form feature
 * Follows the proven container pattern from TradeAnalysis and TradingPlan.
 * 
 * BENEFITS:
 * - Uses new F1 component library
 * - Clean separation of concerns
 * - Comprehensive error handling
 * - Auto-save functionality
 * - Performance optimized
 */

import React, { Suspense } from 'react';
import { F1Container, F1Form } from '@adhd-trading-dashboard/shared';
import { TradeFormData } from '@adhd-trading-dashboard/shared';
import { QuickTradeFormFields } from './QuickTradeFormFields';
import { QuickTradeFormActions } from './QuickTradeFormActions';
import { useQuickTradeForm } from '../hooks/useQuickTradeForm';

export interface QuickTradeFormContainerProps {
  /** Callback when trade is successfully submitted */
  onSubmit?: (trade: TradeFormData) => Promise<void>;
  /** Initial form values */
  initialValues?: Partial<TradeFormData>;
  /** Custom className */
  className?: string;
  /** Whether to enable auto-save */
  autoSave?: boolean;
  /** Auto-save interval in milliseconds */
  autoSaveInterval?: number;
}

/**
 * Loading Fallback Component
 */
const LoadingFallback: React.FC = () => (
  <div style={{ 
    display: 'flex', 
    flexDirection: 'column',
    alignItems: 'center', 
    justifyContent: 'center', 
    height: '300px',
    gap: '16px'
  }}>
    <div style={{
      width: '32px',
      height: '32px',
      border: '3px solid var(--border-primary)',
      borderTop: '3px solid var(--primary-color)',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite'
    }} />
    <div style={{ color: 'var(--text-secondary)' }}>Loading Quick Trade Form...</div>
  </div>
);

/**
 * QuickTradeFormContainer Component
 * 
 * Main container that orchestrates the quick trade form functionality
 * using the new F1 component library patterns.
 */
export const QuickTradeFormContainer: React.FC<QuickTradeFormContainerProps> = ({
  onSubmit,
  initialValues = {},
  className,
  autoSave = true,
  autoSaveInterval = 30000,
}) => {
  const {
    // Form fields
    dateField,
    symbolField,
    directionField,
    quantityField,
    entryPriceField,
    exitPriceField,
    
    // Form state
    isSubmitting,
    error,
    success,
    
    // Form actions
    handleSubmit,
    handleClear,
    validateForm,
    
    // Auto-save
    lastSaved,
  } = useQuickTradeForm({
    onSubmit,
    initialValues,
    autoSave,
    autoSaveInterval,
  });

  return (
    <F1Container
      variant="form"
      maxWidth={600}
      className={className}
      background="surface"
    >
      <Suspense fallback={<LoadingFallback />}>
        <F1Form
          title="🏎️ Quick Trade Entry"
          subtitle="Fast trade logging for active sessions"
          variant="quick"
          showAccent={true}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          error={error}
          success={success}
          autoSave={autoSave}
          autoSaveInterval={autoSaveInterval}
        >
          {/* Form Fields */}
          <QuickTradeFormFields
            dateField={dateField}
            symbolField={symbolField}
            directionField={directionField}
            quantityField={quantityField}
            entryPriceField={entryPriceField}
            exitPriceField={exitPriceField}
          />
          
          {/* Form Actions */}
          <QuickTradeFormActions
            onSubmit={handleSubmit}
            onClear={handleClear}
            isSubmitting={isSubmitting}
            canSubmit={validateForm()}
            lastSaved={lastSaved}
          />
        </F1Form>
      </Suspense>
    </F1Container>
  );
};

export default QuickTradeFormContainer;
