/**
 * Pattern Quality Assessment Constants
 *
 * Constants for the pattern quality assessment feature
 */

import { ScoreRange } from '../types';

/**
 * Score values for each score range
 */
export const SCORE_VALUES: Record<ScoreRange, number> = {
  excellent: 5,
  good: 4,
  average: 3,
  poor: 2,
  unacceptable: 1,
};

/**
 * Score range options for dropdowns
 */
export const SCORE_RANGE_OPTIONS = [
  { value: 'excellent', label: 'Excellent (5)' },
  { value: 'good', label: 'Good (4)' },
  { value: 'average', label: 'Average (3)' },
  { value: 'poor', label: 'Poor (2)' },
  { value: 'unacceptable', label: 'Unacceptable (1)' },
];

/**
 * Pattern Quality Criteria Definitions
 */
export const PATTERN_QUALITY_CRITERIA = {
  clarity: {
    title: 'Pattern Clarity',
    description: 'How clear and well-defined is the pattern?',
    excellent: 'Pattern is extremely clear with perfect formation',
    good: 'Pattern is clear with minor imperfections',
    average: 'Pattern is recognizable but has some ambiguity',
    poor: '<PERSON>tern is difficult to recognize with significant ambiguity',
    unacceptable: 'Pattern is barely recognizable or completely ambiguous',
  },
  confluence: {
    title: 'Confluence Factors',
    description: 'How many supporting factors align with this pattern?',
    excellent: 'Multiple strong confluence factors (5+ factors)',
    good: 'Several good confluence factors (3-4 factors)',
    average: 'Some confluence factors (2-3 factors)',
    poor: 'Minimal confluence (1-2 weak factors)',
    unacceptable: 'No confluence factors',
  },
  context: {
    title: 'Market Context',
    description: 'How well does the pattern fit within the broader market context?',
    excellent: 'Perfect alignment with market structure and conditions',
    good: 'Good alignment with market structure and conditions',
    average: 'Reasonable alignment with some contradicting factors',
    poor: 'Poor alignment with several contradicting factors',
    unacceptable: 'Pattern contradicts the broader market context',
  },
  risk: {
    title: 'Risk Profile',
    description: 'How well-defined and manageable is the risk?',
    excellent: 'Extremely clear stop location with minimal risk',
    good: 'Clear stop location with reasonable risk',
    average: 'Identifiable stop location but with moderate risk',
    poor: 'Unclear stop location or high risk',
    unacceptable: 'No clear stop location or extremely high risk',
  },
  reward: {
    title: 'Reward Potential',
    description: 'What is the potential reward relative to risk?',
    excellent: 'Exceptional reward potential (5R+)',
    good: 'Strong reward potential (3-5R)',
    average: 'Reasonable reward potential (2-3R)',
    poor: 'Limited reward potential (1-2R)',
    unacceptable: 'Poor reward potential (<1R)',
  },
  timeframe: {
    title: 'Timeframe Alignment',
    description: 'How well does the pattern align across multiple timeframes?',
    excellent: 'Strong alignment across all relevant timeframes',
    good: 'Good alignment across most timeframes',
    average: 'Alignment on primary timeframe with some higher/lower support',
    poor: 'Limited alignment across timeframes',
    unacceptable: 'Pattern only appears on a single timeframe with contradictions on others',
  },
  volume: {
    title: 'Volume Profile',
    description: 'How does volume support the pattern?',
    excellent: 'Volume perfectly confirms the pattern',
    good: 'Volume generally supports the pattern',
    average: 'Volume is neutral or mixed',
    poor: 'Volume somewhat contradicts the pattern',
    unacceptable: 'Volume strongly contradicts the pattern',
  },
};

/**
 * Calculate total score from criteria
 */
export const calculateTotalScore = (criteria: Record<string, ScoreRange>): number => {
  return Object.values(criteria).reduce((total, score) => {
    return total + (SCORE_VALUES[score] || 0);
  }, 0);
};

/**
 * Convert total score to 1-10 rating
 */
export const convertScoreToRating = (totalScore: number): number => {
  // Maximum possible score is 35 (7 criteria * 5 points)
  // Convert to a 1-10 scale
  const maxPossibleScore = Object.keys(PATTERN_QUALITY_CRITERIA).length * 5;
  const rating = Math.round((totalScore / maxPossibleScore) * 10);

  // Ensure rating is between 1 and 10
  return Math.max(1, Math.min(10, rating));
};

/**
 * Get rating description based on rating value
 */
export const getRatingDescription = (rating: number): string => {
  if (rating >= 9) return 'Exceptional';
  if (rating >= 8) return 'Excellent';
  if (rating >= 7) return 'Very Good';
  if (rating >= 6) return 'Good';
  if (rating >= 5) return 'Average';
  if (rating >= 4) return 'Below Average';
  if (rating >= 3) return 'Poor';
  if (rating >= 2) return 'Very Poor';
  return 'Unacceptable';
};

/**
 * Get color for rating
 */
export const getRatingColor = (rating: number): string => {
  if (rating >= 8) return 'var(--success-color)'; // Green
  if (rating >= 6) return '#8BC34A'; // Light Green
  if (rating >= 5) return '#FFC107'; // Amber
  if (rating >= 3) return '#FF9800'; // Orange
  return 'var(--error-color)'; // Red
};
