/**
 * Trade Analysis Section Component
 *
 * Unified component that consolidates Pattern Quality Assessment and DOL Analysis
 * into a single cohesive section following the DashboardSection pattern.
 *
 * ARCHITECTURE:
 * - Follows compositional design with focused sub-components
 * - Uses DashboardSection wrapper for consistent styling
 * - Maintains TypeScript strict typing
 * - F1 racing theme integration
 */

import React from 'react';
import styled from 'styled-components';
import { DashboardSection } from '@adhd-trading-dashboard/shared';
import PatternQualityAssessment from '../trade-pattern-quality/PatternQualityAssessment';
import DOLAnalysis from '../trade-dol-analysis/TradeDOLAnalysis';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';

// F1 Racing Theme Styled Components
const AnalysisContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    z-index: 1;
  }
`;

const SectionHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
`;

const HeaderTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const HeaderIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;

const HeaderTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const HeaderDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;

const SectionContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const SectionDivider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  margin: ${({ theme }) => theme.spacing?.xl || '32px'} 0;
  opacity: 0.5;
`;

export interface TradeAnalysisSectionProps {
  /** Form values containing analysis data */
  formValues: TradeFormValues;
  /** Change handler for form updates */
  onChange: (field: string, value: any) => void;
  /** Validation errors */
  validationErrors: ValidationErrors;
  /** Whether the section is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

/**
 * Trade Analysis Section Component
 *
 * Consolidates Pattern Quality Assessment and DOL Analysis into a unified section.
 * Follows the established DashboardSection pattern for consistency.
 */
export const TradeAnalysisSection: React.FC<TradeAnalysisSectionProps> = ({
  formValues,
  onChange,
  validationErrors,
  disabled = false,
  className,
}) => {
  return (
    <AnalysisContainer className={className}>
      <SectionHeader>
        <HeaderTitleRow>
          <HeaderIcon>🔍</HeaderIcon>
          <div>
            <HeaderTitle>Trade Analysis</HeaderTitle>
            <HeaderDescription>
              Comprehensive pattern quality assessment and DOL analysis
            </HeaderDescription>
          </div>
        </HeaderTitleRow>
      </SectionHeader>

      <SectionContent>
        {/* Pattern Quality Assessment */}
        <DashboardSection
          name="pattern-quality"
          title="Pattern Quality Assessment"
          collapsible={true}
          defaultCollapsed={false}
        >
          <PatternQualityAssessment formValues={formValues} onChange={onChange} />
        </DashboardSection>

        <SectionDivider />

        {/* DOL Analysis */}
        <DashboardSection
          name="dol-analysis"
          title="DOL Analysis"
          collapsible={true}
          defaultCollapsed={false}
        >
          <DOLAnalysis
            formValues={formValues}
            onChange={onChange}
            validationErrors={validationErrors}
          />
        </DashboardSection>
      </SectionContent>
    </AnalysisContainer>
  );
};

export default TradeAnalysisSection;
