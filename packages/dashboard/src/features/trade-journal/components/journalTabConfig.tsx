/**
 * Journal Tab Configuration
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Centralized configuration for journal tabs and their content.
 *
 * BENEFITS:
 * - Single source of truth for tab definitions
 * - Easy to maintain and extend
 * - Type-safe tab configurations
 * - Reusable across different journal views
 * - Clear content mapping
 */

import React from 'react';
import styled from 'styled-components';
import { JournalTab } from './F1JournalTabs';
import { TradeJournalContent } from './trade-journal';
import TradeList from './TradeList';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';

export interface JournalTabConfig {
  id: JournalTab;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType<any>;
  showInMobile: boolean;
  requiresData: boolean;
}

export interface JournalTabContentProps {
  /** Current active tab */
  activeTab: JournalTab;
  /** Journal data */
  data: {
    trades: CompleteTradeData[];
    filteredTrades: CompleteTradeData[];
    recentTrades: CompleteTradeData[];
    filters: any;
    uniqueSetups: string[];
    uniqueModelTypes: string[];
    uniquePrimarySetupTypes: string[];
    uniqueSecondarySetupTypes: string[];
    uniqueLiquidityTypes: string[];
    uniqueDOLTypes: string[];
  };
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Show filters state */
  showFilters: boolean;
  /** Action handlers */
  handlers: {
    handleFilterChange: (filters: any) => void;
    resetFilters: () => void;
    refreshTrades: () => void;
  };
}

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 300px;
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
`;

const EmptyIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
`;

const EmptyTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const EmptyMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 400px;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  text-align: center;
`;

const StatValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

/**
 * All Trades Tab Content
 */
const AllTradesTabContent: React.FC<JournalTabContentProps> = ({
  data,
  isLoading,
  error,
  showFilters,
  handlers,
}) => {
  if (error) {
    return (
      <EmptyState>
        <EmptyIcon>⚠️</EmptyIcon>
        <EmptyTitle>Error Loading Trades</EmptyTitle>
        <EmptyMessage>{error}</EmptyMessage>
      </EmptyState>
    );
  }

  if (!isLoading && data.trades.length === 0) {
    return (
      <EmptyState>
        <EmptyIcon>📋</EmptyIcon>
        <EmptyTitle>No Trades Found</EmptyTitle>
        <EmptyMessage>Start building your trading journal by adding your first trade.</EmptyMessage>
      </EmptyState>
    );
  }

  return (
    <TradeJournalContent
      error={error}
      showFilters={showFilters}
      filteredTrades={data.filteredTrades}
      isLoading={isLoading}
      filters={data.filters}
      handleFilterChange={handlers.handleFilterChange}
      resetFilters={handlers.resetFilters}
      uniqueSetups={data.uniqueSetups}
      uniqueModelTypes={data.uniqueModelTypes}
      uniquePrimarySetupTypes={data.uniquePrimarySetupTypes}
      uniqueSecondarySetupTypes={data.uniqueSecondarySetupTypes}
      uniqueLiquidityTypes={data.uniqueLiquidityTypes}
      uniqueDOLTypes={data.uniqueDOLTypes}
    />
  );
};

/**
 * Recent Trades Tab Content
 */
const RecentTradesTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading }) => {
  if (!isLoading && data.recentTrades.length === 0) {
    return (
      <EmptyState>
        <EmptyIcon>⚡</EmptyIcon>
        <EmptyTitle>No Recent Trades</EmptyTitle>
        <EmptyMessage>Recent trades from the last 7 days will appear here.</EmptyMessage>
      </EmptyState>
    );
  }

  return (
    <TradeList
      trades={data.recentTrades}
      isLoading={isLoading}
      title="Recent Trades (Last 7 Days)"
    />
  );
};

/**
 * Filters Tab Content
 */
const FiltersTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading, handlers }) => {
  return (
    <TradeJournalContent
      error={null}
      showFilters={true}
      filteredTrades={data.filteredTrades}
      isLoading={isLoading}
      filters={data.filters}
      handleFilterChange={handlers.handleFilterChange}
      resetFilters={handlers.resetFilters}
      uniqueSetups={data.uniqueSetups}
      uniqueModelTypes={data.uniqueModelTypes}
      uniquePrimarySetupTypes={data.uniquePrimarySetupTypes}
      uniqueSecondarySetupTypes={data.uniqueSecondarySetupTypes}
      uniqueLiquidityTypes={data.uniqueLiquidityTypes}
      uniqueDOLTypes={data.uniqueDOLTypes}
    />
  );
};

/**
 * Statistics Tab Content
 */
const StatsTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading }) => {
  if (!isLoading && data.trades.length === 0) {
    return (
      <EmptyState>
        <EmptyIcon>📊</EmptyIcon>
        <EmptyTitle>No Statistics Available</EmptyTitle>
        <EmptyMessage>
          Trade statistics will be calculated once you have recorded trades.
        </EmptyMessage>
      </EmptyState>
    );
  }

  // Calculate basic statistics using correct field names (matching main dashboard)
  const totalTrades = data.trades.length;
  const winningTrades = data.trades.filter((t) => t.trade.win_loss === 'Win').length;
  const winRate = totalTrades > 0 ? ((winningTrades / totalTrades) * 100).toFixed(1) : '0';
  const totalPnL = data.trades.reduce((sum, t) => sum + (t.trade.achieved_pl || 0), 0);

  return (
    <div>
      <StatsContainer>
        <StatCard>
          <StatValue>{totalTrades}</StatValue>
          <StatLabel>Total Trades</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{winRate}%</StatValue>
          <StatLabel>Win Rate</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>${totalPnL.toFixed(2)}</StatValue>
          <StatLabel>Total P&L</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{data.uniqueSetups.length}</StatValue>
          <StatLabel>Unique Setups</StatLabel>
        </StatCard>
      </StatsContainer>

      <TradeList trades={data.trades} isLoading={isLoading} title="All Trades with Statistics" />
    </div>
  );
};

/**
 * Tab configuration with components and metadata
 */
export const JOURNAL_TAB_CONFIG: Record<JournalTab, JournalTabConfig> = {
  all: {
    id: 'all',
    title: 'All Trades',
    description: 'Complete trade history and journal entries',
    icon: '📋',
    component: AllTradesTabContent,
    showInMobile: true,
    requiresData: false,
  },
  recent: {
    id: 'recent',
    title: 'Recent Trades',
    description: 'Latest trades and recent activity',
    icon: '⚡',
    component: RecentTradesTabContent,
    showInMobile: true,
    requiresData: false,
  },
  filters: {
    id: 'filters',
    title: 'Advanced Filters',
    description: 'Advanced filtering and search options',
    icon: '🔍',
    component: FiltersTabContent,
    showInMobile: false,
    requiresData: false,
  },
  stats: {
    id: 'stats',
    title: 'Statistics',
    description: 'Performance metrics and analytics',
    icon: '📊',
    component: StatsTabContent,
    showInMobile: true,
    requiresData: true,
  },
};

/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId: JournalTab): JournalTabConfig => {
  return JOURNAL_TAB_CONFIG[tabId];
};

/**
 * Get all tab configurations
 */
export const getAllTabConfigs = (): JournalTabConfig[] => {
  return Object.values(JOURNAL_TAB_CONFIG);
};

/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = (): JournalTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.showInMobile);
};

/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = (): JournalTabConfig[] => {
  return getAllTabConfigs().filter((config) => config.requiresData);
};

/**
 * Tab Content Renderer Component
 */
export const JournalTabContentRenderer: React.FC<JournalTabContentProps> = (props) => {
  const { activeTab } = props;
  const config = getTabConfig(activeTab);

  if (!config) {
    return (
      <EmptyState>
        <EmptyIcon>❌</EmptyIcon>
        <EmptyTitle>Unknown Tab</EmptyTitle>
        <EmptyMessage>Tab "{activeTab}" not found.</EmptyMessage>
      </EmptyState>
    );
  }

  const TabComponent = config.component;

  return (
    <div
      id={`journal-panel-${activeTab}`}
      role="tabpanel"
      aria-labelledby={`journal-tab-${activeTab}`}
    >
      <TabComponent {...props} />
    </div>
  );
};

export default JournalTabContentRenderer;
