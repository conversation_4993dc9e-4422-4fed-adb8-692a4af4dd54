/**
 * F1JournalTabs Component
 * 
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * F1 racing-themed tabs for journal navigation.
 * 
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with smooth animations
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */

import React from 'react';
import styled from 'styled-components';

export type JournalTab = 'all' | 'recent' | 'filters' | 'stats';

export interface F1JournalTabsProps {
  /** Currently active tab */
  activeTab: JournalTab;
  /** Tab change handler */
  onTabChange: (tab: JournalTab) => void;
  /** Whether tabs are disabled */
  disabled?: boolean;
  /** Trade counts for badges */
  tradeCounts?: {
    total: number;
    recent: number;
    filtered: number;
  };
  /** Whether filters are active */
  hasActiveFilters?: boolean;
  /** Custom className */
  className?: string;
}

const TabsContainer = styled.div`
  display: flex;
  gap: 0;
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0 ${({ theme }) => theme.spacing?.xl || '32px'} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  position: relative;
`;

const Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};
  border: none;
  background: transparent;
  color: ${({ $isActive, theme }) => 
    $isActive 
      ? theme.colors?.textPrimary || '#ffffff'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.2s ease;
  font-weight: ${({ $isActive }) => $isActive ? '600' : '400'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  position: relative;
  border-bottom: 2px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  /* F1 Racing active indicator */
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    transform: scaleX(${({ $isActive }) => $isActive ? 1 : 0});
    transition: transform 0.2s ease;
    transform-origin: center;
  }

  /* F1 Racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ $isActive, theme }) => 
      $isActive 
        ? theme.colors?.textPrimary || '#ffffff'
        : theme.colors?.textPrimary || '#ffffff'};
    transform: translateY(-1px);

    &::after {
      transform: scaleX(1);
      background: ${({ $isActive, theme }) => 
        $isActive 
          ? theme.colors?.primary || 'var(--primary-color)'
          : theme.colors?.textSecondary || 'var(--text-secondary)'};
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  /* Disabled styling */
  ${({ $disabled }) =>
    $disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}

  /* Mobile responsive */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  }
`;

const TabIcon = styled.span`
  font-size: 16px;
  
  @media (max-width: 768px) {
    font-size: 14px;
  }
`;

const TabLabel = styled.span`
  @media (max-width: 768px) {
    display: none;
  }
`;

const TabBadge = styled.span<{ $variant: 'count' | 'active' | 'new' }>`
  background: ${({ $variant, theme }) => {
    switch ($variant) {
      case 'count':
        return theme.colors?.surface || 'var(--bg-secondary)';
      case 'active':
        return theme.colors?.primary || 'var(--primary-color)';
      case 'new':
        return theme.colors?.success || 'var(--success-color)';
      default:
        return theme.colors?.surface || 'var(--bg-secondary)';
    }
  }};
  color: ${({ $variant, theme }) => {
    switch ($variant) {
      case 'count':
        return theme.colors?.textSecondary || 'var(--text-secondary)';
      case 'active':
        return theme.colors?.textInverse || '#ffffff';
      case 'new':
        return theme.colors?.textInverse || '#ffffff';
      default:
        return theme.colors?.textSecondary || 'var(--text-secondary)';
    }
  }};
  padding: 2px 6px;
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  line-height: 1;
  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};

  @media (max-width: 768px) {
    display: none;
  }
`;

/**
 * Tab configuration with icons and labels
 */
const TAB_CONFIG: Record<JournalTab, { icon: string; label: string; description: string }> = {
  all: {
    icon: '📋',
    label: 'All Trades',
    description: 'Complete trade history and journal entries',
  },
  recent: {
    icon: '⚡',
    label: 'Recent',
    description: 'Latest trades and recent activity',
  },
  filters: {
    icon: '🔍',
    label: 'Filters',
    description: 'Advanced filtering and search options',
  },
  stats: {
    icon: '📊',
    label: 'Statistics',
    description: 'Performance metrics and analytics',
  },
};

/**
 * F1JournalTabs Component
 * 
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 * - Trade count badges for context
 */
export const F1JournalTabs: React.FC<F1JournalTabsProps> = ({
  activeTab,
  onTabChange,
  disabled = false,
  tradeCounts,
  hasActiveFilters = false,
  className,
}) => {
  const handleTabClick = (tab: JournalTab) => {
    if (!disabled) {
      onTabChange(tab);
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent, tab: JournalTab) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
      event.preventDefault();
      onTabChange(tab);
    }
  };

  const getBadgeForTab = (tab: JournalTab) => {
    if (!tradeCounts) return null;

    switch (tab) {
      case 'all':
        return tradeCounts.total > 0 ? (
          <TabBadge $variant="count">{tradeCounts.total}</TabBadge>
        ) : null;
      case 'recent':
        return tradeCounts.recent > 0 ? (
          <TabBadge $variant="new">{tradeCounts.recent}</TabBadge>
        ) : null;
      case 'filters':
        return hasActiveFilters ? (
          <TabBadge $variant="active">ON</TabBadge>
        ) : null;
      case 'stats':
        return tradeCounts.total > 0 ? (
          <TabBadge $variant="count">📈</TabBadge>
        ) : null;
      default:
        return null;
    }
  };
  
  return (
    <TabsContainer className={className} role="tablist">
      {(Object.keys(TAB_CONFIG) as JournalTab[]).map((tab) => {
        const config = TAB_CONFIG[tab];
        const isActive = activeTab === tab;
        const badge = getBadgeForTab(tab);
        
        return (
          <Tab
            key={tab}
            $isActive={isActive}
            $disabled={disabled}
            onClick={() => handleTabClick(tab)}
            onKeyDown={(e) => handleKeyDown(e, tab)}
            disabled={disabled}
            role="tab"
            aria-selected={isActive}
            aria-controls={`journal-panel-${tab}`}
            tabIndex={disabled ? -1 : 0}
            title={config.description}
          >
            <TabIcon>{config.icon}</TabIcon>
            <TabLabel>{config.label}</TabLabel>
            {badge}
          </Tab>
        );
      })}
    </TabsContainer>
  );
};

export default F1JournalTabs;
