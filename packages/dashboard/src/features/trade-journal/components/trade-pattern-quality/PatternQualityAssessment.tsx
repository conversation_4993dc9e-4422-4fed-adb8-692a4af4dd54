/**
 * Pattern Quality Assessment Component
 *
 * Main component for the pattern quality assessment section
 */

import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  PATTERN_QUALITY_CRITERIA,
  calculateTotalScore,
  convertScoreToRating,
  getRatingColor,
  getRatingDescription,
} from '../../constants/patternQuality';
import { ScoreRange } from '../../types';
import CriterionSelector from './CriterionSelector';

const AssessmentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`;

const Introduction = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const IntroTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const IntroText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: 0;
  line-height: 1.5;
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

const ScoreSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const ScoreTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0;
`;

const ScoreDetails = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xl};
`;

const ScoreValue = styled.div<{ color: string }>`
  font-size: 3rem;
  font-weight: 700;
  color: ${({ color }) => color};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid ${({ color }) => color};
`;

const ScoreInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const ScoreDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const ScoreBreakdown = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NotesSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const NotesLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  display: block;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  background-color: ${({ theme }) => theme.colors.background};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

interface PatternQualityAssessmentProps {
  formValues: {
    patternQualityClarity?: string;
    patternQualityConfluence?: string;
    patternQualityContext?: string;
    patternQualityRisk?: string;
    patternQualityReward?: string;
    patternQualityTimeframe?: string;
    patternQualityVolume?: string;
    patternQualityNotes?: string;
    patternQuality?: string;
  };
  onChange: (field: string, value: string) => void;
}

const PatternQualityAssessment: React.FC<PatternQualityAssessmentProps> = ({
  formValues,
  onChange,
}) => {
  const [totalScore, setTotalScore] = useState(0);
  const [rating, setRating] = useState(0);

  // Calculate score and rating when criteria change
  useEffect(() => {
    const criteria: Record<string, ScoreRange> = {
      clarity: (formValues.patternQualityClarity || '') as ScoreRange,
      confluence: (formValues.patternQualityConfluence || '') as ScoreRange,
      context: (formValues.patternQualityContext || '') as ScoreRange,
      risk: (formValues.patternQualityRisk || '') as ScoreRange,
      reward: (formValues.patternQualityReward || '') as ScoreRange,
      timeframe: (formValues.patternQualityTimeframe || '') as ScoreRange,
      volume: (formValues.patternQualityVolume || '') as ScoreRange,
    };

    // Only calculate if all criteria are filled
    const allCriteriaFilled = Object.values(criteria).every(value => value && value !== undefined);

    if (allCriteriaFilled) {
      const score = calculateTotalScore(criteria);
      const calculatedRating = convertScoreToRating(score);

      setTotalScore(score);
      setRating(calculatedRating);

      // Update the patternQuality field with the calculated rating
      onChange('patternQuality', calculatedRating.toString());
    }
  }, [
    formValues.patternQualityClarity,
    formValues.patternQualityConfluence,
    formValues.patternQualityContext,
    formValues.patternQualityRisk,
    formValues.patternQualityReward,
    formValues.patternQualityTimeframe,
    formValues.patternQualityVolume,
    onChange,
  ]);

  // Handle notes change
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange('patternQualityNotes', e.target.value);
  };

  return (
    <AssessmentContainer>
      <Introduction>
        <IntroTitle>Pattern Quality Assessment</IntroTitle>
        <IntroText>
          Evaluate the quality of your trade setup by rating each criterion below. This assessment
          will help you objectively analyze your trade patterns and improve your decision-making
          process.
        </IntroText>
      </Introduction>

      <Divider />

      {/* Criterion Selectors */}
      <CriterionSelector
        criterion='clarity'
        value={(formValues.patternQualityClarity || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityClarity'
      />

      <CriterionSelector
        criterion='confluence'
        value={(formValues.patternQualityConfluence || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityConfluence'
      />

      <CriterionSelector
        criterion='context'
        value={(formValues.patternQualityContext || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityContext'
      />

      <CriterionSelector
        criterion='risk'
        value={(formValues.patternQualityRisk || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityRisk'
      />

      <CriterionSelector
        criterion='reward'
        value={(formValues.patternQualityReward || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityReward'
      />

      <CriterionSelector
        criterion='timeframe'
        value={(formValues.patternQualityTimeframe || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityTimeframe'
      />

      <CriterionSelector
        criterion='volume'
        value={(formValues.patternQualityVolume || '') as '' | ScoreRange}
        onChange={onChange}
        fieldName='patternQualityVolume'
      />

      {/* Score Display */}
      {rating > 0 && (
        <ScoreSection>
          <ScoreTitle>Pattern Quality Score</ScoreTitle>
          <ScoreDetails>
            <ScoreValue color={getRatingColor(rating)}>{rating}</ScoreValue>
            <ScoreInfo>
              <ScoreDescription>{getRatingDescription(rating)}</ScoreDescription>
              <ScoreBreakdown>
                Total Score: {totalScore} out of {Object.keys(PATTERN_QUALITY_CRITERIA).length * 5}
              </ScoreBreakdown>
            </ScoreInfo>
          </ScoreDetails>
        </ScoreSection>
      )}

      {/* Notes Section */}
      <NotesSection>
        <NotesLabel htmlFor='patternQualityNotes'>Additional Notes</NotesLabel>
        <NotesTextarea
          id='patternQualityNotes'
          name='patternQualityNotes'
          value={formValues.patternQualityNotes || ''}
          onChange={handleNotesChange}
          placeholder='Add any additional notes about the pattern quality...'
        />
      </NotesSection>
    </AssessmentContainer>
  );
};

export default PatternQualityAssessment;
