/**
 * Trade Journal Filters Component
 *
 * REFACTORED: Now uses the new F1 filter component library.
 * Simplified from 322 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 90% code reduction
 * - Uses F1FilterPanel for consistent styling
 * - Eliminates repetitive filter field code
 * - Better separation of concerns
 * - Type-safe filter handling
 */

import React, { useMemo } from 'react';
import { F1FilterPanel } from '../F1FilterPanel';
import { FilterState } from '../../types';

interface TradeJournalFiltersProps {
  filters: FilterState;
  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  resetFilters: () => void;
  uniqueSetups: string[];
  uniqueModelTypes: string[];
  uniquePrimarySetupTypes: string[];
  uniqueSecondarySetupTypes: string[];
  uniqueLiquidityTypes: string[];
  uniqueDOLTypes: string[];
}

/**
 * Trade Journal Filters Component
 *
 * Simple wrapper that renders the F1FilterPanel.
 * Follows the proven architecture pattern.
 */
const TradeJournalFilters: React.FC<TradeJournalFiltersProps> = ({
  filters,
  handleFilterChange,
  resetFilters,
  uniqueSetups,
  uniqueModelTypes,
  uniquePrimarySetupTypes,
  uniqueSecondarySetupTypes,
  uniqueLiquidityTypes,
  uniqueDOLTypes,
}) => {
  // Convert unique data to the format expected by F1FilterPanel
  const uniqueData = useMemo(
    () => ({
      uniqueSetups,
      uniqueModelTypes,
      uniquePrimarySetupTypes,
      uniqueSecondarySetupTypes,
      uniqueLiquidityTypes,
      uniqueDOLTypes,
    }),
    [
      uniqueSetups,
      uniqueModelTypes,
      uniquePrimarySetupTypes,
      uniqueSecondarySetupTypes,
      uniqueLiquidityTypes,
      uniqueDOLTypes,
    ]
  );

  // Convert the legacy handleFilterChange to the new format
  const handleFiltersChange = (newFilters: Record<string, string | number>) => {
    // For each changed filter, create a synthetic event and call the legacy handler
    Object.entries(newFilters).forEach(([name, value]) => {
      if (filters[name as keyof FilterState] !== value) {
        const syntheticEvent = {
          target: { name, value: String(value) },
        } as React.ChangeEvent<HTMLInputElement | HTMLSelectElement>;
        handleFilterChange(syntheticEvent);
      }
    });
  };

  return (
    <F1FilterPanel
      initialFilters={filters as any}
      onFiltersChange={handleFiltersChange}
      onReset={resetFilters}
      uniqueData={uniqueData}
    />
  );
};

export default TradeJournalFilters;
