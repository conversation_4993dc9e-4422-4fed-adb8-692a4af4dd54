/**
 * F1JournalContainer Component
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * Main orchestrator for trade journal with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense, useMemo } from 'react';
import styled from 'styled-components';
import { useTradeJournal } from '../hooks/useTradeJournal';
import { useTradeFilters } from '../hooks/useTradeFilters';
import { F1JournalHeader } from './F1JournalHeader';
import { F1JournalTabs } from './F1JournalTabs';
import { useJournalNavigation } from './useJournalNavigation';
import { JournalTabContentRenderer } from './journalTabConfig';

export interface F1JournalContainerProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'all' | 'recent' | 'filters' | 'stats';
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  min-height: 100vh;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  max-width: 1400px;
  margin: 0 auto;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  flex: 1;
`;

const TabContentContainer = styled.div`
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.3;
    }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  max-width: 400px;
`;

const RetryButton = styled.button`
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};
    transform: translateY(-1px);
  }
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>📋</LoadingIcon>
    <LoadingText>Loading Trade Journal...</LoadingText>
  </LoadingState>
);

/**
 * ErrorFallback Component
 */
const ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <ErrorState>
    <ErrorIcon>⚠️</ErrorIcon>
    <ErrorTitle>Journal Error</ErrorTitle>
    <ErrorMessage>{error}</ErrorMessage>
    <RetryButton onClick={onRetry}>Try Again</RetryButton>
  </ErrorState>
);

/**
 * JournalContent Component
 */
const JournalContent: React.FC<F1JournalContainerProps> = ({ initialTab }) => {
  const { trades, isLoading, error, refreshTrades } = useTradeJournal();

  const {
    filters,
    handleFilterChange,
    resetFilters,
    filteredTrades,
    uniqueSetups,
    uniqueModelTypes,
    uniquePrimarySetupTypes,
    uniqueSecondarySetupTypes,
    uniqueLiquidityTypes,
    uniqueDOLTypes,
  } = useTradeFilters(trades as any); // Type assertion for interface compatibility

  const { activeTab, setActiveTab, showFilters } = useJournalNavigation({
    defaultTab: initialTab || 'all',
  });

  // Calculate recent trades (last 7 days)
  const recentTrades = useMemo(() => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    return trades.filter(trade => {
      const tradeDate = new Date(trade.date);
      return tradeDate >= sevenDaysAgo;
    });
  }, [trades]);

  // Calculate trade counts for badges
  const tradeCounts = useMemo(
    () => ({
      total: trades.length,
      recent: recentTrades.length,
      filtered: filteredTrades.length,
    }),
    [trades.length, recentTrades.length, filteredTrades.length]
  );

  // Check if filters are active
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(
      value => value !== '' && value !== null && value !== undefined
    );
  }, [filters]);

  // Prepare data and handlers for tab content
  const tabContentProps = {
    activeTab,
    data: {
      trades: trades as any, // Type assertion for interface compatibility
      filteredTrades,
      recentTrades: recentTrades as any, // Type assertion for interface compatibility
      filters,
      uniqueSetups,
      uniqueModelTypes,
      uniquePrimarySetupTypes,
      uniqueSecondarySetupTypes,
      uniqueLiquidityTypes,
      uniqueDOLTypes,
    },
    isLoading,
    error,
    showFilters,
    handlers: {
      handleFilterChange,
      resetFilters,
      refreshTrades: () => refreshTrades && refreshTrades(),
    },
  };

  // Export handler
  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export trades:', filteredTrades);
  };

  if (error) {
    return <ErrorFallback error={error} onRetry={() => refreshTrades && refreshTrades()} />;
  }

  return (
    <Container>
      {/* F1 Racing Header */}
      <F1JournalHeader
        isLoading={isLoading}
        tradeCount={trades.length}
        filteredCount={filteredTrades.length}
        hasActiveFilters={hasActiveFilters}
        onRefresh={refreshTrades}
        onExport={handleExport}
      />

      {/* F1 Racing Tabs */}
      <F1JournalTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        disabled={isLoading}
        tradeCounts={tradeCounts}
        hasActiveFilters={hasActiveFilters}
      />

      {/* Tab Content */}
      <ContentArea>
        <TabContentContainer>
          <Suspense fallback={<LoadingFallback />}>
            <JournalTabContentRenderer {...tabContentProps} />
          </Suspense>
        </TabContentContainer>
      </ContentArea>
    </Container>
  );
};

/**
 * F1JournalContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const F1JournalContainer: React.FC<F1JournalContainerProps> = props => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <JournalContent {...props} />
    </Suspense>
  );
};

export default F1JournalContainer;
