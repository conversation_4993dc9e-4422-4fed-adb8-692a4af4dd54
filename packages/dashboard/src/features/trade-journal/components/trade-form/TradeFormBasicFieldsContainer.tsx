/**
 * TradeFormBasicFieldsContainer Component
 *
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Main orchestrator for trade form basic fields with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and validation
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense } from 'react';
import styled from 'styled-components';
import { TradeFormFieldGroups } from './TradeFormFieldGroups';
import { useTradeFormFields } from './useTradeFormFields';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
// Removed unused imports - will be added back when needed for real data integration

export interface TradeFormBasicFieldsContainerProps {
  /** Form values */
  formValues: TradeFormValues;
  /** Form values setter */
  setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;
  /** Change handler */
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  /** Validation errors */
  validationErrors: ValidationErrors;
  /** Validation errors setter */
  setValidationErrors?: React.Dispatch<React.SetStateAction<ValidationErrors>>;
  /** Calculate profit/loss callback */
  calculateProfitLoss?: () => void;
  /** Whether form is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const SetupBuilderSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const SetupBuilderHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;

const SetupBuilderTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const SetupBuilderIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;

const SetupBuilderTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const SetupBuilderDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;

const SetupBuilderContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '32px'};
  text-align: center;
  min-height: 200px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.3;
    }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>📝</LoadingIcon>
    <LoadingText>Loading Form Fields...</LoadingText>
  </LoadingState>
);

/**
 * TradeFormFieldsContent Component
 */
const TradeFormFieldsContent: React.FC<TradeFormBasicFieldsContainerProps> = ({
  formValues,
  setFormValues,
  handleChange,
  validationErrors,
  setValidationErrors,
  calculateProfitLoss,
  disabled = false,
}) => {
  // Use enhanced form fields hook if setters are available
  const enhancedHook =
    setFormValues && setValidationErrors
      ? useTradeFormFields({
          formValues,
          setFormValues,
          validationErrors,
          setValidationErrors,
          calculateProfitLoss,
        })
      : null;

  // Use enhanced handlers if available, otherwise fall back to props
  const finalHandleChange = enhancedHook?.handleChange || handleChange;
  const finalHandlePriceChange = enhancedHook?.handlePriceChange || handleChange;

  return (
    <>
      <TradeFormFieldGroups
        formValues={formValues}
        handleChange={finalHandleChange}
        handlePriceChange={finalHandlePriceChange}
        validationErrors={validationErrors}
        disabled={disabled}
      />

      {/* Setup Construction Matrix removed from here - now only in Strategy & Setup section */}
    </>
  );
};

/**
 * TradeFormBasicFieldsContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const TradeFormBasicFieldsContainer: React.FC<TradeFormBasicFieldsContainerProps> = (
  props
) => {
  return (
    <Container className={props.className}>
      <Suspense fallback={<LoadingFallback />}>
        <TradeFormFieldsContent {...props} />
      </Suspense>
    </Container>
  );
};

export default TradeFormBasicFieldsContainer;
