/**
 * Trade Form Strategy Fields Component
 *
 * REFACTORED: Simplified strategy section focusing on core strategy elements.
 * Removed duplicate pattern quality field and legacy setup dropdown.
 * Now uses modern SetupBuilder for setup construction.
 */

import React from 'react';
import styled from 'styled-components';
import {
  // Trade, // Removed as unused
  // TradeFormData, // Removed as unused
  SetupComponents,
  SetupBuilder,
} from '@adhd-trading-dashboard/shared';
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { MODEL_TYPE_OPTIONS } from '../../hooks';
import { SelectDropdown } from '@adhd-trading-dashboard/shared';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const TextArea = styled.textarea`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  min-height: 100px;

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

interface TradeFormStrategyFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;
}

/**
 * Trade Form Strategy Fields Component
 */
const TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps> = ({
  formValues,
  handleChange,
  // validationErrors, // Removed as unused
  setFormValues,
}) => {
  return (
    <>
      {/* Core Strategy Section */}
      <SectionTitle>Core Strategy</SectionTitle>
      <FormRow>
        <FormGroup>
          <SelectDropdown
            id='modelType'
            name='modelType'
            label='Trading Model'
            value={formValues.modelType || ''}
            onChange={handleChange}
            options={MODEL_TYPE_OPTIONS}
            placeholder='Select Trading Model'
          />
        </FormGroup>
      </FormRow>

      <Divider />

      {/* Setup Construction Matrix */}
      {setFormValues && (
        <>
          <SectionTitle>Setup Construction</SectionTitle>
          <SetupBuilder
            onSetupChange={(components: SetupComponents) => {
              setFormValues(prev => ({
                ...prev,
                setupComponents: components,
              }));
            }}
            initialComponents={formValues.setupComponents}
          />
          <Divider />
        </>
      )}

      {/* Notes Section */}
      <SectionTitle>Strategy Notes</SectionTitle>
      <FormGroup>
        <Label htmlFor='notes'>Trade Notes & Strategy Details</Label>
        <TextArea
          id='notes'
          name='notes'
          value={formValues.notes || ''}
          onChange={handleChange}
          placeholder='Add notes about your trading strategy, setup reasoning, market conditions, etc...'
        />
      </FormGroup>
    </>
  );
};

export default TradeFormStrategyFields;
