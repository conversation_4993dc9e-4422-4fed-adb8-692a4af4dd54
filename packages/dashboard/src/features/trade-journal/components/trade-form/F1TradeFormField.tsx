/**
 * F1TradeFormField Component
 * 
 * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)
 * Enhanced F1FormField specifically designed for trading forms.
 * 
 * BENEFITS:
 * - Focused responsibility (single form field)
 * - F1 racing theme with trading-specific styling
 * - Built-in validation and error handling
 * - Supports all trading field types
 * - Reusable across all trading forms
 */

import React from 'react';
import styled from 'styled-components';

export type TradeFieldType = 
  | 'text' 
  | 'number' 
  | 'date' 
  | 'select' 
  | 'price' 
  | 'quantity'
  | 'percentage';

export interface TradeFieldOption {
  value: string | number;
  label: string;
}

export interface F1TradeFormFieldProps {
  /** Field identifier */
  name: string;
  /** Field label */
  label: string;
  /** Field type */
  type: TradeFieldType;
  /** Current value */
  value: any;
  /** Change handler */
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  /** Options for select fields */
  options?: TradeFieldOption[];
  /** Input props for additional configuration */
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  /** Validation error */
  error?: string;
  /** Whether field is required */
  required?: boolean;
  /** Whether field is disabled */
  disabled?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Custom className */
  className?: string;
}

const FieldContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  min-width: 0;
`;

const FieldLabel = styled.label<{ $required?: boolean }>`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  
  /* F1 Racing style */
  ${({ $required, theme }) =>
    $required &&
    `
    &::after {
      content: '*';
      color: ${theme.colors?.primary || 'var(--primary-color)'};
      margin-left: 4px;
      font-weight: 700;
    }
  `}
`;

const BaseInput = styled.input<{ $hasError?: boolean; $fieldType?: TradeFieldType }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasError }) => 
    $hasError 
      ? theme.colors?.error || 'var(--error-color)'
      : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  transition: all 0.2s ease;
  width: 100%;
  
  /* F1 Racing focus effect */
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Trading-specific styling */
  ${({ $fieldType, theme }) => {
    switch ($fieldType) {
      case 'price':
      case 'quantity':
        return `
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          text-align: right;
          font-weight: 600;
        `;
      case 'percentage':
        return `
          text-align: right;
          &::after {
            content: '%';
            position: absolute;
            right: 12px;
            color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};
          }
        `;
      default:
        return '';
    }
  }}
`;

const Select = styled.select<{ $hasError?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme, $hasError }) => 
    $hasError 
      ? theme.colors?.error || 'var(--error-color)'
      : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  
  /* F1 Racing focus effect */
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Custom dropdown arrow */
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
`;

const ErrorMessage = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  
  &::before {
    content: '⚠️';
    font-size: 12px;
  }
`;

/**
 * F1TradeFormField Component
 * 
 * PATTERN: F1 Form Field Pattern for Trading
 * - Racing-inspired styling with red accents
 * - Trading-specific field types and formatting
 * - Built-in validation and error states
 * - Accessible with proper labels and focus
 * - Optimized for trading data entry
 */
export const F1TradeFormField: React.FC<F1TradeFormFieldProps> = ({
  name,
  label,
  type,
  value,
  onChange,
  options = [],
  inputProps = {},
  error,
  required = false,
  disabled = false,
  placeholder,
  className,
}) => {
  const fieldId = `trade-field-${name}`;
  
  const getInputType = (): string => {
    switch (type) {
      case 'price':
      case 'quantity':
      case 'percentage':
        return 'number';
      case 'date':
        return 'date';
      default:
        return 'text';
    }
  };
  
  const getInputProps = () => {
    const baseProps = {
      id: fieldId,
      name,
      value: value || '',
      onChange,
      required,
      disabled,
      placeholder,
      ...inputProps,
    };
    
    switch (type) {
      case 'price':
        return {
          ...baseProps,
          type: 'number',
          step: '0.01',
          min: '0',
        };
      case 'quantity':
        return {
          ...baseProps,
          type: 'number',
          step: '1',
          min: '0',
        };
      case 'percentage':
        return {
          ...baseProps,
          type: 'number',
          step: '0.1',
          min: '0',
          max: '100',
        };
      default:
        return {
          ...baseProps,
          type: getInputType(),
        };
    }
  };
  
  const renderControl = () => {
    if (type === 'select') {
      return (
        <Select
          id={fieldId}
          name={name}
          value={value || ''}
          onChange={onChange}
          required={required}
          disabled={disabled}
          $hasError={!!error}
        >
          {!required && <option value="">Select {label}</option>}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      );
    }
    
    return (
      <BaseInput
        {...getInputProps()}
        $hasError={!!error}
        $fieldType={type}
      />
    );
  };
  
  return (
    <FieldContainer className={className}>
      <FieldLabel htmlFor={fieldId} $required={required}>
        {label}
      </FieldLabel>
      
      {renderControl()}
      
      {error && (
        <ErrorMessage role="alert">
          {error}
        </ErrorMessage>
      )}
    </FieldContainer>
  );
};

export default F1TradeFormField;
