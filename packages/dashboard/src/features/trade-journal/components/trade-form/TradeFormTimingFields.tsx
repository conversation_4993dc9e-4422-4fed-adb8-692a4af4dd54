/**
 * Trade Form Timing Fields Component
 *
 * REFACTORED: Consolidated timing sections into unified component.
 * Combines entry timing, session selection, and trade timing analysis
 * into a single cohesive section following compositional architecture.
 */

import React from 'react';
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';
import { MARKET_OPTIONS } from '../../hooks';
import {
  TimePicker,
  SelectDropdown,
  Input,
  HierarchicalSessionSelector,
  SessionSelection,
  SessionUtils,
} from '@adhd-trading-dashboard/shared';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
`;

const TimingSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const TimingSectionHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;

const TimingSectionTitleRow = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const TimingSectionIcon = styled.div`
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
`;

const TimingSectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const TimingSectionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;

const TimingSectionContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const HelpText = styled.span`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  line-height: 1.4;
`;

const ValidationError = styled.span`
  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};
  font-weight: 500;
`;

interface TradeFormTimingFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
}

/**
 * Trade Form Timing Fields Component
 */
const TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
}) => {
  return (
    <Container>
      {/* Unified Trade Timing Section */}
      <TimingSection>
        <TimingSectionHeader>
          <TimingSectionTitleRow>
            <TimingSectionIcon>📅</TimingSectionIcon>
            <div>
              <TimingSectionTitle>Trade Timing & Execution</TimingSectionTitle>
              <TimingSectionDescription>
                Complete timing details from entry to exit including market context
              </TimingSectionDescription>
            </div>
          </TimingSectionTitleRow>
        </TimingSectionHeader>

        <TimingSectionContent>
          {/* Entry Timing Row */}
          <FormRow>
            <FormGroup>
              <Label htmlFor="entryDate">Entry Date</Label>
              <Input
                id="entryDate"
                name="entryDate"
                type="date"
                value={formValues.entryDate || ''}
                onChange={handleChange}
              />
              {validationErrors.entryDate && (
                <ValidationError>{validationErrors.entryDate}</ValidationError>
              )}
              <HelpText>Date when the trade was entered</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="entryTime">Entry Time</Label>
              <TimePicker
                id="entryTime"
                name="entryTime"
                value={formValues.entryTime || ''}
                onChange={handleChange}
              />
              {validationErrors.entryTime && (
                <ValidationError>{validationErrors.entryTime}</ValidationError>
              )}
              <HelpText>Exact time of trade entry</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="market">Market</Label>
              <SelectDropdown
                id="market"
                name="market"
                value={formValues.market || 'Stocks'}
                onChange={handleChange}
                options={MARKET_OPTIONS}
              />
              <HelpText>Market type being traded</HelpText>
            </FormGroup>
          </FormRow>

          {/* Trade Execution Timeline Row */}
          <FormRow>
            <FormGroup>
              <Label htmlFor="rdTime">RD Formation Time</Label>
              <TimePicker
                id="rdTime"
                name="rdTime"
                value={formValues.rdTime || ''}
                onChange={handleChange}
              />
              {validationErrors.rdTime && (
                <ValidationError>{validationErrors.rdTime}</ValidationError>
              )}
              <HelpText>Time when the Relative Divergence (RD) pattern formed</HelpText>
            </FormGroup>

            <FormGroup>
              <Label htmlFor="exitTime">Exit Time</Label>
              <TimePicker
                id="exitTime"
                name="exitTime"
                value={formValues.exitTime || ''}
                onChange={handleChange}
              />
              {validationErrors.exitTime && (
                <ValidationError>{validationErrors.exitTime}</ValidationError>
              )}
              <HelpText>Time when trade was exited</HelpText>
            </FormGroup>
          </FormRow>

          {/* Session & Context Row */}
          <FormRow>
            <FormGroup>
              <Label htmlFor="session">Trading Session</Label>
              <HierarchicalSessionSelector
                value={formValues.session || ''}
                onChange={(value) => {
                  const event = {
                    target: { name: 'session', value },
                  } as React.ChangeEvent<HTMLSelectElement>;
                  handleChange(event);
                }}
                validationError={validationErrors.session}
              />
              <HelpText>Trading session and macro period</HelpText>
            </FormGroup>
          </FormRow>
        </TimingSectionContent>
      </TimingSection>
    </Container>
  );
};

export default TradeFormTimingFields;
