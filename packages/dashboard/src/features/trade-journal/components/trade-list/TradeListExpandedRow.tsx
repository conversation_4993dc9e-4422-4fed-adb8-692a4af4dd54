/**
 * Trade List Expanded Row Component
 *
 * Displays expanded details for a trade row
 */

import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';

const ExpandedContent = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-top: ${({ theme }) => theme.spacing.xs};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const ExpandedSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const DetailValue = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const ActionButton = styled(Link)`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  text-decoration: none;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  transition: background-color ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`;

interface TradeListExpandedRowProps {
  trade: CompleteTradeData;
}

/**
 * Trade List Expanded Row Component
 */
const TradeListExpandedRow: React.FC<TradeListExpandedRowProps> = ({ trade }) => {
  return (
    <ExpandedContent>
      <ExpandedSection>
        <SectionTitle>Trade Details</SectionTitle>
        <DetailGrid>
          <DetailItem>
            <DetailLabel>Market</DetailLabel>
            <DetailValue>{trade.trade.market || 'N/A'}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Date</DetailLabel>
            <DetailValue>{trade.trade.date}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Direction</DetailLabel>
            <DetailValue>{trade.trade.direction}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Entry Price</DetailLabel>
            <DetailValue>${(trade.trade.entry_price || 0).toFixed(2)}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Exit Price</DetailLabel>
            <DetailValue>${(trade.trade.exit_price || 0).toFixed(2)}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Contracts</DetailLabel>
            <DetailValue>{trade.trade.no_of_contracts || 0}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Profit/Loss</DetailLabel>
            <DetailValue
              style={{
                color:
                  (trade.trade.achieved_pl || 0) > 0
                    ? 'green'
                    : (trade.trade.achieved_pl || 0) < 0
                    ? 'red'
                    : 'inherit',
              }}
            >
              ${(trade.trade.achieved_pl || 0).toFixed(2)}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>R-Multiple</DetailLabel>
            <DetailValue>{trade.trade.r_multiple?.toFixed(2) || 'N/A'}</DetailValue>
          </DetailItem>
        </DetailGrid>
      </ExpandedSection>

      {trade.setup && (
        <ExpandedSection>
          <SectionTitle>Strategy</SectionTitle>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>Model Type</DetailLabel>
              <DetailValue>{trade.trade.model_type || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Session</DetailLabel>
              <DetailValue>{trade.trade.session || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Primary Setup</DetailLabel>
              <DetailValue>{trade.setup.primary_setup || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Secondary Setup</DetailLabel>
              <DetailValue>{trade.setup.secondary_setup || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Liquidity Taken</DetailLabel>
              <DetailValue>{trade.setup.liquidity_taken || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Pattern Quality</DetailLabel>
              <DetailValue>{trade.trade.pattern_quality_rating || 'N/A'}</DetailValue>
            </DetailItem>
          </DetailGrid>
        </ExpandedSection>
      )}

      {trade.analysis && (
        <ExpandedSection>
          <SectionTitle>Analysis</SectionTitle>
          <DetailGrid>
            <DetailItem>
              <DetailLabel>Execution Quality</DetailLabel>
              <DetailValue>{(trade.analysis as any)?.execution_quality || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Lessons Learned</DetailLabel>
              <DetailValue>{(trade.analysis as any)?.lessons_learned || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Emotional State</DetailLabel>
              <DetailValue>{(trade.analysis as any)?.emotional_state || 'N/A'}</DetailValue>
            </DetailItem>
            <DetailItem>
              <DetailLabel>Market Conditions</DetailLabel>
              <DetailValue>{(trade.analysis as any)?.market_conditions || 'N/A'}</DetailValue>
            </DetailItem>
          </DetailGrid>
        </ExpandedSection>
      )}

      {trade.trade.notes && (
        <ExpandedSection>
          <SectionTitle>Notes</SectionTitle>
          <DetailItem>
            <DetailValue>{trade.trade.notes}</DetailValue>
          </DetailItem>
        </ExpandedSection>
      )}

      <ActionButtons>
        <ActionButton to={`/trade-journal/edit/${trade.trade.id}`}>Edit Trade</ActionButton>
      </ActionButtons>
    </ExpandedContent>
  );
};

export default TradeListExpandedRow;
