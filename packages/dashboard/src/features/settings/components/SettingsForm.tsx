/**
 * SettingsForm Component
 *
 * REFACTORED FROM: Settings.tsx (271 lines → focused components)
 * F1 racing-themed form with organized sections and validation.
 *
 * BENEFITS:
 * - Focused responsibility (form logic only)
 * - Organized sections with clear hierarchy
 * - F1 racing theme with consistent styling
 * - Built-in validation and error handling
 * - Reusable form field components
 */

import React from 'react';
import styled from 'styled-components';
import { SettingsFormField, FieldOption } from './SettingsFormField';

export interface SettingsFormData {
  theme: string;
  refreshInterval: number;
  showNotifications: boolean;
  enableAdvancedMetrics: boolean;
  autoSaveJournal: boolean;
}

export interface SettingsFormProps {
  /** Form data */
  data: SettingsFormData;
  /** Change handler */
  onChange: (name: string, value: any) => void;
  /** Validation errors */
  errors?: Record<string, string>;
  /** Whether form is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const FormSection = styled.div`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const SectionHeader = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
  }
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.xs || '4px'} 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;

const SectionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  margin: 0;
  line-height: 1.5;
`;

const SectionContent = styled.div`
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const FieldGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

/**
 * Form field configurations
 */
const THEME_OPTIONS: FieldOption[] = [
  { value: 'mercedes-green', label: 'Mercedes Green' },
  { value: 'f1-official', label: 'F1 Official' },
  { value: 'dark', label: 'Dark Theme' },
];

/**
 * SettingsForm Component
 *
 * PATTERN: F1 Form Pattern
 * - Racing-inspired section styling
 * - Organized field groups with clear hierarchy
 * - Consistent form field components
 * - Built-in validation and error handling
 * - Responsive design for all screen sizes
 */
export const SettingsForm: React.FC<SettingsFormProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false,
  className,
}) => {
  return (
    <FormContainer className={className}>
      {/* Appearance Section */}
      <FormSection>
        <SectionHeader>
          <SectionTitle>Appearance</SectionTitle>
          <SectionDescription>
            Customize the visual appearance and theme of your trading dashboard
          </SectionDescription>
        </SectionHeader>

        <SectionContent>
          <FieldGroup>
            <SettingsFormField
              name="theme"
              label="Theme"
              description="Choose your preferred visual theme"
              type="select"
              value={data.theme}
              onChange={onChange}
              options={THEME_OPTIONS}
              error={errors.theme}
              disabled={disabled}
            />
          </FieldGroup>
        </SectionContent>
      </FormSection>

      {/* General Settings Section */}
      <FormSection>
        <SectionHeader>
          <SectionTitle>General Settings</SectionTitle>
          <SectionDescription>
            Configure general application behavior and performance settings
          </SectionDescription>
        </SectionHeader>

        <SectionContent>
          <FieldGroup>
            <SettingsFormField
              name="refreshInterval"
              label="Data Refresh Interval"
              description="How often to refresh dashboard data (in minutes)"
              type="number"
              value={data.refreshInterval}
              onChange={onChange}
              inputProps={{
                min: 1,
                max: 60,
                step: 1,
                style: { width: '100px' },
              }}
              error={errors.refreshInterval}
              disabled={disabled}
            />

            <SettingsFormField
              name="showNotifications"
              label="Desktop Notifications"
              description="Enable desktop notifications for important events and alerts"
              type="toggle"
              value={data.showNotifications}
              onChange={onChange}
              error={errors.showNotifications}
              disabled={disabled}
            />

            <SettingsFormField
              name="enableAdvancedMetrics"
              label="Advanced Metrics"
              description="Show additional performance metrics and detailed analytics"
              type="toggle"
              value={data.enableAdvancedMetrics}
              onChange={onChange}
              error={errors.enableAdvancedMetrics}
              disabled={disabled}
            />

            <SettingsFormField
              name="autoSaveJournal"
              label="Auto-Save Trade Journal"
              description="Automatically save trade entries as you type to prevent data loss"
              type="toggle"
              value={data.autoSaveJournal}
              onChange={onChange}
              error={errors.autoSaveJournal}
              disabled={disabled}
            />
          </FieldGroup>
        </SectionContent>
      </FormSection>
    </FormContainer>
  );
};

export default SettingsForm;
