// React import handled by <PERSON><PERSON><PERSON> transform
import { useState, useEffect } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';
import { ThemeTestPanel } from './components/ThemeTestPanel';
import './styles/unified-theme.css';
import './styles/components/session-cards.css';
import './styles/components/pd-arrays.css';

/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
  const [showThemeTest, setShowThemeTest] = useState(false);

  // Show theme test panel on Ctrl+T
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.ctrlKey && e.key === 't') {
      e.preventDefault();
      setShowThemeTest(!showThemeTest);
    }
  };

  // Add keyboard listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showThemeTest]);

  return (
    <AppErrorBoundary>
      <ThemeProvider initialTheme="mercedes-green">
        <BrowserRouter>
          <AppRoutes />
          {showThemeTest && <ThemeTestPanel onClose={() => setShowThemeTest(false)} />}
        </BrowserRouter>
      </ThemeProvider>
    </AppErrorBoundary>
  );
}

export default App;
