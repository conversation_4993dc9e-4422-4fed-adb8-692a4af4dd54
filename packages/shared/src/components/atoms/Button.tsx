/**
 * Button Component
 *
 * A customizable button component that follows the design system.
 */
import React from 'react';
import styled, { css, keyframes } from 'styled-components';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text' | 'success' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'size' | 'type'> {
  /** The content to display inside the button */
  children: React.ReactNode;
  /** The variant of the button */
  variant?: ButtonVariant;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Whether the button is in a loading state */
  loading?: boolean;
  /** The size of the button */
  size?: ButtonSize;
  /** Whether the button is full width */
  fullWidth?: boolean;
  /** Icon to display before the button text */
  startIcon?: React.ReactNode;
  /** Icon to display after the button text */
  endIcon?: React.ReactNode;
  /** Function called when the button is clicked */
  onClick?: () => void;
  /** Additional CSS class names */
  className?: string;
  /** Button type */
  type?: 'button' | 'submit' | 'reset';
}

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: ${spin} 0.8s linear infinite;
  margin-right: ${({ theme }) => theme.spacing.xs};
`;

// Size styles
const sizeStyles = {
  small: css`
    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};
    font-size: ${({ theme }) => theme.fontSizes.xs};
    min-height: 32px;
  `,
  medium: css`
    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.md}`};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    min-height: 40px;
  `,
  large: css`
    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.lg}`};
    font-size: ${({ theme }) => theme.fontSizes.md};
    min-height: 48px;
  `,
};

// Variant styles
const variantStyles = {
  primary: css`
    background-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primaryDark};
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primaryDark};
      transform: translateY(0);
      box-shadow: none;
    }
  `,
  secondary: css`
    background-color: ${({ theme }) => theme.colors.secondary};
    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.secondaryDark};
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.secondaryDark};
      transform: translateY(0);
      box-shadow: none;
    }
  `,
  outline: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: 1px solid ${({ theme }) => theme.colors.primary};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */
      transform: translateY(0);
    }
  `,
  text: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: none;
    padding-left: ${({ theme }) => theme.spacing.xs};
    padding-right: ${({ theme }) => theme.spacing.xs};

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */
    }

    &:active:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */
    }
  `,
  success: css`
    background-color: ${({ theme }) => theme.colors.success};
    color: ${({ theme }) => theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.success}dd;
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: none;
    }
  `,
  danger: css`
    background-color: ${({ theme }) => theme.colors.error};
    color: ${({ theme }) => theme.colors.textInverse || '#fff'};
    border: none;

    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.error}dd;
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: none;
    }
  `,
};

// Define the props that StyledButton will accept
type StyledButtonProps = {
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  fullWidth?: boolean;
  $hasStartIcon?: boolean;
  $hasEndIcon?: boolean;
};

const StyledButton = styled.button<StyledButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};
  position: relative;
  overflow: hidden;

  /* Apply size styles */
  ${({ size = 'medium' }) => sizeStyles[size]}

  /* Apply variant styles */
  ${({ variant = 'primary' }) => variantStyles[variant]}

  /* Full width style */
  ${({ fullWidth }) =>
    fullWidth &&
    css`
      width: 100%;
    `}

  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
    transform: translateY(0);
  }

  /* Icon spacing */
  ${({ $hasStartIcon }) =>
    $hasStartIcon &&
    css`
      & > *:first-child {
        margin-right: ${({ theme }) => theme.spacing.xs};
      }
    `}

  ${({ $hasEndIcon }) =>
    $hasEndIcon &&
    css`
      & > *:last-child {
        margin-left: ${({ theme }) => theme.spacing.xs};
      }
    `}
`;

const ButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

/**
 * Button Component
 *
 * A customizable button component that follows the design system.
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary' as ButtonVariant,
  disabled = false,
  loading = false,
  size = 'medium' as ButtonSize,
  fullWidth = false,
  startIcon,
  endIcon,
  onClick,
  className = '',
  type = 'button' as 'button' | 'submit' | 'reset',
  ...rest
}) => {
  return (
    <StyledButton
      variant={variant}
      disabled={disabled || loading}
      size={size}
      fullWidth={fullWidth}
      onClick={onClick}
      className={className}
      type={type}
      $hasStartIcon={!!startIcon && !loading}
      $hasEndIcon={!!endIcon && !loading}
      {...rest}
    >
      <ButtonContent>
        {loading && <LoadingSpinner />}
        {!loading && startIcon}
        {children}
        {!loading && endIcon}
      </ButtonContent>
    </StyledButton>
  );
};
