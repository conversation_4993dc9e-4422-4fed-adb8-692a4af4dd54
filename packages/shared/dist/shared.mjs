var no = Object.defineProperty;
var so = (e, r, t) => r in e ? no(e, r, { enumerable: !0, configurable: !0, writable: !0, value: t }) : e[r] = t;
var we = (e, r, t) => (so(e, typeof r != "symbol" ? r + "" : r, t), t);
import Se, { useState as V, useRef as St, useEffect as ae, Component as io, useMemo as U, useCallback as F, Suspense as ao, createContext as Ct, useContext as It, useReducer as co } from "react";
import c, { css as g, keyframes as ke, createGlobalStyle as lo, ThemeProvider as po } from "styled-components";
import { createPortal as uo } from "react-dom";
var fo = /* @__PURE__ */ ((e) => (e.LONG = "LONG", e.SHORT = "SHORT", e))(fo || {}), go = /* @__PURE__ */ ((e) => (e.OPEN = "OPEN", e.CLOSED = "CLOSED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e.PENDING = "PENDING", e))(go || {}), mo = /* @__PURE__ */ ((e) => (e.MARKET = "MARKET", e.LIMIT = "LIMIT", e.STOP = "STOP", e.STOP_LIMIT = "STOP_LIMIT", e))(mo || {}), ho = /* @__PURE__ */ ((e) => (e.BUY = "BUY", e.SELL = "SELL", e))(ho || {}), xo = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.FILLED = "FILLED", e.PARTIALLY_FILLED = "PARTIALLY_FILLED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e))(xo || {}), bo = /* @__PURE__ */ ((e) => (e.GTC = "GTC", e.IOC = "IOC", e.FOK = "FOK", e.DAY = "DAY", e))(bo || {});
const Ur = {
  /**
   * Convert CompleteTradeData to legacy Trade interface
   */
  completeTradeToLegacy: (e) => {
    var t;
    const r = e.trade;
    return {
      id: ((t = r.id) == null ? void 0 : t.toString()) || "0",
      symbol: r.market || "MNQ",
      date: r.date,
      direction: r.direction,
      size: r.no_of_contracts || 1,
      entry: r.entry_price || 0,
      exit: r.exit_price || 0,
      stopLoss: 0,
      // Not in new schema
      takeProfit: 0,
      // Not in new schema
      profitLoss: r.achieved_pl || 0,
      strategy: r.setup || "",
      notes: r.notes || "",
      tags: [],
      // Not in new schema
      images: []
      // Not in new schema
    };
  },
  /**
   * Convert legacy Trade to CompleteTradeData
   */
  legacyToCompleteTrade: (e) => ({
    trade: {
      id: parseInt(e.id) || void 0,
      date: e.date,
      model_type: "Unknown",
      direction: e.direction,
      market: e.symbol,
      entry_price: e.entry,
      exit_price: e.exit,
      achieved_pl: e.profitLoss,
      no_of_contracts: e.size,
      setup: e.strategy,
      notes: e.notes,
      created_at: (/* @__PURE__ */ new Date()).toISOString(),
      updated_at: (/* @__PURE__ */ new Date()).toISOString()
    }
  }),
  /**
   * Convert array of CompleteTradeData to legacy Trade array
   */
  completeTradeArrayToLegacy: (e) => e.map(Ur.completeTradeToLegacy),
  /**
   * Convert array of legacy Trade to CompleteTradeData array
   */
  legacyArrayToCompleteTrade: (e) => e.map(Ur.legacyToCompleteTrade)
};
var Q = /* @__PURE__ */ ((e) => (e.LONDON = "london", e.NEW_YORK_AM = "new-york-am", e.NEW_YORK_PM = "new-york-pm", e.ASIA = "asia", e.PRE_MARKET = "pre-market", e.AFTER_HOURS = "after-hours", e.OVERNIGHT = "overnight", e))(Q || {}), D = /* @__PURE__ */ ((e) => (e.MORNING_BREAKOUT = "morning-breakout", e.MID_MORNING_REVERSION = "mid-morning-reversion", e.PRE_LUNCH = "pre-lunch", e.LUNCH_MACRO_EXTENDED = "lunch-macro-extended", e.LUNCH_MACRO = "lunch-macro", e.POST_LUNCH = "post-lunch", e.PRE_CLOSE = "pre-close", e.POWER_HOUR = "power-hour", e.MOC = "moc", e.LONDON_OPEN = "london-open", e.LONDON_NY_OVERLAP = "london-ny-overlap", e.CUSTOM = "custom", e))(D || {});
const Le = {
  constant: {
    parentArrays: ["NWOG", "Old-NWOG", "NDOG", "Old-NDOG", "Monthly-FVG", "Weekly-FVG", "Daily-FVG", "15min-Top/Bottom-FVG", "1h-Top/Bottom-FVG"],
    fvgTypes: ["Strong-FVG", "AM-FPFVG", "PM-FPFVG", "Asia-FPFVG", "Premarket-FPFVG", "MNOR-FVG", "Macro-FVG", "News-FVG", "Top/Bottom-FVG"]
  },
  action: {
    liquidityEvents: ["None", "London-H/L", "Premarket-H/L", "09:30-Opening-Range-H/L", "Lunch-H/L", "Prev-Day-H/L", "Prev-Week-H/L", "Monthly-H/L", "Macro-H/L"]
  },
  variable: {
    rdTypes: ["None", "True-RD", "IMM-RD", "Dispersed-RD", "Wide-Gap-RD"]
  },
  entry: {
    methods: ["Simple-Entry", "Complex-Entry", "Complex-Entry/Mini"]
  }
}, yc = ["RD-Cont", "FVG-RD", "Combined"];
var o = {}, yo = {
  get exports() {
    return o;
  },
  set exports(e) {
    o = e;
  }
}, Re = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Vr;
function vo() {
  if (Vr)
    return Re;
  Vr = 1;
  var e = Se, r = Symbol.for("react.element"), t = Symbol.for("react.fragment"), n = Object.prototype.hasOwnProperty, s = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, a = { key: !0, ref: !0, __self: !0, __source: !0 };
  function i(u, d, f) {
    var l, m = {}, b = null, x = null;
    f !== void 0 && (b = "" + f), d.key !== void 0 && (b = "" + d.key), d.ref !== void 0 && (x = d.ref);
    for (l in d)
      n.call(d, l) && !a.hasOwnProperty(l) && (m[l] = d[l]);
    if (u && u.defaultProps)
      for (l in d = u.defaultProps, d)
        m[l] === void 0 && (m[l] = d[l]);
    return { $$typeof: r, type: u, key: b, ref: x, props: m, _owner: s.current };
  }
  return Re.Fragment = t, Re.jsx = i, Re.jsxs = i, Re;
}
var Me = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Yr;
function wo() {
  return Yr || (Yr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Se, r = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), s = Symbol.for("react.strict_mode"), a = Symbol.for("react.profiler"), i = Symbol.for("react.provider"), u = Symbol.for("react.context"), d = Symbol.for("react.forward_ref"), f = Symbol.for("react.suspense"), l = Symbol.for("react.suspense_list"), m = Symbol.for("react.memo"), b = Symbol.for("react.lazy"), x = Symbol.for("react.offscreen"), h = Symbol.iterator, y = "@@iterator";
    function I(p) {
      if (p === null || typeof p != "object")
        return null;
      var v = h && p[h] || p[y];
      return typeof v == "function" ? v : null;
    }
    var S = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function C(p) {
      {
        for (var v = arguments.length, j = new Array(v > 1 ? v - 1 : 0), P = 1; P < v; P++)
          j[P - 1] = arguments[P];
        R("error", p, j);
      }
    }
    function R(p, v, j) {
      {
        var P = S.ReactDebugCurrentFrame, B = P.getStackAddendum();
        B !== "" && (v += "%s", j = j.concat([B]));
        var Y = j.map(function(z) {
          return String(z);
        });
        Y.unshift("Warning: " + v), Function.prototype.apply.call(console[p], console, Y);
      }
    }
    var A = !1, N = !1, k = !1, M = !1, E = !1, _;
    _ = Symbol.for("react.module.reference");
    function K(p) {
      return !!(typeof p == "string" || typeof p == "function" || p === n || p === a || E || p === s || p === f || p === l || M || p === x || A || N || k || typeof p == "object" && p !== null && (p.$$typeof === b || p.$$typeof === m || p.$$typeof === i || p.$$typeof === u || p.$$typeof === d || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      p.$$typeof === _ || p.getModuleId !== void 0));
    }
    function W(p, v, j) {
      var P = p.displayName;
      if (P)
        return P;
      var B = v.displayName || v.name || "";
      return B !== "" ? j + "(" + B + ")" : j;
    }
    function L(p) {
      return p.displayName || "Context";
    }
    function H(p) {
      if (p == null)
        return null;
      if (typeof p.tag == "number" && C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof p == "function")
        return p.displayName || p.name || null;
      if (typeof p == "string")
        return p;
      switch (p) {
        case n:
          return "Fragment";
        case t:
          return "Portal";
        case a:
          return "Profiler";
        case s:
          return "StrictMode";
        case f:
          return "Suspense";
        case l:
          return "SuspenseList";
      }
      if (typeof p == "object")
        switch (p.$$typeof) {
          case u:
            var v = p;
            return L(v) + ".Consumer";
          case i:
            var j = p;
            return L(j._context) + ".Provider";
          case d:
            return W(p, p.render, "ForwardRef");
          case m:
            var P = p.displayName || null;
            return P !== null ? P : H(p.type) || "Memo";
          case b: {
            var B = p, Y = B._payload, z = B._init;
            try {
              return H(z(Y));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var re = Object.assign, te = 0, fe, oe, $, J, ve, q, me;
    function kr() {
    }
    kr.__reactDisabledLog = !0;
    function Mt() {
      {
        if (te === 0) {
          fe = console.log, oe = console.info, $ = console.warn, J = console.error, ve = console.group, q = console.groupCollapsed, me = console.groupEnd;
          var p = {
            configurable: !0,
            enumerable: !0,
            value: kr,
            writable: !0
          };
          Object.defineProperties(console, {
            info: p,
            log: p,
            warn: p,
            error: p,
            group: p,
            groupCollapsed: p,
            groupEnd: p
          });
        }
        te++;
      }
    }
    function Pt() {
      {
        if (te--, te === 0) {
          var p = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: re({}, p, {
              value: fe
            }),
            info: re({}, p, {
              value: oe
            }),
            warn: re({}, p, {
              value: $
            }),
            error: re({}, p, {
              value: J
            }),
            group: re({}, p, {
              value: ve
            }),
            groupCollapsed: re({}, p, {
              value: q
            }),
            groupEnd: re({}, p, {
              value: me
            })
          });
        }
        te < 0 && C("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var sr = S.ReactCurrentDispatcher, ir;
    function ze(p, v, j) {
      {
        if (ir === void 0)
          try {
            throw Error();
          } catch (B) {
            var P = B.stack.trim().match(/\n( *(at )?)/);
            ir = P && P[1] || "";
          }
        return `
` + ir + p;
      }
    }
    var ar = !1, Fe;
    {
      var Dt = typeof WeakMap == "function" ? WeakMap : Map;
      Fe = new Dt();
    }
    function _r(p, v) {
      if (!p || ar)
        return "";
      {
        var j = Fe.get(p);
        if (j !== void 0)
          return j;
      }
      var P;
      ar = !0;
      var B = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var Y;
      Y = sr.current, sr.current = null, Mt();
      try {
        if (v) {
          var z = function() {
            throw Error();
          };
          if (Object.defineProperty(z.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(z, []);
            } catch (he) {
              P = he;
            }
            Reflect.construct(p, [], z);
          } else {
            try {
              z.call();
            } catch (he) {
              P = he;
            }
            p.call(z.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (he) {
            P = he;
          }
          p();
        }
      } catch (he) {
        if (he && P && typeof he.stack == "string") {
          for (var O = he.stack.split(`
`), ne = P.stack.split(`
`), Z = O.length - 1, ee = ne.length - 1; Z >= 1 && ee >= 0 && O[Z] !== ne[ee]; )
            ee--;
          for (; Z >= 1 && ee >= 0; Z--, ee--)
            if (O[Z] !== ne[ee]) {
              if (Z !== 1 || ee !== 1)
                do
                  if (Z--, ee--, ee < 0 || O[Z] !== ne[ee]) {
                    var de = `
` + O[Z].replace(" at new ", " at ");
                    return p.displayName && de.includes("<anonymous>") && (de = de.replace("<anonymous>", p.displayName)), typeof p == "function" && Fe.set(p, de), de;
                  }
                while (Z >= 1 && ee >= 0);
              break;
            }
        }
      } finally {
        ar = !1, sr.current = Y, Pt(), Error.prepareStackTrace = B;
      }
      var Ie = p ? p.displayName || p.name : "", Hr = Ie ? ze(Ie) : "";
      return typeof p == "function" && Fe.set(p, Hr), Hr;
    }
    function $t(p, v, j) {
      return _r(p, !1);
    }
    function Ot(p) {
      var v = p.prototype;
      return !!(v && v.isReactComponent);
    }
    function Be(p, v, j) {
      if (p == null)
        return "";
      if (typeof p == "function")
        return _r(p, Ot(p));
      if (typeof p == "string")
        return ze(p);
      switch (p) {
        case f:
          return ze("Suspense");
        case l:
          return ze("SuspenseList");
      }
      if (typeof p == "object")
        switch (p.$$typeof) {
          case d:
            return $t(p.render);
          case m:
            return Be(p.type, v, j);
          case b: {
            var P = p, B = P._payload, Y = P._init;
            try {
              return Be(Y(B), v, j);
            } catch {
            }
          }
        }
      return "";
    }
    var qe = Object.prototype.hasOwnProperty, Lr = {}, Rr = S.ReactDebugCurrentFrame;
    function He(p) {
      if (p) {
        var v = p._owner, j = Be(p.type, p._source, v ? v.type : null);
        Rr.setExtraStackFrame(j);
      } else
        Rr.setExtraStackFrame(null);
    }
    function At(p, v, j, P, B) {
      {
        var Y = Function.call.bind(qe);
        for (var z in p)
          if (Y(p, z)) {
            var O = void 0;
            try {
              if (typeof p[z] != "function") {
                var ne = Error((P || "React class") + ": " + j + " type `" + z + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof p[z] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ne.name = "Invariant Violation", ne;
              }
              O = p[z](v, z, P, j, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (Z) {
              O = Z;
            }
            O && !(O instanceof Error) && (He(B), C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", P || "React class", j, z, typeof O), He(null)), O instanceof Error && !(O.message in Lr) && (Lr[O.message] = !0, He(B), C("Failed %s type: %s", j, O.message), He(null));
          }
      }
    }
    var zt = Array.isArray;
    function cr(p) {
      return zt(p);
    }
    function Ft(p) {
      {
        var v = typeof Symbol == "function" && Symbol.toStringTag, j = v && p[Symbol.toStringTag] || p.constructor.name || "Object";
        return j;
      }
    }
    function Bt(p) {
      try {
        return Mr(p), !1;
      } catch {
        return !0;
      }
    }
    function Mr(p) {
      return "" + p;
    }
    function Pr(p) {
      if (Bt(p))
        return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Ft(p)), Mr(p);
    }
    var _e = S.ReactCurrentOwner, qt = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, Dr, $r, lr;
    lr = {};
    function Ht(p) {
      if (qe.call(p, "ref")) {
        var v = Object.getOwnPropertyDescriptor(p, "ref").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return p.ref !== void 0;
    }
    function Ut(p) {
      if (qe.call(p, "key")) {
        var v = Object.getOwnPropertyDescriptor(p, "key").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return p.key !== void 0;
    }
    function Vt(p, v) {
      if (typeof p.ref == "string" && _e.current && v && _e.current.stateNode !== v) {
        var j = H(_e.current.type);
        lr[j] || (C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', H(_e.current.type), p.ref), lr[j] = !0);
      }
    }
    function Yt(p, v) {
      {
        var j = function() {
          Dr || (Dr = !0, C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        j.isReactWarning = !0, Object.defineProperty(p, "key", {
          get: j,
          configurable: !0
        });
      }
    }
    function Gt(p, v) {
      {
        var j = function() {
          $r || ($r = !0, C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        j.isReactWarning = !0, Object.defineProperty(p, "ref", {
          get: j,
          configurable: !0
        });
      }
    }
    var Wt = function(p, v, j, P, B, Y, z) {
      var O = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: r,
        // Built-in properties that belong on the element
        type: p,
        key: v,
        ref: j,
        props: z,
        // Record the component responsible for creating this element.
        _owner: Y
      };
      return O._store = {}, Object.defineProperty(O._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(O, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: P
      }), Object.defineProperty(O, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: B
      }), Object.freeze && (Object.freeze(O.props), Object.freeze(O)), O;
    };
    function Kt(p, v, j, P, B) {
      {
        var Y, z = {}, O = null, ne = null;
        j !== void 0 && (Pr(j), O = "" + j), Ut(v) && (Pr(v.key), O = "" + v.key), Ht(v) && (ne = v.ref, Vt(v, B));
        for (Y in v)
          qe.call(v, Y) && !qt.hasOwnProperty(Y) && (z[Y] = v[Y]);
        if (p && p.defaultProps) {
          var Z = p.defaultProps;
          for (Y in Z)
            z[Y] === void 0 && (z[Y] = Z[Y]);
        }
        if (O || ne) {
          var ee = typeof p == "function" ? p.displayName || p.name || "Unknown" : p;
          O && Yt(z, ee), ne && Gt(z, ee);
        }
        return Wt(p, O, ne, B, P, _e.current, z);
      }
    }
    var dr = S.ReactCurrentOwner, Or = S.ReactDebugCurrentFrame;
    function Ce(p) {
      if (p) {
        var v = p._owner, j = Be(p.type, p._source, v ? v.type : null);
        Or.setExtraStackFrame(j);
      } else
        Or.setExtraStackFrame(null);
    }
    var pr;
    pr = !1;
    function ur(p) {
      return typeof p == "object" && p !== null && p.$$typeof === r;
    }
    function Ar() {
      {
        if (dr.current) {
          var p = H(dr.current.type);
          if (p)
            return `

Check the render method of \`` + p + "`.";
        }
        return "";
      }
    }
    function Qt(p) {
      {
        if (p !== void 0) {
          var v = p.fileName.replace(/^.*[\\\/]/, ""), j = p.lineNumber;
          return `

Check your code at ` + v + ":" + j + ".";
        }
        return "";
      }
    }
    var zr = {};
    function Xt(p) {
      {
        var v = Ar();
        if (!v) {
          var j = typeof p == "string" ? p : p.displayName || p.name;
          j && (v = `

Check the top-level render call using <` + j + ">.");
        }
        return v;
      }
    }
    function Fr(p, v) {
      {
        if (!p._store || p._store.validated || p.key != null)
          return;
        p._store.validated = !0;
        var j = Xt(v);
        if (zr[j])
          return;
        zr[j] = !0;
        var P = "";
        p && p._owner && p._owner !== dr.current && (P = " It was passed a child from " + H(p._owner.type) + "."), Ce(p), C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', j, P), Ce(null);
      }
    }
    function Br(p, v) {
      {
        if (typeof p != "object")
          return;
        if (cr(p))
          for (var j = 0; j < p.length; j++) {
            var P = p[j];
            ur(P) && Fr(P, v);
          }
        else if (ur(p))
          p._store && (p._store.validated = !0);
        else if (p) {
          var B = I(p);
          if (typeof B == "function" && B !== p.entries)
            for (var Y = B.call(p), z; !(z = Y.next()).done; )
              ur(z.value) && Fr(z.value, v);
        }
      }
    }
    function Jt(p) {
      {
        var v = p.type;
        if (v == null || typeof v == "string")
          return;
        var j;
        if (typeof v == "function")
          j = v.propTypes;
        else if (typeof v == "object" && (v.$$typeof === d || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        v.$$typeof === m))
          j = v.propTypes;
        else
          return;
        if (j) {
          var P = H(v);
          At(j, p.props, "prop", P, p);
        } else if (v.PropTypes !== void 0 && !pr) {
          pr = !0;
          var B = H(v);
          C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", B || "Unknown");
        }
        typeof v.getDefaultProps == "function" && !v.getDefaultProps.isReactClassApproved && C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Zt(p) {
      {
        for (var v = Object.keys(p.props), j = 0; j < v.length; j++) {
          var P = v[j];
          if (P !== "children" && P !== "key") {
            Ce(p), C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", P), Ce(null);
            break;
          }
        }
        p.ref !== null && (Ce(p), C("Invalid attribute `ref` supplied to `React.Fragment`."), Ce(null));
      }
    }
    function qr(p, v, j, P, B, Y) {
      {
        var z = K(p);
        if (!z) {
          var O = "";
          (p === void 0 || typeof p == "object" && p !== null && Object.keys(p).length === 0) && (O += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ne = Qt(B);
          ne ? O += ne : O += Ar();
          var Z;
          p === null ? Z = "null" : cr(p) ? Z = "array" : p !== void 0 && p.$$typeof === r ? (Z = "<" + (H(p.type) || "Unknown") + " />", O = " Did you accidentally export a JSX literal instead of a component?") : Z = typeof p, C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", Z, O);
        }
        var ee = Kt(p, v, j, B, Y);
        if (ee == null)
          return ee;
        if (z) {
          var de = v.children;
          if (de !== void 0)
            if (P)
              if (cr(de)) {
                for (var Ie = 0; Ie < de.length; Ie++)
                  Br(de[Ie], p);
                Object.freeze && Object.freeze(de);
              } else
                C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              Br(de, p);
        }
        return p === n ? Zt(ee) : Jt(ee), ee;
      }
    }
    function eo(p, v, j) {
      return qr(p, v, j, !0);
    }
    function ro(p, v, j) {
      return qr(p, v, j, !1);
    }
    var to = ro, oo = eo;
    Me.Fragment = n, Me.jsx = to, Me.jsxs = oo;
  }()), Me;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = vo() : e.exports = wo();
})(yo);
const So = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, Co = (e, r, t = !1) => g(["", ""], ({
  theme: n
}) => {
  let s, a, i;
  switch (e) {
    case "primary":
      s = r ? n.colors.primary : `${n.colors.primary}20`, a = r ? n.colors.textInverse : n.colors.primary, i = n.colors.primary;
      break;
    case "secondary":
      s = r ? n.colors.secondary : `${n.colors.secondary}20`, a = r ? n.colors.textInverse : n.colors.secondary, i = n.colors.secondary;
      break;
    case "success":
      s = r ? n.colors.success : `${n.colors.success}20`, a = r ? n.colors.textInverse : n.colors.success, i = n.colors.success;
      break;
    case "warning":
      s = r ? n.colors.warning : `${n.colors.warning}20`, a = r ? n.colors.textInverse : n.colors.warning, i = n.colors.warning;
      break;
    case "error":
      s = r ? n.colors.error : `${n.colors.error}20`, a = r ? n.colors.textInverse : n.colors.error, i = n.colors.error;
      break;
    case "info":
      s = r ? n.colors.info : `${n.colors.info}20`, a = r ? n.colors.textInverse : n.colors.info, i = n.colors.info;
      break;
    case "neutral":
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}10`, a = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
      break;
    default:
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}20`, a = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
  }
  return t ? `
          background-color: transparent;
          color: ${i};
          border: 1px solid ${i};
        ` : `
        background-color: ${s};
        color: ${a};
        border: 1px solid transparent;
      `;
}), jt = /* @__PURE__ */ c.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), Io = /* @__PURE__ */ c(jt).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), jo = /* @__PURE__ */ c(jt).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), To = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: r,
  dot: t
}) => t ? "50%" : r ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => So[e], ({
  variant: e,
  solid: r,
  outlined: t
}) => Co(e, r, t || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), Qe = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  solid: n = !1,
  className: s = "",
  style: a,
  onClick: i,
  rounded: u = !1,
  dot: d = !1,
  counter: f = !1,
  outlined: l = !1,
  startIcon: m,
  endIcon: b,
  max: x,
  inline: h = !0
}) => {
  let y = e;
  return f && typeof e == "number" && x !== void 0 && e > x && (y = `${x}+`), /* @__PURE__ */ o.jsx(To, { variant: r, size: t, solid: n, clickable: !!i, className: s, style: a, onClick: i, rounded: u, dot: d, counter: f, outlined: l, inline: h, children: !d && /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    m && /* @__PURE__ */ o.jsx(Io, { children: m }),
    y,
    b && /* @__PURE__ */ o.jsx(jo, { children: b })
  ] }) });
}, Eo = /* @__PURE__ */ ke(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), No = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], Eo, ({
  theme: e
}) => e.spacing.xs), ko = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, _o = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:", "0d;transform:translateY(-1px);}&:active:not(:disabled){background-color:", "1a;transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:", "0d;}&:active:not(:disabled){background-color:", "1a;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, Lo = /* @__PURE__ */ c.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => ko[e], ({
  variant: e = "primary"
}) => _o[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: r
}) => r.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: r
}) => r.spacing.xs)), Ro = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), le = ({
  children: e,
  variant: r = "primary",
  disabled: t = !1,
  loading: n = !1,
  size: s = "medium",
  fullWidth: a = !1,
  startIcon: i,
  endIcon: u,
  onClick: d,
  className: f = "",
  type: l = "button",
  ...m
}) => /* @__PURE__ */ o.jsx(Lo, { variant: r, disabled: t || n, size: s, fullWidth: a, onClick: d, className: f, type: l, $hasStartIcon: !!i && !n, $hasEndIcon: !!u && !n, ...m, children: /* @__PURE__ */ o.jsxs(Ro, { children: [
  n && /* @__PURE__ */ o.jsx(No, {}),
  !n && i,
  e,
  !n && u
] }) }), Mo = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Po = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Do = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Gr = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), $o = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Oo = /* @__PURE__ */ c.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Ao = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), je = ({
  value: e,
  onChange: r,
  placeholder: t = "",
  disabled: n = !1,
  error: s = "",
  type: a = "text",
  name: i = "",
  id: u = "",
  className: d = "",
  required: f = !1,
  autoComplete: l = "",
  label: m = "",
  helperText: b = "",
  startIcon: x,
  endIcon: h,
  loading: y = !1,
  success: I = !1,
  clearable: S = !1,
  onClear: C,
  maxLength: R,
  showCharCount: A = !1,
  size: N = "medium",
  fullWidth: k = !1,
  ...M
}) => {
  const [E, _] = V(!1), K = St(null), W = () => {
    C ? C() : r(""), K.current && K.current.focus();
  }, L = (oe) => {
    _(!0), M.onFocus && M.onFocus(oe);
  }, H = (oe) => {
    _(!1), M.onBlur && M.onBlur(oe);
  }, re = S && e && !n, te = (e == null ? void 0 : e.length) || 0, fe = A || R !== void 0 && R > 0;
  return /* @__PURE__ */ o.jsxs(Mo, { className: d, fullWidth: k, children: [
    m && /* @__PURE__ */ o.jsxs(Po, { htmlFor: u, children: [
      m,
      f && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(Do, { hasError: !!s, hasSuccess: !!I, disabled: !!n, $size: N, hasStartIcon: !!x, hasEndIcon: !!(h || re), isFocused: !!E, children: [
      x && /* @__PURE__ */ o.jsx(Gr, { children: x }),
      /* @__PURE__ */ o.jsx(
        $o,
        {
          ref: K,
          type: a,
          value: e,
          onChange: (oe) => r(oe.target.value),
          placeholder: t,
          disabled: !!(n || y),
          name: i,
          id: u,
          required: !!f,
          autoComplete: l,
          hasStartIcon: !!x,
          hasEndIcon: !!(h || re),
          $size: N,
          maxLength: R,
          onFocus: L,
          onBlur: H,
          ...M
        }
      ),
      re && /* @__PURE__ */ o.jsx(Oo, { type: "button", onClick: W, tabIndex: -1, children: "✕" }),
      h && /* @__PURE__ */ o.jsx(Gr, { children: h })
    ] }),
    (s || b || fe) && /* @__PURE__ */ o.jsxs(Ao, { hasError: !!s, hasSuccess: !!I, children: [
      /* @__PURE__ */ o.jsx("div", { children: s || b }),
      fe && /* @__PURE__ */ o.jsxs("div", { children: [
        te,
        R !== void 0 && `/${R}`
      ] })
    ] })
  ] });
}, Wr = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, zo = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, Fo = /* @__PURE__ */ ke(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Bo = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: r,
  customWidth: t
}) => e === "custom" ? Wr.custom({
  customHeight: r,
  customWidth: t
}) : Wr[e], ({
  variant: e
}) => zo[e]), qo = /* @__PURE__ */ c.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, Fo, ({
  theme: e
}) => e.spacing.sm), Ho = /* @__PURE__ */ c.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), Uo = ({
  variant: e = "default",
  size: r = "medium",
  height: t = "200px",
  width: n = "",
  text: s = "Loading...",
  showSpinner: a = !0,
  className: i = ""
}) => /* @__PURE__ */ o.jsxs(Bo, { variant: e, size: r, customHeight: t, customWidth: n, className: i, children: [
  a && /* @__PURE__ */ o.jsx(qo, {}),
  s && /* @__PURE__ */ o.jsx(Ho, { children: s })
] }), Vo = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Yo = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), Go = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), Wo = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), Ko = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), Qo = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), Xo = /* @__PURE__ */ c.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), Pe = ({
  options: e,
  value: r,
  onChange: t,
  disabled: n = !1,
  error: s = "",
  name: a = "",
  id: i = "",
  className: u = "",
  required: d = !1,
  placeholder: f = "",
  label: l = "",
  helperText: m = "",
  size: b = "medium",
  fullWidth: x = !0,
  loading: h = !1,
  success: y = !1,
  startIcon: I,
  ...S
}) => {
  const [C, R] = V(!1), A = (_) => {
    R(!0), S.onFocus && S.onFocus(_);
  }, N = (_) => {
    R(!1), S.onBlur && S.onBlur(_);
  }, k = {}, M = [];
  e.forEach((_) => {
    _.group ? (k[_.group] || (k[_.group] = []), k[_.group].push(_)) : M.push(_);
  });
  const E = Object.keys(k).length > 0;
  return /* @__PURE__ */ o.jsxs(Vo, { className: u, fullWidth: x, children: [
    l && /* @__PURE__ */ o.jsxs(Yo, { htmlFor: i, children: [
      l,
      d && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(Go, { hasError: !!s, hasSuccess: !!y, disabled: !!(n || h), $size: b, hasStartIcon: !!I, isFocused: !!C, children: [
      I && /* @__PURE__ */ o.jsx(Wo, { children: I }),
      /* @__PURE__ */ o.jsxs(
        Ko,
        {
          value: r,
          onChange: (_) => t(_.target.value),
          disabled: !!(n || h),
          name: a,
          id: i,
          required: !!d,
          hasStartIcon: !!I,
          $size: b,
          onFocus: A,
          onBlur: N,
          ...S,
          children: [
            f && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: f }),
            E ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
              M.map((_) => /* @__PURE__ */ o.jsx("option", { value: _.value, disabled: _.disabled, children: _.label }, _.value)),
              Object.entries(k).map(([_, K]) => /* @__PURE__ */ o.jsx(Xo, { label: _, children: K.map((W) => /* @__PURE__ */ o.jsx("option", { value: W.value, disabled: W.disabled, children: W.label }, W.value)) }, _))
            ] }) : (
              // Render all options without groups
              e.map((_) => /* @__PURE__ */ o.jsx("option", { value: _.value, disabled: _.disabled, children: _.label }, _.value))
            )
          ]
        }
      )
    ] }),
    (s || m) && /* @__PURE__ */ o.jsx(Qo, { hasError: !!s, hasSuccess: !!y, children: /* @__PURE__ */ o.jsx("div", { children: s || m }) })
  ] });
}, Kr = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, Jo = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, Zo = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), en = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), rn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => Kr[e], ({
  size: e
}) => Kr[e], ({
  status: e,
  theme: r,
  pulse: t
}) => {
  let n, s;
  switch (e) {
    case "success":
      n = r.colors.success, s = "76, 175, 80";
      break;
    case "error":
      n = r.colors.error, s = "244, 67, 54";
      break;
    case "warning":
      n = r.colors.warning, s = "255, 152, 0";
      break;
    case "info":
      n = r.colors.info, s = "33, 150, 243";
      break;
    default:
      n = r.colors.textSecondary, s = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], n, t && g(["--pulse-color:", ";", ""], s, Zo));
}), tn = /* @__PURE__ */ c.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => Jo[e], ({
  status: e,
  theme: r
}) => {
  let t;
  switch (e) {
    case "success":
      t = r.colors.success;
      break;
    case "error":
      t = r.colors.error;
      break;
    case "warning":
      t = r.colors.warning;
      break;
    case "info":
      t = r.colors.info;
      break;
    default:
      t = r.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], t, r.fontWeights.medium);
}), vc = ({
  status: e,
  size: r = "medium",
  pulse: t = !1,
  showLabel: n = !1,
  label: s = "",
  className: a = ""
}) => {
  const i = s || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ o.jsxs(en, { className: a, children: [
    /* @__PURE__ */ o.jsx(rn, { status: e, size: r, pulse: t }),
    n && /* @__PURE__ */ o.jsx(tn, { status: e, size: r, children: i })
  ] });
}, on = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, nn = (e) => g(["", ""], ({
  theme: r
}) => {
  let t, n, s;
  switch (e) {
    case "primary":
      t = `${r.colors.primary}10`, n = r.colors.primary, s = `${r.colors.primary}30`;
      break;
    case "secondary":
      t = `${r.colors.secondary}10`, n = r.colors.secondary, s = `${r.colors.secondary}30`;
      break;
    case "success":
      t = `${r.colors.success}10`, n = r.colors.success, s = `${r.colors.success}30`;
      break;
    case "warning":
      t = `${r.colors.warning}10`, n = r.colors.warning, s = `${r.colors.warning}30`;
      break;
    case "error":
      t = `${r.colors.error}10`, n = r.colors.error, s = `${r.colors.error}30`;
      break;
    case "info":
      t = `${r.colors.info}10`, n = r.colors.info, s = `${r.colors.info}30`;
      break;
    default:
      t = `${r.colors.textSecondary}10`, n = r.colors.textSecondary, s = `${r.colors.textSecondary}30`;
  }
  return `
        background-color: ${t};
        color: ${n};
        border: 1px solid ${s};
      `;
}), sn = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => on[e], ({
  variant: e
}) => nn(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), an = /* @__PURE__ */ c.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${t[e]};
      height: ${t[e]};
      font-size: ${r.fontSizes.xs};
    `;
}), wc = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  removable: n = !1,
  onRemove: s,
  className: a = "",
  onClick: i
}) => {
  const u = (d) => {
    d.stopPropagation(), s == null || s();
  };
  return /* @__PURE__ */ o.jsxs(sn, { variant: r, size: t, clickable: !!i, className: a, onClick: i, children: [
    e,
    n && /* @__PURE__ */ o.jsx(an, { size: t, onClick: u, children: "×" })
  ] });
}, cn = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimePickerContainer",
  componentId: "sc-v5w9zw-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), ln = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-v5w9zw-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), dn = /* @__PURE__ */ c.input.withConfig({
  displayName: "TimeInput",
  componentId: "sc-v5w9zw-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), pn = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  label: s,
  required: a = !1,
  disabled: i = !1,
  className: u,
  placeholder: d = "HH:MM",
  min: f,
  max: l
}) => /* @__PURE__ */ o.jsxs(cn, { className: u, children: [
  s && /* @__PURE__ */ o.jsxs(ln, { htmlFor: e, children: [
    s,
    a && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsx(dn, { id: e, name: r, type: "time", value: t, onChange: n, required: a, disabled: i, placeholder: d, min: f, max: l })
] }), Sc = pn, vr = () => Intl.DateTimeFormat().resolvedOptions().timeZone, xr = (e, r = /* @__PURE__ */ new Date()) => {
  const s = new Intl.DateTimeFormat("en", {
    timeZone: e,
    timeZoneName: "short"
  }).formatToParts(r).find((a) => a.type === "timeZoneName");
  return (s == null ? void 0 : s.value) || e;
}, Qr = (e, r) => {
  const t = r || vr(), [n, s] = e.split(":").map(Number), a = /* @__PURE__ */ new Date(), i = a.getFullYear(), u = String(a.getMonth() + 1).padStart(2, "0"), d = String(a.getDate()).padStart(2, "0"), f = `${String(n).padStart(2, "0")}:${String(s).padStart(2, "0")}:00`, l = `${i}-${u}-${d}T${f}`, m = new Date(l), b = /* @__PURE__ */ new Date(), x = new Date(b.toLocaleString("en-US", {
    timeZone: "America/New_York"
  })), y = new Date(b.toLocaleString("en-US", {
    timeZone: t
  })).getTime() - x.getTime();
  return new Date(m.getTime() + y).toLocaleTimeString("en-GB", {
    timeZone: t,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, Cc = (e, r) => {
  const t = /* @__PURE__ */ new Date();
  return (/* @__PURE__ */ new Date(`${t.toDateString()} ${e}:00`)).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
}, br = (e) => {
  const r = e || vr(), t = /* @__PURE__ */ new Date(), n = t.toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), s = t.toLocaleTimeString("en-GB", {
    timeZone: r,
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), a = xr("America/New_York", t), i = xr(r, t);
  return {
    nyTime: n,
    localTime: s,
    nyTimezone: a,
    localTimezone: i,
    formatted: `${n} ${a} | ${s} ${i}`
  };
}, Ic = () => {
  const r = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  });
  return Ge(r);
}, jc = () => {
  const e = /* @__PURE__ */ new Date();
  return new Date(e.toLocaleString("en-US", {
    timeZone: "America/New_York"
  }));
}, un = (e) => {
  const r = Math.floor(e / 60), t = e % 60;
  let n = "";
  return r > 0 ? n = t > 0 ? `${r}h ${t}m` : `${r}h` : n = `${t}m`, {
    totalMinutes: e,
    hours: r,
    minutes: t,
    formatted: n
  };
}, We = (e) => {
  const t = (/* @__PURE__ */ new Date()).toLocaleString("en-US", {
    timeZone: "America/New_York"
  }), n = new Date(t), [s, a] = e.split(":").map(Number), i = new Date(n);
  i.setHours(s, a, 0, 0), i <= n && i.setDate(i.getDate() + 1);
  const u = i.getTime() - n.getTime(), d = Math.floor(u / (1e3 * 60));
  return un(d);
}, fn = (e) => We(e), wr = (e, r, t) => {
  const n = t || vr(), s = Qr(e, n), a = Qr(r, n), i = xr(n);
  return {
    nyStart: e,
    nyEnd: r,
    localStart: s,
    localEnd: a,
    formatted: `${e}-${r} NY | ${s}-${a} ${i}`
  };
}, gn = (e, r) => {
  const n = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    timeZone: "America/New_York",
    hour: "2-digit",
    minute: "2-digit",
    hour12: !1
  }), s = Ge(n), a = Ge(e), i = Ge(r);
  return s >= a && s <= i;
}, Ge = (e) => {
  const [r, t] = e.split(":").map(Number);
  return r * 60 + t;
}, Tc = (e) => {
  const r = Math.floor(e / 60), t = e % 60;
  return `${r.toString().padStart(2, "0")}:${t.toString().padStart(2, "0")}`;
}, mn = (e) => {
  const r = e.localTimezone.includes("GMT") ? "🇮🇪" : "🌍";
  return `${e.localTime} ${r} | ${e.nyTime} 🇺🇸`;
}, Ec = (e) => `${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`, Nc = (e, r, t, n) => {
  const s = gn(r, t), a = wr(r, t, n);
  if (s)
    return {
      isActive: !0,
      timeRemaining: fn(t),
      sessionTime: a,
      status: "active"
    };
  const i = We(r);
  return {
    isActive: !1,
    timeUntilStart: i,
    sessionTime: a,
    status: i.totalMinutes < 24 * 60 ? "upcoming" : "ended"
  };
}, ye = /* @__PURE__ */ c.div.withConfig({
  displayName: "TimeContainer",
  componentId: "sc-10dqpqu-0"
})(["display:flex;align-items:center;gap:", ";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"], ({
  format: e
}) => e === "mobile" ? "4px" : "8px"), Te = /* @__PURE__ */ c.span.withConfig({
  displayName: "NYTime",
  componentId: "sc-10dqpqu-1"
})(["color:#3b82f6;font-size:inherit;"]), Ee = /* @__PURE__ */ c.span.withConfig({
  displayName: "LocalTime",
  componentId: "sc-10dqpqu-2"
})(["color:#10b981;font-size:inherit;"]), Ne = /* @__PURE__ */ c.span.withConfig({
  displayName: "Separator",
  componentId: "sc-10dqpqu-3"
})(["color:#6b7280;font-size:inherit;"]), Ke = /* @__PURE__ */ c.span.withConfig({
  displayName: "Timezone",
  componentId: "sc-10dqpqu-4"
})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]), fr = /* @__PURE__ */ c.span.withConfig({
  displayName: "LiveIndicator",
  componentId: "sc-10dqpqu-5"
})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]), Xr = /* @__PURE__ */ c.div.withConfig({
  displayName: "CountdownContainer",
  componentId: "sc-10dqpqu-6"
})(["display:flex;align-items:center;gap:8px;"]), Jr = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownValue",
  componentId: "sc-10dqpqu-7"
})(["color:#f59e0b;font-weight:bold;"]), gr = /* @__PURE__ */ c.span.withConfig({
  displayName: "CountdownLabel",
  componentId: "sc-10dqpqu-8"
})(["color:#9ca3af;font-size:0.9em;"]), hn = ({
  format: e,
  showLive: r,
  updateInterval: t
}) => {
  const [n, s] = V(br());
  return ae(() => {
    const a = setInterval(() => {
      s(br());
    }, t * 1e3);
    return () => clearInterval(a);
  }, [t]), e === "mobile" ? /* @__PURE__ */ o.jsxs(ye, { format: e, children: [
    /* @__PURE__ */ o.jsx("span", { children: mn(n) }),
    r && /* @__PURE__ */ o.jsx(fr, { children: "LIVE" })
  ] }) : e === "compact" ? /* @__PURE__ */ o.jsxs(ye, { format: e, children: [
    /* @__PURE__ */ o.jsx(Te, { children: n.nyTime }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsx(Ee, { children: n.localTime }),
    r && /* @__PURE__ */ o.jsx(fr, { children: "LIVE" })
  ] }) : /* @__PURE__ */ o.jsxs(ye, { format: e, children: [
    /* @__PURE__ */ o.jsx(Te, { children: n.nyTime }),
    /* @__PURE__ */ o.jsx(Ke, { children: n.nyTimezone }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsx(Ee, { children: n.localTime }),
    /* @__PURE__ */ o.jsx(Ke, { children: n.localTimezone }),
    r && /* @__PURE__ */ o.jsx(fr, { children: "LIVE" })
  ] });
}, xn = ({
  nyTime: e,
  format: r
}) => {
  const t = br(), n = wr(e, e);
  return r === "mobile" ? /* @__PURE__ */ o.jsx(ye, { format: r, children: /* @__PURE__ */ o.jsxs("span", { children: [
    n.localStart,
    " 🇮🇪 | ",
    e,
    " 🇺🇸"
  ] }) }) : r === "compact" ? /* @__PURE__ */ o.jsxs(ye, { format: r, children: [
    /* @__PURE__ */ o.jsx(Te, { children: e }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsx(Ee, { children: n.localStart })
  ] }) : /* @__PURE__ */ o.jsxs(ye, { format: r, children: [
    /* @__PURE__ */ o.jsx(Te, { children: e }),
    /* @__PURE__ */ o.jsx(Ke, { children: t.nyTimezone }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsx(Ee, { children: n.localStart }),
    /* @__PURE__ */ o.jsx(Ke, { children: t.localTimezone })
  ] });
}, bn = ({
  targetNYTime: e,
  format: r,
  updateInterval: t
}) => {
  const [n, s] = V(We(e));
  return ae(() => {
    const a = setInterval(() => {
      s(We(e));
    }, t * 1e3);
    return () => clearInterval(a);
  }, [e, t]), r === "mobile" ? /* @__PURE__ */ o.jsxs(Xr, { children: [
    /* @__PURE__ */ o.jsx(Jr, { children: n.formatted }),
    /* @__PURE__ */ o.jsxs(gr, { children: [
      "until ",
      e
    ] })
  ] }) : /* @__PURE__ */ o.jsxs(Xr, { children: [
    /* @__PURE__ */ o.jsx(gr, { children: "Next in:" }),
    /* @__PURE__ */ o.jsx(Jr, { children: n.formatted }),
    /* @__PURE__ */ o.jsxs(gr, { children: [
      "(",
      e,
      " NY)"
    ] })
  ] });
}, yn = ({
  sessionStart: e,
  sessionEnd: r,
  format: t
}) => {
  const n = wr(e, r);
  return t === "mobile" ? /* @__PURE__ */ o.jsx(ye, { format: t, children: /* @__PURE__ */ o.jsx("span", { children: n.formatted }) }) : t === "compact" ? /* @__PURE__ */ o.jsxs(ye, { format: t, children: [
    /* @__PURE__ */ o.jsxs(Te, { children: [
      e,
      "-",
      r
    ] }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsxs(Ee, { children: [
      n.localStart,
      "-",
      n.localEnd
    ] })
  ] }) : /* @__PURE__ */ o.jsxs(ye, { format: t, children: [
    /* @__PURE__ */ o.jsx("div", { children: /* @__PURE__ */ o.jsxs(Te, { children: [
      e,
      "-",
      r,
      " NY"
    ] }) }),
    /* @__PURE__ */ o.jsx(Ne, { children: "|" }),
    /* @__PURE__ */ o.jsx("div", { children: /* @__PURE__ */ o.jsxs(Ee, { children: [
      n.localStart,
      "-",
      n.localEnd,
      " Local"
    ] }) })
  ] });
}, kc = (e) => {
  const {
    mode: r = "current",
    nyTime: t,
    targetNYTime: n,
    sessionStart: s,
    sessionEnd: a,
    format: i = "desktop",
    showLive: u = !1,
    className: d,
    updateInterval: f = 1
  } = e, l = {
    className: d,
    style: {
      fontSize: i === "mobile" ? "14px" : i === "compact" ? "13px" : "14px"
    }
  };
  switch (r) {
    case "static":
      return t ? /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(xn, { nyTime: t, format: i }) }) : (console.warn("DualTimeDisplay: nyTime is required for static mode"), null);
    case "countdown":
      return n ? /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(bn, { targetNYTime: n, format: i, updateInterval: f }) }) : (console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"), null);
    case "session":
      return !s || !a ? (console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"), null) : /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(yn, { sessionStart: s, sessionEnd: a, format: i }) });
    case "current":
    default:
      return /* @__PURE__ */ o.jsx("div", { ...l, children: /* @__PURE__ */ o.jsx(hn, { format: i, showLive: u, updateInterval: f }) });
  }
}, vn = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-w0dp8e-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), wn = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-w0dp8e-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), Sn = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-w0dp8e-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), Cn = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  options: s,
  label: a,
  required: i = !1,
  disabled: u = !1,
  className: d,
  placeholder: f
}) => /* @__PURE__ */ o.jsxs(vn, { className: d, children: [
  a && /* @__PURE__ */ o.jsxs(wn, { htmlFor: e, children: [
    a,
    i && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsxs(Sn, { id: e, name: r, value: t, onChange: n, required: i, disabled: u, children: [
    f && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: f }),
    s.map((l) => /* @__PURE__ */ o.jsx("option", { value: l.value, children: l.label }, l.value))
  ] })
] }), _c = Cn, In = /* @__PURE__ */ c.span.withConfig({
  displayName: "StyledLoadingCell",
  componentId: "sc-1i0qdjp-0"
})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;", " border-radius:", ";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"], ({
  $size: e,
  theme: r
}) => {
  var t, n, s, a, i, u, d, f, l;
  switch (e) {
    case "small":
      return g(["font-size:", ";padding:", " ", ";"], ((t = r.fontSizes) == null ? void 0 : t.xs) || "12px", ((n = r.spacing) == null ? void 0 : n.xxs) || "2px", ((s = r.spacing) == null ? void 0 : s.xs) || "4px");
    case "large":
      return g(["font-size:", ";padding:", " ", ";"], ((a = r.fontSizes) == null ? void 0 : a.lg) || "18px", ((i = r.spacing) == null ? void 0 : i.sm) || "8px", ((u = r.spacing) == null ? void 0 : u.md) || "12px");
    default:
      return g(["font-size:", ";padding:", " ", ";"], ((d = r.fontSizes) == null ? void 0 : d.sm) || "14px", ((f = r.spacing) == null ? void 0 : f.xs) || "4px", ((l = r.spacing) == null ? void 0 : l.sm) || "8px");
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}), jn = /* @__PURE__ */ c.span.withConfig({
  displayName: "LoadingPlaceholder",
  componentId: "sc-1i0qdjp-1"
})(["display:inline-block;width:", ";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"], ({
  $width: e
}) => e || "60px"), Tn = (e) => {
  const {
    size: r = "medium",
    width: t,
    className: n,
    "aria-label": s
  } = e;
  return /* @__PURE__ */ o.jsx(In, { className: n, $size: r, $width: t, "aria-label": s || "Loading data", role: "cell", "aria-busy": "true", children: /* @__PURE__ */ o.jsx(jn, { $width: t }) });
}, Lc = Tn, En = /* @__PURE__ */ ke(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Nn = /* @__PURE__ */ ke(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]), kn = /* @__PURE__ */ c.div.withConfig({
  displayName: "StyledSpinner",
  componentId: "sc-1hoaoss-0"
})(["display:inline-block;position:relative;", " &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;", " animation:", " ", "s linear infinite;}", ""], ({
  $size: e
}) => {
  switch (e) {
    case "xs":
      return g(["width:16px;height:16px;"]);
    case "sm":
      return g(["width:20px;height:20px;"]);
    case "md":
      return g(["width:32px;height:32px;"]);
    case "lg":
      return g(["width:48px;height:48px;"]);
    case "xl":
      return g(["width:64px;height:64px;"]);
    default:
      return g(["width:32px;height:32px;"]);
  }
}, ({
  $variant: e,
  theme: r
}) => {
  var t, n, s, a, i, u;
  switch (e) {
    case "primary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primary) || "#dc2626");
    case "secondary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((s = r.colors) == null ? void 0 : s.textSecondary) || "#9ca3af", ((a = r.colors) == null ? void 0 : a.textSecondary) || "#9ca3af");
    case "white":
      return g(["border-top-color:#ffffff;border-right-color:#ffffff;"]);
    case "red":
      return g(["border-top-color:#dc2626;border-right-color:#dc2626;"]);
    default:
      return g(["border-top-color:", ";border-right-color:", ";"], ((i = r.colors) == null ? void 0 : i.primary) || "#dc2626", ((u = r.colors) == null ? void 0 : u.primary) || "#dc2626");
  }
}, En, ({
  $speed: e
}) => 1 / e, ({
  $showStripes: e,
  $variant: r
}) => e && g(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:", ";background-size:8px 8px;animation:", " ", "s linear infinite;}"], r === "red" || r === "primary" ? "linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)" : "linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)", Nn, (t) => 2 / t.$speed)), _n = /* @__PURE__ */ c.div.withConfig({
  displayName: "SpinnerContainer",
  componentId: "sc-1hoaoss-1"
})(["display:inline-flex;align-items:center;justify-content:center;"]), Ln = (e) => {
  const {
    size: r = "md",
    variant: t = "primary",
    className: n,
    "aria-label": s,
    speed: a = 1,
    showStripes: i = !1
  } = e;
  return /* @__PURE__ */ o.jsx(_n, { className: n, children: /* @__PURE__ */ o.jsx(kn, { $size: r, $variant: t, $speed: a, $showStripes: i, role: "status", "aria-label": s || "Loading", "aria-live": "polite" }) });
}, Rc = Ln, Rn = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, Mn = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, Pn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";"], r.colors.border), ({
  padding: e
}) => Rn[e], ({
  variant: e
}) => Mn[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: r
}) => r.shadows.md)), Dn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), $n = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), On = /* @__PURE__ */ c.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), An = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), zn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Fn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), Bn = /* @__PURE__ */ c.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), qn = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), Hn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), Un = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), Vn = ({
  children: e,
  title: r = "",
  subtitle: t = "",
  bordered: n = !0,
  variant: s = "default",
  padding: a = "medium",
  className: i = "",
  footer: u,
  actions: d,
  isLoading: f = !1,
  hasError: l = !1,
  errorMessage: m = "An error occurred",
  clickable: b = !1,
  onClick: x,
  ...h
}) => {
  const y = r || t || d;
  return /* @__PURE__ */ o.jsxs(Pn, { bordered: n, variant: s, padding: a, clickable: b, className: i, onClick: b ? x : void 0, ...h, children: [
    f && /* @__PURE__ */ o.jsx(qn, { children: /* @__PURE__ */ o.jsx(Un, {}) }),
    y && /* @__PURE__ */ o.jsxs(Dn, { children: [
      /* @__PURE__ */ o.jsxs($n, { children: [
        r && /* @__PURE__ */ o.jsx(On, { children: r }),
        t && /* @__PURE__ */ o.jsx(An, { children: t })
      ] }),
      d && /* @__PURE__ */ o.jsx(zn, { children: d })
    ] }),
    l && /* @__PURE__ */ o.jsx(Hn, { children: /* @__PURE__ */ o.jsx("p", { children: m }) }),
    /* @__PURE__ */ o.jsx(Fn, { children: e }),
    u && /* @__PURE__ */ o.jsx(Bn, { children: u })
  ] });
}, Yn = /* @__PURE__ */ c.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.md,
    medium: r.fontSizes.lg,
    large: r.fontSizes.xl
  };
  return g(["font-size:", ";"], t[e]);
}), Gn = /* @__PURE__ */ c.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.sm,
    medium: r.fontSizes.md,
    large: r.fontSizes.lg
  };
  return g(["font-size:", ";"], t[e]);
}), Wn = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, Kn = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => Wn[e], ({
  size: e,
  theme: r
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], r.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], r.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], r.spacing.lg);
  }
}), Qn = /* @__PURE__ */ c.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], t[e], t[e], t[e], r.colors.textSecondary);
}), Xn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), Jn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), Zr = ({
  title: e = "",
  description: r = "",
  icon: t,
  actionText: n = "",
  onAction: s,
  variant: a = "default",
  size: i = "medium",
  className: u = "",
  children: d
}) => /* @__PURE__ */ o.jsxs(Kn, { variant: a, size: i, className: u, children: [
  t && /* @__PURE__ */ o.jsx(Qn, { size: i, children: t }),
  e && /* @__PURE__ */ o.jsx(Yn, { size: i, children: e }),
  r && /* @__PURE__ */ o.jsx(Gn, { size: i, children: r }),
  n && s && /* @__PURE__ */ o.jsx(Xn, { children: /* @__PURE__ */ o.jsx(le, { variant: "primary", size: i === "small" ? "small" : "medium", onClick: s, children: n }) }),
  d && /* @__PURE__ */ o.jsx(Jn, { children: d })
] }), et = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), Zn = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), rt = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Ue = /* @__PURE__ */ c.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), tt = /* @__PURE__ */ c.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), ot = /* @__PURE__ */ c.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), es = /* @__PURE__ */ c.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), Tt = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), rs = /* @__PURE__ */ c.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), ts = /* @__PURE__ */ c(Tt).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), os = ({
  error: e,
  resetError: r,
  isAppLevel: t,
  name: n,
  onSkip: s
}) => {
  const a = () => {
    window.location.reload();
  };
  return t ? /* @__PURE__ */ o.jsx(et, { isAppLevel: !0, children: /* @__PURE__ */ o.jsxs(Zn, { children: [
    /* @__PURE__ */ o.jsx(rt, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Ue, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ o.jsxs(tt, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Ue, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(ot, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsx(ts, { onClick: a, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ o.jsxs(et, { children: [
    /* @__PURE__ */ o.jsx(rt, { children: n ? `Error in ${n}` : "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Ue, { children: n ? `We encountered a problem while loading ${n}. You can try again${s ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ o.jsxs(tt, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Ue, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(ot, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsxs(es, { children: [
      /* @__PURE__ */ o.jsx(Tt, { onClick: r, children: "Try Again" }),
      s && /* @__PURE__ */ o.jsx(rs, { onClick: s, children: "Skip This Feature" })
    ] })
  ] });
};
class ns extends io {
  constructor(t) {
    super(t);
    we(this, "resetError", () => {
      this.setState({
        hasError: !1,
        error: null
      });
    });
    this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(t) {
    return {
      hasError: !0,
      error: t
    };
  }
  componentDidCatch(t, n) {
    const {
      name: s
    } = this.props, a = s ? `ErrorBoundary(${s})` : "ErrorBoundary";
    console.error(`Error caught by ${a}:`, t, n), this.props.onError && this.props.onError(t, n);
  }
  componentDidUpdate(t) {
    this.state.hasError && this.props.resetOnPropsChange && t.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: t,
      error: n
    } = this.state, {
      children: s,
      fallback: a,
      name: i,
      isFeatureBoundary: u,
      onSkip: d
    } = this.props;
    return t && n ? typeof a == "function" ? a({
      error: n,
      resetError: this.resetError
    }) : a || /* @__PURE__ */ o.jsx(os, { error: n, resetError: this.resetError, isAppLevel: !u, name: i, onSkip: d }) : s;
  }
}
const Et = ({
  isAppLevel: e = !1,
  isFeatureBoundary: r = !1,
  children: t,
  ...n
}) => {
  const s = e ? "app" : r ? "feature" : "component", a = {
    resetOnPropsChange: s !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: s !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: s === "feature"
  };
  return /* @__PURE__ */ o.jsx(ns, { ...a, ...n, children: t });
}, Mc = (e) => /* @__PURE__ */ o.jsx(Et, { isAppLevel: !0, ...e }), Pc = ({
  featureName: e,
  children: r,
  ...t
}) => /* @__PURE__ */ o.jsx(Et, { isFeatureBoundary: !0, name: e, children: r, ...t }), ss = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContainer",
  componentId: "sc-lgz9vh-0"
})(["display:flex;flex-direction:column;width:100%;"]), is = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabList",
  componentId: "sc-lgz9vh-1"
})(["display:flex;border-bottom:1px solid ", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md), as = /* @__PURE__ */ c.button.withConfig({
  displayName: "TabButton",
  componentId: "sc-lgz9vh-2"
})(["padding:", " ", ";background:none;border:none;border-bottom:2px solid ", ";color:", ";font-weight:", ";cursor:pointer;transition:all ", ";&:hover{color:", ";}&:focus{outline:none;color:", ";}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md, ({
  active: e,
  theme: r
}) => e ? r.colors.primary : "transparent", ({
  active: e,
  theme: r
}) => e ? r.colors.primary : r.colors.textSecondary, ({
  active: e,
  theme: r
}) => e ? r.fontWeights.semibold : r.fontWeights.regular, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary), cs = /* @__PURE__ */ c.div.withConfig({
  displayName: "TabContent",
  componentId: "sc-lgz9vh-3"
})(["padding:", " 0;"], ({
  theme: e
}) => e.spacing.sm), ls = ({
  tabs: e,
  defaultTab: r,
  className: t,
  activeTab: n,
  onTabClick: s
}) => {
  var f;
  const [a, i] = V(r || e[0].id), u = n !== void 0 ? n : a, d = (l, m) => {
    l.preventDefault(), l.stopPropagation(), s ? s(m) : i(m);
  };
  return /* @__PURE__ */ o.jsxs(ss, { className: t, children: [
    /* @__PURE__ */ o.jsx(is, { children: e.map((l) => /* @__PURE__ */ o.jsx(
      as,
      {
        active: u === l.id,
        onClick: (m) => d(m, l.id),
        type: "button",
        form: "",
        tabIndex: 0,
        "data-tab-id": l.id,
        children: l.label
      },
      l.id
    )) }),
    /* @__PURE__ */ o.jsx(cs, { children: (f = e.find((l) => l.id === u)) == null ? void 0 : f.content })
  ] });
}, Dc = ls, ds = {
  required: (e = "This field is required") => ({
    validate: (r) => typeof r == "string" ? r.trim().length > 0 : typeof r == "number" ? !isNaN(r) : Array.isArray(r) ? r.length > 0 : r != null && r !== void 0,
    message: e
  }),
  email: (e = "Please enter a valid email address") => ({
    validate: (r) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r),
    message: e
  }),
  minLength: (e, r) => ({
    validate: (t) => t.length >= e,
    message: r || `Must be at least ${e} characters`
  }),
  maxLength: (e, r) => ({
    validate: (t) => t.length <= e,
    message: r || `Must be no more than ${e} characters`
  }),
  min: (e, r) => ({
    validate: (t) => t >= e,
    message: r || `Must be at least ${e}`
  }),
  max: (e, r) => ({
    validate: (t) => t <= e,
    message: r || `Must be no more than ${e}`
  }),
  pattern: (e, r) => ({
    validate: (t) => e.test(t),
    message: r
  })
}, ps = (e = {}) => {
  const {
    initialValue: r = "",
    required: t = !1,
    type: n = "text",
    validationRules: s = [],
    validateOnChange: a = !1,
    validateOnBlur: i = !0,
    transform: u
  } = e, d = U(() => {
    const E = [...s];
    return t && !s.some((_) => _.message.toLowerCase().includes("required")) && E.unshift(ds.required()), E;
  }, [t, s]), [f, l] = V(r), [m, b] = V(null), [x, h] = V(!1), [y, I] = V(!1), S = U(() => f !== r, [f, r]), C = U(() => m === null && !y, [m, y]), R = U(() => m === null && !y, [m, y]), A = F(async () => {
    I(!0);
    try {
      for (const E of d)
        if (!E.validate(f))
          return b(E.message), I(!1), !1;
      return b(null), I(!1), !0;
    } catch {
      return b("Validation error occurred"), I(!1), !1;
    }
  }, [f, d]), N = F(() => {
    l(r), b(null), h(!1), I(!1);
  }, [r]), k = F((E) => {
    let _;
    n === "number" ? _ = parseFloat(E.target.value) || 0 : _ = E.target.value, u && (_ = u(_)), l(_), a && x && setTimeout(() => A(), 0);
  }, [n, u, a, x, A]), M = F((E) => {
    h(!0), i && A();
  }, [i, A]);
  return {
    // State
    value: f,
    error: m,
    touched: x,
    dirty: S,
    valid: C,
    isValid: R,
    validating: y,
    // Actions
    setValue: l,
    setError: b,
    setTouched: h,
    validate: A,
    reset: N,
    handleChange: k,
    handleBlur: M
  };
}, us = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-oh07s1-0"
})(["display:flex;flex-direction:column;gap:", ";width:100%;margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), fs = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-oh07s1-1"
})(["font-size:", ";font-weight:", ";color:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || "500";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $required: e
}) => e && g(["&::after{content:' *';color:", ";}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.error) || "#dc2626";
})), Sr = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background-color:", ";color:", ";font-size:", ";padding:", ";transition:", ";&:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px ", ";}&:disabled{background-color:", ";color:", ";cursor:not-allowed;}&::placeholder{color:", ";}"], ({
  theme: e,
  $hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.lg) || "18px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.md) || "16px";
  }
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return (r = e.colors) != null && r.primary ? `${e.colors.primary}20` : "rgba(220, 38, 38, 0.2)";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.chartGrid) || "#374151";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), gs = /* @__PURE__ */ c.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-oh07s1-2"
})(["", ""], Sr), ms = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "StyledTextarea",
  componentId: "sc-oh07s1-3"
})(["", " resize:vertical;min-height:80px;"], Sr), hs = /* @__PURE__ */ c.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-oh07s1-4"
})(["", " cursor:pointer;"], Sr), xs = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-oh07s1-5"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#dc2626";
}), bs = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-oh07s1-6"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), ys = (e) => {
  const {
    name: r,
    label: t,
    placeholder: n,
    disabled: s = !1,
    className: a,
    size: i = "md",
    helpText: u,
    inputType: d = "input",
    options: f = [],
    rows: l = 4,
    onChange: m,
    onBlur: b,
    ...x
  } = e, h = ps({
    ...x,
    validateOnBlur: !0
  });
  Se.useEffect(() => {
    m && m(h.value);
  }, [h.value, m]);
  const y = (C) => {
    h.handleBlur(C), b && b();
  }, I = {
    id: r,
    name: r,
    value: h.value,
    onChange: h.handleChange,
    onBlur: y,
    disabled: s,
    placeholder: n,
    $hasError: !!h.error,
    $disabled: s,
    $size: i
  }, S = () => {
    switch (d) {
      case "textarea":
        return /* @__PURE__ */ o.jsx(ms, { ...I, rows: l });
      case "select":
        return /* @__PURE__ */ o.jsxs(hs, { ...I, children: [
          n && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: n }),
          f.map((C) => /* @__PURE__ */ o.jsx("option", { value: C.value, children: C.label }, C.value))
        ] });
      default:
        return /* @__PURE__ */ o.jsx(gs, { ...I, type: x.type || "text" });
    }
  };
  return /* @__PURE__ */ o.jsxs(us, { className: a, children: [
    t && /* @__PURE__ */ o.jsx(fs, { htmlFor: r, $required: !!x.required, children: t }),
    S(),
    h.error && h.touched && /* @__PURE__ */ o.jsx(xs, { role: "alert", children: h.error }),
    u && !h.error && /* @__PURE__ */ o.jsx(bs, { children: u })
  ] });
}, $c = ys, Oc = {
  string: (e) => (r, t) => {
    const n = String(r[e] || ""), s = String(t[e] || "");
    return n.localeCompare(s);
  },
  number: (e) => (r, t) => {
    const n = Number(r[e]) || 0, s = Number(t[e]) || 0;
    return n - s;
  },
  date: (e) => (r, t) => {
    const n = new Date(r[e]).getTime(), s = new Date(t[e]).getTime();
    return n - s;
  },
  boolean: (e) => (r, t) => {
    const n = !!r[e], s = !!t[e];
    return Number(n) - Number(s);
  }
}, vs = ({
  data: e,
  columns: r,
  defaultSort: t
}) => {
  const [n, s] = V(t ? {
    field: t.field,
    direction: t.direction
  } : null), a = F((l) => {
    const m = r.find((b) => b.field === l);
    m != null && m.sortable && s((b) => {
      var x;
      if ((b == null ? void 0 : b.field) === l)
        return {
          field: l,
          direction: b.direction === "asc" ? "desc" : "asc"
        };
      {
        const h = typeof ((x = e[0]) == null ? void 0 : x[l]) == "number" ? "desc" : "asc";
        return {
          field: l,
          direction: h
        };
      }
    });
  }, [r, e]), i = U(() => {
    if (!n)
      return e;
    const l = r.find((b) => b.field === n.field);
    return l ? [...e].sort((b, x) => {
      let h = 0;
      if (l.sortFn)
        h = l.sortFn(b, x);
      else {
        const y = b[n.field], I = x[n.field];
        typeof y == "string" && typeof I == "string" ? h = y.localeCompare(I) : typeof y == "number" && typeof I == "number" ? h = y - I : h = String(y).localeCompare(String(I));
      }
      return n.direction === "asc" ? h : -h;
    }) : e;
  }, [e, n, r]), u = F((l) => !n || n.field !== l ? null : n.direction === "asc" ? "↑" : "↓", [n]), d = F((l) => (n == null ? void 0 : n.field) === l, [n]), f = F((l) => (n == null ? void 0 : n.field) === l ? n.direction : null, [n]);
  return {
    sortedData: i,
    sortConfig: n,
    handleSort: a,
    getSortIcon: u,
    isSorted: d,
    getSortDirection: f
  };
}, nt = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-13j9udn-0"
})(["overflow-x:auto;border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), ws = /* @__PURE__ */ c.table.withConfig({
  displayName: "Table",
  componentId: "sc-13j9udn-1"
})(["width:100%;border-collapse:collapse;font-size:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.md) || "16px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.sm) || "14px";
  }
}), Ss = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHead",
  componentId: "sc-13j9udn-2"
})(["background-color:", ";border-bottom:2px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Cs = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13j9udn-3"
})([""]), st = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-13j9udn-4"
})(["", " ", " ", " border-bottom:1px solid ", ";"], ({
  $striped: e,
  theme: r
}) => {
  var t;
  return e && g(["&:nth-child(even){background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.background) || "#0f0f0f");
}, ({
  $hoverable: e,
  theme: r
}) => {
  var t;
  return e && g(["&:hover{background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.surface) || "#1f2937");
}, ({
  $clickable: e
}) => e && g(["cursor:pointer;"]), ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Is = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13j9udn-5"
})(["text-align:left;font-weight:", ";color:", ";cursor:", ";user-select:none;transition:", ";padding:", ";&:hover{", "}&:focus{outline:2px solid ", ";outline-offset:-2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e,
  $active: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textPrimary) || "#ffffff";
}, ({
  $sortable: e
}) => e ? "pointer" : "default", ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  $sortable: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), js = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-13j9udn-6"
})(["padding:", ";color:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s, a, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((a = e.spacing) == null ? void 0 : a.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), Ts = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13j9udn-7"
})(["display:inline-block;margin-left:", ";font-size:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  $direction: e
}) => e === "asc" ? "↑" : "↓"), Es = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13j9udn-8"
})(["padding:", ";text-align:center;color:", ";font-style:italic;"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
    case "lg":
      return ((n = e.spacing) == null ? void 0 : n.xl) || "24px";
    default:
      return ((s = e.spacing) == null ? void 0 : s.lg) || "16px";
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Ns = ({
  data: e,
  columns: r,
  className: t,
  emptyMessage: n = "No data available",
  defaultSort: s,
  renderCell: a,
  onRowClick: i,
  size: u = "md",
  striped: d = !0,
  hoverable: f = !0
}) => {
  const {
    sortedData: l,
    handleSort: m,
    getSortIcon: b,
    isSorted: x
  } = vs({
    data: e,
    columns: r,
    defaultSort: s
  });
  return e.length === 0 ? /* @__PURE__ */ o.jsx(nt, { className: t, children: /* @__PURE__ */ o.jsx(Es, { $size: u, children: n }) }) : /* @__PURE__ */ o.jsx(nt, { className: t, children: /* @__PURE__ */ o.jsxs(ws, { $size: u, $striped: d, $hoverable: f, children: [
    /* @__PURE__ */ o.jsx(Ss, { children: /* @__PURE__ */ o.jsx(st, { $striped: !1, $hoverable: !1, $clickable: !1, children: r.map((h) => /* @__PURE__ */ o.jsxs(Is, { $sortable: h.sortable || !1, $active: x(h.field), $size: u, onClick: () => h.sortable && m(h.field), tabIndex: h.sortable ? 0 : -1, onKeyDown: (y) => {
      h.sortable && (y.key === "Enter" || y.key === " ") && (y.preventDefault(), m(h.field));
    }, role: h.sortable ? "button" : void 0, "aria-sort": x(h.field) ? b(h.field) === "↑" ? "ascending" : "descending" : void 0, children: [
      h.label,
      x(h.field) && /* @__PURE__ */ o.jsx(Ts, { $direction: b(h.field) === "↑" ? "asc" : "desc" })
    ] }, String(h.field))) }) }),
    /* @__PURE__ */ o.jsx(Cs, { children: l.map((h, y) => /* @__PURE__ */ o.jsx(st, { $striped: d, $hoverable: f, $clickable: !!i, onClick: () => i == null ? void 0 : i(h, y), tabIndex: i ? 0 : -1, onKeyDown: (I) => {
      i && (I.key === "Enter" || I.key === " ") && (I.preventDefault(), i(h, y));
    }, role: i ? "button" : void 0, children: r.map((I) => {
      const S = h[I.field];
      return /* @__PURE__ */ o.jsx(js, { $size: u, children: a ? a(S, h, I) : String(S) }, String(I.field));
    }) }, y)) })
  ] }) });
}, Ac = Ns, ks = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), _s = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), Ls = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), zc = ({
  children: e,
  label: r,
  helperText: t,
  required: n = !1,
  error: s,
  className: a,
  id: i,
  ...u
}) => {
  const d = i || `field-${Math.random().toString(36).substr(2, 9)}`, f = Se.Children.map(e, (l) => Se.isValidElement(l) ? Se.cloneElement(l, {
    id: d,
    required: n,
    error: s
  }) : l);
  return /* @__PURE__ */ o.jsxs(ks, { className: a, ...u, children: [
    /* @__PURE__ */ o.jsxs(_s, { htmlFor: d, hasError: !!s, children: [
      r,
      n && /* @__PURE__ */ o.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    f,
    (t || s) && /* @__PURE__ */ o.jsx(Ls, { hasError: !!s, children: s || t })
  ] });
}, Rs = /* @__PURE__ */ ke(["from{opacity:0;}to{opacity:1;}"]), Ms = /* @__PURE__ */ ke(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), Ps = /* @__PURE__ */ c.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, Rs), Ds = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, Ms, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), $s = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Os = /* @__PURE__ */ c.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), As = /* @__PURE__ */ c.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), zs = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), Fs = /* @__PURE__ */ c.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Fc = ({
  isOpen: e,
  title: r = "",
  children: t,
  onClose: n,
  size: s = "medium",
  closeOnOutsideClick: a = !0,
  showCloseButton: i = !0,
  footer: u,
  hasFooter: d = !0,
  primaryActionText: f = "",
  onPrimaryAction: l,
  primaryActionDisabled: m = !1,
  primaryActionLoading: b = !1,
  secondaryActionText: x = "",
  onSecondaryAction: h,
  secondaryActionDisabled: y = !1,
  className: I = "",
  zIndex: S = 1e3,
  centered: C = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: R = !0
}) => {
  const A = St(null);
  ae(() => {
    const E = (_) => {
      _.key === "Escape" && e && a && n();
    };
    return document.addEventListener("keydown", E), () => {
      document.removeEventListener("keydown", E);
    };
  }, [e, n, a]);
  const N = (E) => {
    A.current && !A.current.contains(E.target) && a && n();
  };
  ae(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const k = /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    x && /* @__PURE__ */ o.jsx(le, { variant: "outline", onClick: h, disabled: y, children: x }),
    f && /* @__PURE__ */ o.jsx(le, { onClick: l, disabled: m, loading: b, children: f })
  ] });
  return e ? uo(/* @__PURE__ */ o.jsx(Ps, { onClick: N, zIndex: S, children: /* @__PURE__ */ o.jsxs(Ds, { ref: A, size: s, className: I, centered: C, scrollable: R, onClick: (E) => E.stopPropagation(), children: [
    (r || i) && /* @__PURE__ */ o.jsxs($s, { children: [
      r && /* @__PURE__ */ o.jsx(Os, { children: r }),
      i && /* @__PURE__ */ o.jsx(As, { onClick: n, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ o.jsx(zs, { scrollable: R, children: t }),
    d && (u || f || x) && /* @__PURE__ */ o.jsx(Fs, { children: u || k })
  ] }) }), document.body) : null;
}, Bs = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), qs = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";border-radius:", ";"], r.colors.border, r.borderRadius.sm), ({
  compact: e,
  theme: r
}) => e ? g(["th,td{padding:", " ", ";}"], r.spacing.xs, r.spacing.sm) : g(["th,td{padding:", " ", ";}"], r.spacing.sm, r.spacing.md)), Hs = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), Us = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), Vs = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => r.colors.background), ({
  isSorted: e,
  theme: r
}) => e && g(["color:", ";"], r.colors.primary)), Ys = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), Gs = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), Ws = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:nth-child(even){background-color:", "50;}"], r.colors.background), ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:hover{background-color:", "aa;}"], r.colors.background), ({
  isSelected: e,
  theme: r
}) => e && g(["background-color:", "15;"], r.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), Ks = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), Qs = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), Xs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), Js = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), Zs = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), ei = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), ri = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), ti = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function Bc({
  columns: e,
  data: r,
  isLoading: t = !1,
  bordered: n = !0,
  striped: s = !0,
  hoverable: a = !0,
  compact: i = !1,
  stickyHeader: u = !1,
  height: d,
  onRowClick: f,
  isRowSelected: l,
  onSort: m,
  sortColumn: b,
  sortDirection: x,
  pagination: h = !1,
  currentPage: y = 1,
  pageSize: I = 10,
  totalRows: S = 0,
  onPageChange: C,
  onPageSizeChange: R,
  className: A,
  emptyMessage: N = "No data available",
  scrollable: k = !0
}) {
  const M = U(() => e.filter((L) => !L.hidden), [e]), E = U(() => Math.ceil(S / I), [S, I]), _ = U(() => {
    if (!h)
      return r;
    const L = (y - 1) * I, H = L + I;
    return S > 0 && r.length <= I ? r : r.slice(L, H);
  }, [r, h, y, I, S]), K = (L) => {
    if (!m)
      return;
    m(L, b === L && x === "asc" ? "desc" : "asc");
  }, W = (L) => {
    L < 1 || L > E || !C || C(L);
  };
  return /* @__PURE__ */ o.jsxs("div", { style: {
    position: "relative"
  }, children: [
    t && /* @__PURE__ */ o.jsx(ri, { children: /* @__PURE__ */ o.jsx(ti, {}) }),
    /* @__PURE__ */ o.jsx(Bs, { height: d, scrollable: k, children: /* @__PURE__ */ o.jsxs(qs, { bordered: n, striped: s, compact: i, className: A, children: [
      /* @__PURE__ */ o.jsx(Hs, { stickyHeader: u, children: /* @__PURE__ */ o.jsx(Us, { children: M.map((L) => /* @__PURE__ */ o.jsxs(Vs, { sortable: L.sortable, isSorted: b === L.id, align: L.align, width: L.width, onClick: () => L.sortable && K(L.id), children: [
        L.header,
        L.sortable && /* @__PURE__ */ o.jsx(Ys, { direction: b === L.id ? x : void 0 })
      ] }, L.id)) }) }),
      /* @__PURE__ */ o.jsx(Gs, { children: _.length > 0 ? _.map((L, H) => /* @__PURE__ */ o.jsx(Ws, { hoverable: a, striped: s, isSelected: l ? l(L, H) : !1, isClickable: !!f, onClick: () => f && f(L, H), children: M.map((re) => /* @__PURE__ */ o.jsx(Ks, { align: re.align, children: re.cell(L, H) }, re.id)) }, H)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: M.length, children: /* @__PURE__ */ o.jsx(Qs, { children: N }) }) }) })
    ] }) }),
    h && E > 0 && /* @__PURE__ */ o.jsxs(Xs, { children: [
      /* @__PURE__ */ o.jsxs(Js, { children: [
        "Showing ",
        Math.min((y - 1) * I + 1, S),
        " to",
        " ",
        Math.min(y * I, S),
        " of ",
        S,
        " entries"
      ] }),
      /* @__PURE__ */ o.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        R && /* @__PURE__ */ o.jsxs(ei, { children: [
          /* @__PURE__ */ o.jsx("span", { children: "Show" }),
          /* @__PURE__ */ o.jsx("select", { value: I, onChange: (L) => R(Number(L.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((L) => /* @__PURE__ */ o.jsx("option", { value: L, children: L }, L)) }),
          /* @__PURE__ */ o.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ o.jsxs(Zs, { children: [
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(1), disabled: y === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(y - 1), disabled: y === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(y + 1), disabled: y === E, children: "Next" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(E), disabled: y === E, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const pe = {
  [D.MORNING_BREAKOUT]: {
    type: D.MORNING_BREAKOUT,
    name: "9:50-10:10 Macro",
    timeRange: {
      start: "09:50:00",
      end: "10:10:00"
    },
    description: "Morning breakout period - high volatility after market open",
    characteristics: ["High Volume", "Breakout Setups", "Gap Fills", "Opening Range"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [D.MID_MORNING_REVERSION]: {
    type: D.MID_MORNING_REVERSION,
    name: "10:50-11:10 Macro",
    timeRange: {
      start: "10:50:00",
      end: "11:10:00"
    },
    description: "Mid-morning reversion period - mean reversion opportunities",
    characteristics: ["Mean Reversion", "Pullback Setups", "Support/Resistance Tests"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !0
  },
  [D.PRE_LUNCH]: {
    type: D.PRE_LUNCH,
    name: "11:50-12:10 Macro",
    timeRange: {
      start: "11:50:00",
      end: "12:10:00"
    },
    description: "Pre-lunch macro window - specific high-activity period within lunch session",
    characteristics: ["Consolidation", "Range Trading", "Pre-Lunch Activity"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    parentMacro: D.LUNCH_MACRO_EXTENDED
  },
  [D.LUNCH_MACRO_EXTENDED]: {
    type: D.LUNCH_MACRO_EXTENDED,
    name: "Lunch Macro (11:30-13:30)",
    timeRange: {
      start: "11:30:00",
      end: "13:30:00"
    },
    description: "Extended lunch period spanning late morning through early afternoon",
    characteristics: ["Multi-Session", "Lunch Trading", "Lower Volume", "Transition Period"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    isMultiSession: !0,
    spansSessions: [Q.NEW_YORK_AM, Q.NEW_YORK_PM],
    subPeriods: []
    // Will be populated with PRE_LUNCH macro
  },
  [D.LUNCH_MACRO]: {
    type: D.LUNCH_MACRO,
    name: "Lunch Macro (12:00-13:30)",
    timeRange: {
      start: "12:00:00",
      end: "13:30:00"
    },
    description: "Traditional lunch time trading - typically lower volume",
    characteristics: ["Low Volume", "Range Bound", "Choppy Price Action"],
    volatilityLevel: 2,
    volumeLevel: 1,
    isHighProbability: !1
  },
  [D.POST_LUNCH]: {
    type: D.POST_LUNCH,
    name: "13:50-14:10 Macro",
    timeRange: {
      start: "13:50:00",
      end: "14:10:00"
    },
    description: "Post-lunch macro window",
    characteristics: ["Volume Pickup", "Trend Resumption"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  },
  [D.PRE_CLOSE]: {
    type: D.PRE_CLOSE,
    name: "14:50-15:10 Macro",
    timeRange: {
      start: "14:50:00",
      end: "15:10:00"
    },
    description: "Pre-close macro window",
    characteristics: ["Institutional Activity", "Position Adjustments"],
    volatilityLevel: 3,
    volumeLevel: 4,
    isHighProbability: !1
  },
  [D.POWER_HOUR]: {
    type: D.POWER_HOUR,
    name: "15:15-15:45 Macro (Power Hour)",
    timeRange: {
      start: "15:15:00",
      end: "15:45:00"
    },
    description: "Last hour macro - high activity before close",
    characteristics: ["High Volume", "Institutional Flows", "EOD Positioning"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [D.MOC]: {
    type: D.MOC,
    name: "MOC (Market on Close)",
    timeRange: {
      start: "15:45:00",
      end: "16:00:00"
    },
    description: "Market on close period",
    characteristics: ["MOC Orders", "Final Positioning", "High Volume"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !1
  },
  [D.LONDON_OPEN]: {
    type: D.LONDON_OPEN,
    name: "London Open",
    timeRange: {
      start: "08:00:00",
      end: "09:00:00"
    },
    description: "London market opening hour",
    characteristics: ["European Activity", "Currency Moves", "News Reactions"],
    volatilityLevel: 4,
    volumeLevel: 4,
    isHighProbability: !0
  },
  [D.LONDON_NY_OVERLAP]: {
    type: D.LONDON_NY_OVERLAP,
    name: "London/NY Overlap",
    timeRange: {
      start: "14:00:00",
      end: "16:00:00"
    },
    description: "London and New York session overlap",
    characteristics: ["Highest Volume", "Major Moves", "Cross-Market Activity"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [D.CUSTOM]: {
    type: D.CUSTOM,
    name: "Custom Period",
    timeRange: {
      start: "00:00:00",
      end: "23:59:59"
    },
    description: "User-defined custom time period",
    characteristics: ["Custom"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  }
}, oi = () => {
  const e = Object.values(ni).map((s) => ({
    id: s.type,
    ...s
  })), t = [{
    ...pe[D.LUNCH_MACRO_EXTENDED],
    id: "lunch-macro-extended",
    subPeriods: [{
      ...pe[D.PRE_LUNCH],
      id: "pre-lunch-sub"
    }]
  }], n = {};
  return e.forEach((s) => {
    s.macroPeriods.forEach((a) => {
      n[a.type] = {
        ...a,
        parentSession: s.type
      };
    });
  }), t.forEach((s) => {
    n[s.type] = {
      ...s,
      spansSessions: s.spansSessions
    };
  }), {
    sessions: e,
    sessionsByType: e.reduce((s, a) => (s[a.type] = a, s), {}),
    macrosByType: n,
    multiSessionMacros: t
  };
}, ni = {
  [Q.NEW_YORK_AM]: {
    type: Q.NEW_YORK_AM,
    name: "New York AM Session",
    timeRange: {
      start: "09:30:00",
      end: "12:00:00"
    },
    description: "New York morning session - high activity and volatility",
    timezone: "America/New_York",
    characteristics: ["High Volume", "Trend Development", "Breakout Opportunities"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[D.MORNING_BREAKOUT],
      id: "morning-breakout"
    }, {
      ...pe[D.MID_MORNING_REVERSION],
      id: "mid-morning-reversion"
    }, {
      ...pe[D.PRE_LUNCH],
      id: "pre-lunch"
    }]
  },
  [Q.NEW_YORK_PM]: {
    type: Q.NEW_YORK_PM,
    name: "New York PM Session",
    timeRange: {
      start: "12:00:00",
      end: "16:00:00"
    },
    description: "New York afternoon session - institutional activity increases toward close",
    timezone: "America/New_York",
    characteristics: ["Institutional Flows", "EOD Positioning", "Power Hour Activity"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[D.LUNCH_MACRO],
      id: "lunch-macro"
    }, {
      ...pe[D.POST_LUNCH],
      id: "post-lunch"
    }, {
      ...pe[D.PRE_CLOSE],
      id: "pre-close"
    }, {
      ...pe[D.POWER_HOUR],
      id: "power-hour"
    }, {
      ...pe[D.MOC],
      id: "moc"
    }]
  },
  [Q.LONDON]: {
    type: Q.LONDON,
    name: "London Session",
    timeRange: {
      start: "08:00:00",
      end: "16:00:00"
    },
    description: "London trading session - European market activity",
    timezone: "Europe/London",
    characteristics: ["European Activity", "Currency Focus", "News-Driven"],
    color: "#1f2937",
    // Dark Gray
    macroPeriods: [{
      ...pe[D.LONDON_OPEN],
      id: "london-open"
    }, {
      ...pe[D.LONDON_NY_OVERLAP],
      id: "london-ny-overlap"
    }]
  },
  [Q.ASIA]: {
    type: Q.ASIA,
    name: "Asia Session",
    timeRange: {
      start: "18:00:00",
      end: "03:00:00"
    },
    description: "Asian trading session - typically lower volatility",
    timezone: "Asia/Tokyo",
    characteristics: ["Lower Volume", "Range Trading", "News Reactions"],
    color: "#4b5563",
    // Gray
    macroPeriods: []
  },
  [Q.PRE_MARKET]: {
    type: Q.PRE_MARKET,
    name: "Pre-Market",
    timeRange: {
      start: "04:00:00",
      end: "09:30:00"
    },
    description: "Pre-market trading hours",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "News Reactions", "Gap Setups"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [Q.AFTER_HOURS]: {
    type: Q.AFTER_HOURS,
    name: "After Hours",
    timeRange: {
      start: "16:00:00",
      end: "20:00:00"
    },
    description: "After-hours trading",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "Earnings Reactions", "News-Driven"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [Q.OVERNIGHT]: {
    type: Q.OVERNIGHT,
    name: "Overnight",
    timeRange: {
      start: "20:00:00",
      end: "04:00:00"
    },
    description: "Overnight session",
    timezone: "America/New_York",
    characteristics: ["Very Low Volume", "Futures Activity"],
    color: "#374151",
    // Dark Gray
    macroPeriods: []
  }
};
class ie {
  /**
   * Initialize and get the session hierarchy
   */
  static getSessionHierarchy() {
    return this.hierarchy || (this.hierarchy = this.buildHierarchy()), this.hierarchy;
  }
  /**
   * Build the complete session hierarchy with overlapping macro support
   */
  static buildHierarchy() {
    return oi();
  }
  /**
   * Parse time string to minutes since midnight
   */
  static timeToMinutes(r) {
    const [t, n, s = 0] = r.split(":").map(Number);
    return t * 60 + n + s / 60;
  }
  /**
   * Convert minutes since midnight to time string
   */
  static minutesToTime(r) {
    const t = Math.floor(r / 60), n = Math.floor(r % 60), s = Math.floor(r % 1 * 60);
    return `${t.toString().padStart(2, "0")}:${n.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
  }
  /**
   * Check if a time falls within a time range
   */
  static isTimeInRange(r, t) {
    const n = this.timeToMinutes(r), s = this.timeToMinutes(t.start), a = this.timeToMinutes(t.end);
    return a < s ? n >= s || n <= a : n >= s && n <= a;
  }
  /**
   * Validate a time and suggest appropriate session/macro with overlapping support
   */
  static validateTime(r) {
    var a;
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(r))
      return {
        isValid: !1,
        error: "Invalid time format. Use HH:MM or HH:MM:SS format."
      };
    const n = this.getSessionHierarchy(), s = [];
    for (const [i, u] of Object.entries(n.macrosByType))
      this.isTimeInRange(r, u.timeRange) && s.push({
        type: i,
        macro: u,
        isSubPeriod: !!u.parentMacro
      });
    if (s.length > 0) {
      const u = s.sort((f, l) => {
        if (f.isSubPeriod && !l.isSubPeriod)
          return -1;
        if (!f.isSubPeriod && l.isSubPeriod)
          return 1;
        const m = this.timeToMinutes(f.macro.timeRange.end) - this.timeToMinutes(f.macro.timeRange.start), b = this.timeToMinutes(l.macro.timeRange.end) - this.timeToMinutes(l.macro.timeRange.start);
        return m - b;
      })[0], d = s.length > 1;
      return {
        isValid: !0,
        suggestedMacro: u.type,
        suggestedSession: u.macro.parentSession || ((a = u.macro.spansSessions) == null ? void 0 : a[0]),
        warning: d ? `Time falls within ${s.length} overlapping macro periods. Suggesting most specific: ${u.macro.name}` : void 0
      };
    }
    for (const i of n.sessions)
      if (this.isTimeInRange(r, i.timeRange))
        return {
          isValid: !0,
          suggestedSession: i.type,
          warning: "Time falls within session but not in a specific macro period."
        };
    return {
      isValid: !0,
      warning: "Time does not fall within any defined session or macro period."
    };
  }
  /**
   * Get session by type
   */
  static getSession(r) {
    return this.getSessionHierarchy().sessionsByType[r] || null;
  }
  /**
   * Get macro period by type
   */
  static getMacroPeriod(r) {
    return this.getSessionHierarchy().macrosByType[r] || null;
  }
  /**
   * Get all macro periods for a session
   */
  static getMacroPeriodsForSession(r) {
    const t = this.getSession(r);
    return (t == null ? void 0 : t.macroPeriods) || [];
  }
  /**
   * Create a session selection
   */
  static createSessionSelection(r, t, n) {
    if (t) {
      const s = this.getMacroPeriod(t);
      return {
        session: s == null ? void 0 : s.parentSession,
        macroPeriod: t,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Macro",
        selectionType: "macro"
      };
    }
    if (r) {
      const s = this.getSession(r);
      return {
        session: r,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Session",
        selectionType: "session"
      };
    }
    return n ? {
      customTimeRange: n,
      displayLabel: `${n.start} - ${n.end}`,
      selectionType: "custom"
    } : {
      displayLabel: "No Selection",
      selectionType: "custom"
    };
  }
  /**
   * Filter sessions and macros based on criteria
   */
  static filterSessions(r = {}) {
    var a, i;
    const t = this.getSessionHierarchy();
    let n = [...t.sessions], s = Object.values(t.macrosByType);
    return r.activeOnly && (n = n.filter((u) => u.isActive)), (a = r.sessionTypes) != null && a.length && (n = n.filter((u) => r.sessionTypes.includes(u.type))), (i = r.macroTypes) != null && i.length && (s = s.filter((u) => r.macroTypes.includes(u.type))), r.highProbabilityOnly && (s = s.filter((u) => u.isHighProbability)), r.minVolatility !== void 0 && (s = s.filter((u) => u.volatilityLevel >= r.minVolatility)), r.maxVolatility !== void 0 && (s = s.filter((u) => u.volatilityLevel <= r.maxVolatility)), {
      sessions: n,
      macros: s
    };
  }
  /**
   * Get current active session based on current time
   */
  static getCurrentSession() {
    const r = /* @__PURE__ */ new Date(), t = `${r.getHours().toString().padStart(2, "0")}:${r.getMinutes().toString().padStart(2, "0")}:00`, n = this.validateTime(t);
    return n.suggestedMacro ? this.createSessionSelection(n.suggestedSession, n.suggestedMacro) : n.suggestedSession ? this.createSessionSelection(n.suggestedSession) : null;
  }
  /**
   * Check if two time ranges overlap
   */
  static timeRangesOverlap(r, t) {
    const n = this.timeToMinutes(r.start), s = this.timeToMinutes(r.end), a = this.timeToMinutes(t.start), i = this.timeToMinutes(t.end);
    return Math.max(n, a) < Math.min(s, i);
  }
  /**
   * Get display options for UI dropdowns
   */
  static getDisplayOptions() {
    const r = this.getSessionHierarchy(), t = r.sessions.map((s) => ({
      value: s.type,
      label: s.name,
      group: "Sessions"
    })), n = Object.values(r.macrosByType).filter((s) => s.parentSession).map((s) => {
      var a;
      return {
        value: s.type,
        label: s.name,
        group: ((a = r.sessionsByType[s.parentSession]) == null ? void 0 : a.name) || "Other",
        parentSession: s.parentSession
      };
    });
    return {
      sessionOptions: t,
      macroOptions: n
    };
  }
  /**
   * Get all overlapping macro periods for a given time
   */
  static getOverlappingMacros(r) {
    const t = this.getSessionHierarchy(), n = [];
    for (const [s, a] of Object.entries(t.macrosByType))
      this.isTimeInRange(r, a.timeRange) && n.push({
        type: s,
        macro: a,
        isSubPeriod: !!a.parentMacro,
        isMultiSession: !!a.spansSessions
      });
    return n.sort((s, a) => {
      if (s.isSubPeriod && !a.isSubPeriod)
        return -1;
      if (!s.isSubPeriod && a.isSubPeriod)
        return 1;
      const i = this.timeToMinutes(s.macro.timeRange.end) - this.timeToMinutes(s.macro.timeRange.start), u = this.timeToMinutes(a.macro.timeRange.end) - this.timeToMinutes(a.macro.timeRange.start);
      return i - u;
    });
  }
  /**
   * Get multi-session macros
   */
  static getMultiSessionMacros() {
    return this.getSessionHierarchy().multiSessionMacros || [];
  }
  /**
   * Check if a macro period has sub-periods
   */
  static hasSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return !!(t != null && t.subPeriods && t.subPeriods.length > 0);
  }
  /**
   * Get sub-periods for a macro
   */
  static getSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return (t == null ? void 0 : t.subPeriods) || [];
  }
  /**
   * Convert legacy session string to new session selection
   */
  static convertLegacySession(r) {
    const n = {
      "NY Open": {
        session: Q.NEW_YORK_AM
      },
      "London Open": {
        session: Q.LONDON
      },
      "Lunch Macro": {
        macro: D.LUNCH_MACRO_EXTENDED
      },
      // Updated to use extended lunch macro
      "Lunch Macro (11:30-13:30)": {
        macro: D.LUNCH_MACRO_EXTENDED
      },
      "Lunch Macro (12:00-13:30)": {
        macro: D.LUNCH_MACRO
      },
      MOC: {
        macro: D.MOC
      },
      Overnight: {
        session: Q.OVERNIGHT
      },
      "Pre-Market": {
        session: Q.PRE_MARKET
      },
      "After Hours": {
        session: Q.AFTER_HOURS
      },
      "Power Hour": {
        macro: D.POWER_HOUR
      },
      "10:50-11:10": {
        macro: D.MID_MORNING_REVERSION
      },
      "11:50-12:10": {
        macro: D.PRE_LUNCH
      },
      "15:15-15:45": {
        macro: D.POWER_HOUR
      }
    }[r];
    return n ? this.createSessionSelection(n.session, n.macro) : null;
  }
}
we(ie, "hierarchy", null);
const si = (e = {}) => {
  const {
    initialSelection: r,
    autoDetectCurrent: t = !1,
    filterOptions: n = {},
    onSelectionChange: s,
    validateTimes: a = !0
  } = e, [i, u] = V(r || {
    displayLabel: "No Selection",
    selectionType: "custom"
  }), d = U(() => ie.getCurrentSession(), []), f = U(() => d !== null, [d]), {
    availableSessions: l,
    availableMacros: m
  } = U(() => {
    const {
      sessions: k,
      macros: M
    } = ie.filterSessions(n), {
      sessionOptions: E,
      macroOptions: _
    } = ie.getDisplayOptions(), K = E.filter((L) => k.some((H) => H.type === L.value)), W = _.filter((L) => M.some((H) => H.type === L.value));
    return {
      availableSessions: K,
      availableMacros: W
    };
  }, [n]), b = U(() => l.map((k) => {
    const M = m.filter((E) => E.parentSession === k.value).map((E) => ({
      value: E.value,
      label: E.label
    }));
    return {
      session: k.value,
      sessionLabel: k.label,
      macros: M
    };
  }), [l, m]);
  ae(() => {
    t && d && !r && u(d);
  }, [t, d, r]), ae(() => {
    s == null || s(i);
  }, [i, s]);
  const x = F((k) => {
    const M = ie.createSessionSelection(k);
    u(M);
  }, []), h = F((k) => {
    const M = ie.createSessionSelection(void 0, k);
    u(M);
  }, []), y = F((k) => {
    const M = ie.createSessionSelection(void 0, void 0, k);
    u(M);
  }, []), I = F(() => {
    u({
      displayLabel: "No Selection",
      selectionType: "custom"
    });
  }, []), S = F((k) => a ? ie.validateTime(k) : {
    isValid: !0
  }, [a]), C = U(() => {
    if (i.selectionType === "session" && i.session)
      return ie.getSession(i.session) !== null;
    if (i.selectionType === "macro" && i.macroPeriod)
      return ie.getMacroPeriod(i.macroPeriod) !== null;
    if (i.selectionType === "custom" && i.customTimeRange) {
      const k = S(i.customTimeRange.start), M = S(i.customTimeRange.end);
      return k.isValid && M.isValid;
    }
    return i.selectionType === "custom" && !i.customTimeRange;
  }, [i, S]), R = F((k) => ie.getSession(k), []), A = F((k) => ie.getMacroPeriod(k), []), N = F((k) => ie.convertLegacySession(k), []);
  return {
    // State
    selection: i,
    // Selection methods
    selectSession: x,
    selectMacro: h,
    selectCustomRange: y,
    clearSelection: I,
    // Validation
    validateTime: S,
    isValidSelection: C,
    // Options
    availableSessions: l,
    availableMacros: m,
    hierarchicalOptions: b,
    // Current session
    currentSession: d,
    isCurrentSessionActive: f,
    // Utilities
    getSessionDetails: R,
    getMacroDetails: A,
    convertLegacySession: N
  };
}, ii = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-1reqqnl-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), ai = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectorContainer",
  componentId: "sc-1reqqnl-1"
})(["position:relative;border:1px solid ", ";border-radius:", ";background:", ";transition:all 0.2s ease;opacity:", ";pointer-events:", ";&:hover{border-color:", "40;}&:focus-within{border-color:", ";box-shadow:0 0 0 3px ", "20;}"], ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  disabled: e
}) => e ? 0.6 : 1, ({
  disabled: e
}) => e ? "none" : "auto", ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), ci = /* @__PURE__ */ c.div.withConfig({
  displayName: "SelectedValue",
  componentId: "sc-1reqqnl-2"
})(["padding:", ";color:", ";font-size:", ";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.md) || "1rem";
}), li = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownIcon",
  componentId: "sc-1reqqnl-3"
})(["transition:transform 0.2s ease;transform:", ";color:", ";"], ({
  isOpen: e
}) => e ? "rotate(180deg)" : "rotate(0deg)", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), di = /* @__PURE__ */ c.div.withConfig({
  displayName: "DropdownMenu",
  componentId: "sc-1reqqnl-4"
})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:", ";border:1px solid ", ";border-radius:", ";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  isOpen: e
}) => e ? "block" : "none"), pi = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionGroup",
  componentId: "sc-1reqqnl-5"
})(["border-bottom:1px solid ", ";background:", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), ui = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionHeader",
  componentId: "sc-1reqqnl-6"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ", ";&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.surface) || "#1f2937";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), fi = /* @__PURE__ */ c.div.withConfig({
  displayName: "MultiSessionIndicator",
  componentId: "sc-1reqqnl-7"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}), gi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionGroup",
  componentId: "sc-1reqqnl-8"
})(["border-bottom:1px solid ", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), mi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SessionHeader",
  componentId: "sc-1reqqnl-9"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), hi = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroList",
  componentId: "sc-1reqqnl-10"
})(["background:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), xi = /* @__PURE__ */ c.div.withConfig({
  displayName: "MacroItem",
  componentId: "sc-1reqqnl-11"
})(["padding:", " ", ";color:", ";cursor:pointer;font-size:", ";transition:all 0.2s ease;border-left:3px solid ", ";&:hover{background:", "20;color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "24px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), bi = /* @__PURE__ */ c.div.withConfig({
  displayName: "CurrentSessionIndicator",
  componentId: "sc-1reqqnl-12"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.success) || "#10b981";
}), yi = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-1reqqnl-13"
})(["color:", ";font-size:", ";margin-top:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#ef4444";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), qc = ({
  value: e,
  onChange: r,
  showMacroPeriods: t = !0,
  showCurrentSession: n = !0,
  placeholder: s = "Select session or macro period",
  disabled: a = !1,
  error: i,
  className: u
}) => {
  const [d, f] = V(!1), {
    hierarchicalOptions: l,
    currentSession: m,
    selectSession: b,
    selectMacro: x
  } = si({
    onSelectionChange: r
  }), h = U(() => ie.getMultiSessionMacros(), []), y = U(() => e != null && e.displayLabel ? e.displayLabel : s, [e, s]), I = (N) => {
    b(N), f(!1);
  }, S = (N) => {
    x(N), f(!1);
  }, C = (N) => (e == null ? void 0 : e.session) === N && (e == null ? void 0 : e.selectionType) === "session", R = (N) => (e == null ? void 0 : e.macroPeriod) === N && (e == null ? void 0 : e.selectionType) === "macro", A = (N) => (m == null ? void 0 : m.session) === N;
  return /* @__PURE__ */ o.jsxs(ii, { className: u, hasError: !!i, children: [
    /* @__PURE__ */ o.jsxs(ai, { hasError: !!i, disabled: a, onClick: () => !a && f(!d), children: [
      /* @__PURE__ */ o.jsxs(ci, { children: [
        /* @__PURE__ */ o.jsx("span", { children: y }),
        /* @__PURE__ */ o.jsx(li, { isOpen: d, children: "▼" })
      ] }),
      /* @__PURE__ */ o.jsxs(di, { isOpen: d, children: [
        t && h.length > 0 && /* @__PURE__ */ o.jsx(pi, { children: h.map((N) => /* @__PURE__ */ o.jsxs(ui, { isSelected: R(N.type), onClick: (k) => {
          k.stopPropagation(), S(N.type);
        }, children: [
          /* @__PURE__ */ o.jsx("span", { children: N.name }),
          /* @__PURE__ */ o.jsx(fi, { children: "🌐 MULTI-SESSION" })
        ] }, N.type)) }),
        l.map(({
          session: N,
          sessionLabel: k,
          macros: M
        }) => /* @__PURE__ */ o.jsxs(gi, { children: [
          /* @__PURE__ */ o.jsxs(mi, { isSelected: C(N), onClick: (E) => {
            E.stopPropagation(), I(N);
          }, children: [
            /* @__PURE__ */ o.jsx("span", { children: k }),
            n && A(N) && /* @__PURE__ */ o.jsx(bi, { children: "🔴 LIVE" })
          ] }),
          t && M.length > 0 && /* @__PURE__ */ o.jsx(hi, { children: M.map(({
            value: E,
            label: _
          }) => /* @__PURE__ */ o.jsxs(xi, { isSelected: R(E), onClick: (K) => {
            K.stopPropagation(), S(E);
          }, children: [
            _,
            ie.hasSubPeriods(E) && /* @__PURE__ */ o.jsx("span", { style: {
              marginLeft: "8px",
              fontSize: "0.75rem",
              opacity: 0.7
            }, children: "📋 Has sub-periods" })
          ] }, E)) })
        ] }, N))
      ] })
    ] }),
    i && /* @__PURE__ */ o.jsx(yi, { children: i })
  ] });
}, T = {
  DATE: "date",
  SYMBOL: "symbol",
  DIRECTION: "direction",
  MODEL_TYPE: "model_type",
  SESSION: "session",
  ENTRY_PRICE: "entry_price",
  EXIT_PRICE: "exit_price",
  R_MULTIPLE: "r_multiple",
  ACHIEVED_PL: "achieved_pl",
  WIN_LOSS: "win_loss",
  PATTERN_QUALITY: "pattern_quality_rating",
  ENTRY_TIME: "entry_time",
  EXIT_TIME: "exit_time"
}, Cr = /* @__PURE__ */ c.span.withConfig({
  displayName: "ProfitLossCell",
  componentId: "sc-14bks31-0"
})(["color:", ";font-weight:", ";"], ({
  isProfit: e,
  theme: r
}) => e ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), Nt = /* @__PURE__ */ c(Qe).withConfig({
  displayName: "DirectionBadge",
  componentId: "sc-14bks31-1"
})(["background-color:", ";color:white;"], ({
  direction: e,
  theme: r
}) => e === "Long" ? r.colors.success || "#10b981" : r.colors.error || "#ef4444"), kt = /* @__PURE__ */ c.span.withConfig({
  displayName: "QualityRating",
  componentId: "sc-14bks31-2"
})(["color:", ";font-weight:", ";"], ({
  rating: e,
  theme: r
}) => e >= 4 ? r.colors.success || "#10b981" : e >= 3 ? r.colors.warning || "#f59e0b" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), Ir = /* @__PURE__ */ c.span.withConfig({
  displayName: "RMultipleCell",
  componentId: "sc-14bks31-3"
})(["color:", ";font-weight:", ";"], ({
  rMultiple: e,
  theme: r
}) => e > 0 ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), Ae = (e) => e == null ? "-" : new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2
}).format(e), jr = (e) => {
  try {
    return new Date(e).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch {
    return e;
  }
}, it = (e) => e || "-", vi = () => [{
  id: T.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => jr(e.trade[T.DATE])
}, {
  id: T.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "80px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: T.DIRECTION,
  header: "Direction",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Nt, { direction: e.trade[T.DIRECTION], size: "small", children: e.trade[T.DIRECTION] })
}, {
  id: T.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[T.MODEL_TYPE] || "-"
}, {
  id: T.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[T.SESSION] || "-"
}, {
  id: T.ENTRY_PRICE,
  header: "Entry",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ae(e.trade[T.ENTRY_PRICE])
}, {
  id: T.EXIT_PRICE,
  header: "Exit",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Ae(e.trade[T.EXIT_PRICE])
}, {
  id: T.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => {
    var r;
    return /* @__PURE__ */ o.jsx(Ir, { rMultiple: e.trade[T.R_MULTIPLE] || 0, children: e.trade[T.R_MULTIPLE] ? `${(r = e.trade[T.R_MULTIPLE]) == null ? void 0 : r.toFixed(2)}R` : "-" });
  }
}, {
  id: T.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(Cr, { isProfit: (e.trade[T.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[T.ACHIEVED_PL]) })
}, {
  id: T.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Qe, { variant: e.trade[T.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[T.WIN_LOSS] || "-" })
}, {
  id: T.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(kt, { rating: e.trade[T.PATTERN_QUALITY] || 0, children: e.trade[T.PATTERN_QUALITY] ? `${e.trade[T.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: T.ENTRY_TIME,
  header: "Entry Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => it(e.trade[T.ENTRY_TIME])
}, {
  id: T.EXIT_TIME,
  header: "Exit Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => it(e.trade[T.EXIT_TIME])
}], wi = () => [{
  id: T.DATE,
  header: "Date",
  sortable: !0,
  width: "90px",
  cell: (e) => jr(e.trade[T.DATE])
}, {
  id: T.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "60px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: T.DIRECTION,
  header: "Dir",
  sortable: !0,
  width: "50px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Nt, { direction: e.trade[T.DIRECTION], size: "small", children: e.trade[T.DIRECTION].charAt(0) })
}, {
  id: T.R_MULTIPLE,
  header: "R",
  sortable: !0,
  width: "60px",
  align: "right",
  cell: (e) => {
    var r;
    return /* @__PURE__ */ o.jsx(Ir, { rMultiple: e.trade[T.R_MULTIPLE] || 0, children: e.trade[T.R_MULTIPLE] ? `${(r = e.trade[T.R_MULTIPLE]) == null ? void 0 : r.toFixed(1)}R` : "-" });
  }
}, {
  id: T.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "80px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(Cr, { isProfit: (e.trade[T.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[T.ACHIEVED_PL]) })
}, {
  id: T.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "60px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Qe, { variant: e.trade[T.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[T.WIN_LOSS] === "Win" ? "W" : e.trade[T.WIN_LOSS] === "Loss" ? "L" : "-" })
}], Si = () => [{
  id: T.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => jr(e.trade[T.DATE])
}, {
  id: T.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[T.MODEL_TYPE] || "-"
}, {
  id: T.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[T.SESSION] || "-"
}, {
  id: T.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => {
    var r;
    return /* @__PURE__ */ o.jsx(Ir, { rMultiple: e.trade[T.R_MULTIPLE] || 0, children: e.trade[T.R_MULTIPLE] ? `${(r = e.trade[T.R_MULTIPLE]) == null ? void 0 : r.toFixed(2)}R` : "-" });
  }
}, {
  id: T.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(Cr, { isProfit: (e.trade[T.ACHIEVED_PL] || 0) > 0, children: Ae(e.trade[T.ACHIEVED_PL]) })
}, {
  id: T.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(kt, { rating: e.trade[T.PATTERN_QUALITY] || 0, children: e.trade[T.PATTERN_QUALITY] ? `${e.trade[T.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: T.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(Qe, { variant: e.trade[T.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[T.WIN_LOSS] || "-" })
}], Ci = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-uyrnn-0"
})(["", " ", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:nth-child(even){background-color:", "50;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:hover{background-color:", "aa;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  isSelected: e,
  theme: r
}) => {
  var t;
  return e && g(["background-color:", "15;"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}, ({
  isClickable: e
}) => e && g(["cursor:pointer;"]), ({
  isExpanded: e,
  theme: r
}) => {
  var t;
  return e && g(["border-bottom:2px solid ", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), at = /* @__PURE__ */ c.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-uyrnn-1"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";padding:", " ", ";vertical-align:middle;"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), Ii = /* @__PURE__ */ c.tr.withConfig({
  displayName: "ExpandedRow",
  componentId: "sc-uyrnn-2"
})(["display:", ";"], ({
  isVisible: e
}) => e ? "table-row" : "none"), ji = /* @__PURE__ */ c.td.withConfig({
  displayName: "ExpandedCell",
  componentId: "sc-uyrnn-3"
})(["padding:0;border-bottom:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Ti = /* @__PURE__ */ c.div.withConfig({
  displayName: "ExpandedContent",
  componentId: "sc-uyrnn-4"
})(["padding:", ";background-color:", "30;border-left:3px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Ei = /* @__PURE__ */ c.button.withConfig({
  displayName: "ExpandButton",
  componentId: "sc-uyrnn-5"
})(["background:none;border:none;cursor:pointer;padding:", ";color:", ";font-size:", ";display:flex;align-items:center;justify-content:center;border-radius:", ";transition:all 0.2s ease;&:hover{background-color:", ";color:", ";}&:focus{outline:2px solid ", ";outline-offset:2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Ni = /* @__PURE__ */ c.span.withConfig({
  displayName: "ExpandIcon",
  componentId: "sc-uyrnn-6"
})(["display:inline-block;transition:transform 0.2s ease;transform:", ";&::after{content:'▶';}"], ({
  isExpanded: e
}) => e ? "rotate(90deg)" : "rotate(0deg)"), ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "TradeDetails",
  componentId: "sc-uyrnn-7"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), De = /* @__PURE__ */ c.div.withConfig({
  displayName: "DetailGroup",
  componentId: "sc-uyrnn-8"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), $e = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailLabel",
  componentId: "sc-uyrnn-9"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), ce = /* @__PURE__ */ c.span.withConfig({
  displayName: "DetailValue",
  componentId: "sc-uyrnn-10"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}), _i = ({
  trade: e
}) => /* @__PURE__ */ o.jsxs(ki, { children: [
  e.fvg_details && /* @__PURE__ */ o.jsxs(De, { children: [
    /* @__PURE__ */ o.jsx($e, { children: "FVG Details" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Type: ",
      e.fvg_details.rd_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry Version: ",
      e.fvg_details.entry_version || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Draw on Liquidity: ",
      e.fvg_details.draw_on_liquidity || "-"
    ] })
  ] }),
  e.setup && /* @__PURE__ */ o.jsxs(De, { children: [
    /* @__PURE__ */ o.jsx($e, { children: "Setup Classification" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Primary: ",
      e.setup.primary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Secondary: ",
      e.setup.secondary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Liquidity: ",
      e.setup.liquidity_taken || "-"
    ] })
  ] }),
  e.analysis && /* @__PURE__ */ o.jsxs(De, { children: [
    /* @__PURE__ */ o.jsx($e, { children: "Analysis" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "DOL Target: ",
      e.analysis.dol_target_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Path Quality: ",
      e.analysis.path_quality || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Clustering: ",
      e.analysis.clustering || "-"
    ] })
  ] }),
  /* @__PURE__ */ o.jsxs(De, { children: [
    /* @__PURE__ */ o.jsx($e, { children: "Timing" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry: ",
      e.trade.entry_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Exit: ",
      e.trade.exit_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "FVG: ",
      e.trade.fvg_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "RD: ",
      e.trade.rd_time || "-"
    ] })
  ] }),
  e.trade.notes && /* @__PURE__ */ o.jsxs(De, { style: {
    gridColumn: "1 / -1"
  }, children: [
    /* @__PURE__ */ o.jsx($e, { children: "Notes" }),
    /* @__PURE__ */ o.jsx(ce, { children: e.trade.notes })
  ] })
] }), Li = ({
  trade: e,
  index: r,
  columns: t,
  isSelected: n = !1,
  hoverable: s = !0,
  striped: a = !0,
  expandable: i = !1,
  isExpanded: u = !1,
  onRowClick: d,
  onToggleExpand: f,
  expandedContent: l
}) => {
  const [m, b] = V(!1), x = u !== void 0 ? u : m, h = (S) => {
    S.target.closest("button") || d == null || d(e, r);
  }, y = (S) => {
    S.stopPropagation(), f ? f(e, r) : b(!m);
  }, I = t.filter((S) => !S.hidden);
  return /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    /* @__PURE__ */ o.jsxs(Ci, { hoverable: s, striped: a, isSelected: n, isClickable: !!d, isExpanded: x, onClick: h, children: [
      i && /* @__PURE__ */ o.jsx(at, { align: "center", style: {
        width: "40px",
        padding: "8px"
      }, children: /* @__PURE__ */ o.jsx(Ei, { onClick: y, children: /* @__PURE__ */ o.jsx(Ni, { isExpanded: x }) }) }),
      I.map((S) => /* @__PURE__ */ o.jsx(at, { align: S.align, children: S.cell(e, r) }, S.id))
    ] }),
    i && /* @__PURE__ */ o.jsx(Ii, { isVisible: x, children: /* @__PURE__ */ o.jsx(ji, { colSpan: I.length + 1, children: /* @__PURE__ */ o.jsx(Ti, { children: l || /* @__PURE__ */ o.jsx(_i, { trade: e }) }) }) })
  ] });
}, ge = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  DATE_FROM: "dateFrom",
  DATE_TO: "dateTo",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  MIN_R_MULTIPLE: "min_r_multiple",
  MAX_R_MULTIPLE: "max_r_multiple",
  MIN_PATTERN_QUALITY: "min_pattern_quality",
  MAX_PATTERN_QUALITY: "max_pattern_quality"
}, Ri = /* @__PURE__ */ c.div.withConfig({
  displayName: "FiltersContainer",
  componentId: "sc-32k3gq-0"
})(["display:flex;flex-direction:column;gap:", ";padding:", ";background-color:", ";border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), ct = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterRow",
  componentId: "sc-32k3gq-1"
})(["display:flex;gap:", ";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), xe = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterGroup",
  componentId: "sc-32k3gq-2"
})(["display:flex;flex-direction:column;gap:", ";min-width:120px;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), be = /* @__PURE__ */ c.label.withConfig({
  displayName: "FilterLabel",
  componentId: "sc-32k3gq-3"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Mi = /* @__PURE__ */ c.div.withConfig({
  displayName: "FilterActions",
  componentId: "sc-32k3gq-4"
})(["display:flex;gap:", ";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), Pi = /* @__PURE__ */ c.div.withConfig({
  displayName: "AdvancedFilters",
  componentId: "sc-32k3gq-5"
})(["display:", ";flex-direction:column;gap:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  isVisible: e
}) => e ? "flex" : "none", ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), lt = /* @__PURE__ */ c.div.withConfig({
  displayName: "RangeInputGroup",
  componentId: "sc-32k3gq-6"
})(["display:flex;gap:", ";align-items:center;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), dt = /* @__PURE__ */ c.span.withConfig({
  displayName: "RangeLabel",
  componentId: "sc-32k3gq-7"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Di = ({
  filters: e,
  onFiltersChange: r,
  onReset: t,
  isLoading: n = !1,
  showAdvanced: s = !1,
  onToggleAdvanced: a
}) => {
  var f, l, m, b;
  const i = (x, h) => {
    r({
      ...e,
      [x]: h
    });
  }, u = () => {
    r({}), t == null || t();
  }, d = Object.values(e).some((x) => x !== void 0 && x !== "" && x !== null);
  return /* @__PURE__ */ o.jsxs(Ri, { children: [
    /* @__PURE__ */ o.jsxs(ct, { children: [
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Date From" }),
        /* @__PURE__ */ o.jsx(je, { type: "date", value: e.dateFrom || "", onChange: (x) => i(ge.DATE_FROM, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Date To" }),
        /* @__PURE__ */ o.jsx(je, { type: "date", value: e.dateTo || "", onChange: (x) => i(ge.DATE_TO, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Model Type" }),
        /* @__PURE__ */ o.jsx(Pe, { options: [{
          value: "",
          label: "All Models"
        }, {
          value: "RD-Cont",
          label: "RD-Cont"
        }, {
          value: "FVG-RD",
          label: "FVG-RD"
        }, {
          value: "True-RD",
          label: "True-RD"
        }, {
          value: "IMM-RD",
          label: "IMM-RD"
        }, {
          value: "Dispersed-RD",
          label: "Dispersed-RD"
        }, {
          value: "Wide-Gap-RD",
          label: "Wide-Gap-RD"
        }], value: e.model_type || "", onChange: (x) => i(ge.MODEL_TYPE, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Session" }),
        /* @__PURE__ */ o.jsx(Pe, { options: [{
          value: "",
          label: "All Sessions"
        }, {
          value: "Pre-Market",
          label: "Pre-Market"
        }, {
          value: "NY Open",
          label: "NY Open"
        }, {
          value: "10:50-11:10",
          label: "10:50-11:10"
        }, {
          value: "11:50-12:10",
          label: "11:50-12:10"
        }, {
          value: "Lunch Macro",
          label: "Lunch Macro"
        }, {
          value: "13:50-14:10",
          label: "13:50-14:10"
        }, {
          value: "14:50-15:10",
          label: "14:50-15:10"
        }, {
          value: "15:15-15:45",
          label: "15:15-15:45"
        }, {
          value: "MOC",
          label: "MOC"
        }, {
          value: "Post MOC",
          label: "Post MOC"
        }], value: e.session || "", onChange: (x) => i(ge.SESSION, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Direction" }),
        /* @__PURE__ */ o.jsx(Pe, { options: [{
          value: "",
          label: "All Directions"
        }, {
          value: "Long",
          label: "Long"
        }, {
          value: "Short",
          label: "Short"
        }], value: e.direction || "", onChange: (x) => i(ge.DIRECTION, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Result" }),
        /* @__PURE__ */ o.jsx(Pe, { options: [{
          value: "",
          label: "All Results"
        }, {
          value: "Win",
          label: "Win"
        }, {
          value: "Loss",
          label: "Loss"
        }], value: e.win_loss || "", onChange: (x) => i(ge.WIN_LOSS, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(Mi, { children: [
        a && /* @__PURE__ */ o.jsxs(le, { variant: "outline", size: "small", onClick: a, disabled: n, children: [
          s ? "Hide" : "Show",
          " Advanced"
        ] }),
        /* @__PURE__ */ o.jsx(le, { variant: "outline", size: "small", onClick: u, disabled: n || !d, children: "Reset" })
      ] })
    ] }),
    /* @__PURE__ */ o.jsx(Pi, { isVisible: s, children: /* @__PURE__ */ o.jsxs(ct, { children: [
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Market" }),
        /* @__PURE__ */ o.jsx(Pe, { options: [{
          value: "",
          label: "All Markets"
        }, {
          value: "MNQ",
          label: "MNQ"
        }, {
          value: "NQ",
          label: "NQ"
        }, {
          value: "ES",
          label: "ES"
        }, {
          value: "MES",
          label: "MES"
        }, {
          value: "YM",
          label: "YM"
        }, {
          value: "MYM",
          label: "MYM"
        }], value: e.market || "", onChange: (x) => i(ge.MARKET, x), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "R Multiple Range" }),
        /* @__PURE__ */ o.jsxs(lt, { children: [
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Min", step: "0.1", value: ((f = e.min_r_multiple) == null ? void 0 : f.toString()) || "", onChange: (x) => i(ge.MIN_R_MULTIPLE, x ? Number(x) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(dt, { children: "to" }),
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Max", step: "0.1", value: ((l = e.max_r_multiple) == null ? void 0 : l.toString()) || "", onChange: (x) => i(ge.MAX_R_MULTIPLE, x ? Number(x) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(xe, { children: [
        /* @__PURE__ */ o.jsx(be, { children: "Pattern Quality Range" }),
        /* @__PURE__ */ o.jsxs(lt, { children: [
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: ((m = e.min_pattern_quality) == null ? void 0 : m.toString()) || "", onChange: (x) => i(ge.MIN_PATTERN_QUALITY, x ? Number(x) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(dt, { children: "to" }),
          /* @__PURE__ */ o.jsx(je, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: ((b = e.max_pattern_quality) == null ? void 0 : b.toString()) || "", onChange: (x) => i(ge.MAX_PATTERN_QUALITY, x ? Number(x) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] })
    ] }) })
  ] });
}, $i = /* @__PURE__ */ c.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-13oxwmo-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), Oi = /* @__PURE__ */ c.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-13oxwmo-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  bordered: e,
  theme: r
}) => {
  var t, n;
  return e && g(["border:1px solid ", ";border-radius:", ";"], ((t = r.colors) == null ? void 0 : t.border) || "#e5e7eb", ((n = r.borderRadius) == null ? void 0 : n.sm) || "4px");
}, ({
  compact: e,
  theme: r
}) => {
  var t, n, s, a;
  return e ? g(["th,td{padding:", " ", ";}"], ((t = r.spacing) == null ? void 0 : t.xs) || "8px", ((n = r.spacing) == null ? void 0 : n.sm) || "12px") : g(["th,td{padding:", " ", ";}"], ((s = r.spacing) == null ? void 0 : s.sm) || "12px", ((a = r.spacing) == null ? void 0 : a.md) || "16px");
}), Ai = /* @__PURE__ */ c.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-13oxwmo-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), zi = /* @__PURE__ */ c.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-13oxwmo-3"
})(["background-color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}), pt = /* @__PURE__ */ c.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13oxwmo-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.background) || "#f8f9fa";
}), ({
  isSorted: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), Fi = /* @__PURE__ */ c.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13oxwmo-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), Bi = /* @__PURE__ */ c.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13oxwmo-6"
})([""]), qi = /* @__PURE__ */ c.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13oxwmo-7"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xl) || "32px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Hi = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-13oxwmo-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return `${((r = e.colors) == null ? void 0 : r.background) || "#ffffff"}80`;
}), Ui = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-13oxwmo-9"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Vi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-13oxwmo-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}), Yi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-13oxwmo-11"
})(["color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), Gi = /* @__PURE__ */ c.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-13oxwmo-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Hc = ({
  data: e,
  isLoading: r = !1,
  bordered: t = !0,
  striped: n = !0,
  hoverable: s = !0,
  compact: a = !1,
  stickyHeader: i = !1,
  height: u = "",
  onRowClick: d,
  isRowSelected: f,
  onSort: l,
  sortColumn: m = "",
  sortDirection: b = "asc",
  pagination: x = !1,
  currentPage: h = 1,
  pageSize: y = 10,
  totalRows: I = 0,
  onPageChange: S,
  onPageSizeChange: C,
  className: R = "",
  emptyMessage: A = "No trades available",
  scrollable: N = !0,
  showFilters: k = !1,
  filters: M = {},
  onFiltersChange: E,
  columnPreset: _ = "default",
  customColumns: K,
  expandableRows: W = !1,
  renderExpandedContent: L
}) => {
  const [H, re] = V(!1), te = U(() => {
    if (K)
      return K;
    switch (_) {
      case "compact":
        return wi();
      case "performance":
        return Si();
      default:
        return vi();
    }
  }, [K, _]), fe = U(() => te.filter((q) => !q.hidden), [te]), oe = U(() => Math.ceil(I / y), [I, y]), $ = U(() => {
    if (!x)
      return e;
    const q = (h - 1) * y, me = q + y;
    return I > 0 && e.length <= y ? e : e.slice(q, me);
  }, [e, x, h, y, I]), J = (q) => {
    if (!l)
      return;
    l(q, m === q && b === "asc" ? "desc" : "asc");
  }, ve = (q) => {
    q < 1 || q > oe || !S || S(q);
  };
  return /* @__PURE__ */ o.jsxs("div", { children: [
    k && E && /* @__PURE__ */ o.jsx(Di, { filters: M, onFiltersChange: E, isLoading: r, showAdvanced: H, onToggleAdvanced: () => re(!H) }),
    /* @__PURE__ */ o.jsxs("div", { style: {
      position: "relative"
    }, children: [
      r && /* @__PURE__ */ o.jsx(Hi, { children: /* @__PURE__ */ o.jsx(Ui, {}) }),
      /* @__PURE__ */ o.jsx($i, { height: u, scrollable: N, children: /* @__PURE__ */ o.jsxs(Oi, { bordered: t, striped: n, compact: a, className: R, children: [
        /* @__PURE__ */ o.jsx(Ai, { stickyHeader: i, children: /* @__PURE__ */ o.jsxs(zi, { children: [
          W && /* @__PURE__ */ o.jsx(pt, { width: "40px", align: "center" }),
          fe.map((q) => /* @__PURE__ */ o.jsxs(pt, { sortable: q.sortable, isSorted: m === q.id, align: q.align, width: q.width, onClick: () => q.sortable && J(q.id), children: [
            q.header,
            q.sortable && /* @__PURE__ */ o.jsx(Fi, { direction: m === q.id ? b : void 0 })
          ] }, q.id))
        ] }) }),
        /* @__PURE__ */ o.jsx(Bi, { children: $.length > 0 ? $.map((q, me) => /* @__PURE__ */ o.jsx(Li, { trade: q, index: me, columns: fe, isSelected: f ? f(q, me) : !1, hoverable: s, striped: n, expandable: W, onRowClick: d, expandedContent: L == null ? void 0 : L(q) }, q.trade.id || me)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: fe.length + (W ? 1 : 0), children: /* @__PURE__ */ o.jsx(qi, { children: A }) }) }) })
      ] }) }),
      x && oe > 0 && /* @__PURE__ */ o.jsxs(Vi, { children: [
        /* @__PURE__ */ o.jsxs(Yi, { children: [
          "Showing ",
          Math.min((h - 1) * y + 1, I),
          " to",
          " ",
          Math.min(h * y, I),
          " of ",
          I,
          " entries"
        ] }),
        /* @__PURE__ */ o.jsxs(Gi, { children: [
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(1), disabled: h === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(h - 1), disabled: h === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(h + 1), disabled: h === oe, children: "Next" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(oe), disabled: h === oe, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}, Wi = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Uc = ({
  title: e,
  children: r,
  isLoading: t = !1,
  hasError: n = !1,
  errorMessage: s = "An error occurred while loading data",
  showRetry: a = !0,
  onRetry: i,
  isEmpty: u = !1,
  emptyMessage: d = "No data available",
  emptyActionText: f,
  onEmptyAction: l,
  actionButton: m,
  className: b,
  ...x
}) => {
  const h = /* @__PURE__ */ o.jsx(Wi, { children: m });
  let y;
  return t ? y = /* @__PURE__ */ o.jsx(Uo, { variant: "card", text: "Loading data..." }) : n ? y = /* @__PURE__ */ o.jsx(Zr, { title: "Error", description: s, variant: "compact", actionText: a ? "Retry" : void 0, onAction: a ? i : void 0 }) : u ? y = /* @__PURE__ */ o.jsx(Zr, { title: "No Data", description: d, variant: "compact", actionText: f, onAction: l }) : y = r, /* @__PURE__ */ o.jsx(Vn, { title: e, actions: h, className: b, ...x, children: y });
}, Ki = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContainer",
  componentId: "sc-14y246p-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.spacing.lg), Qi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionHeader",
  componentId: "sc-14y246p-1"
})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:", ";padding-bottom:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border), Xi = /* @__PURE__ */ c.h2.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-14y246p-2"
})(["color:", ";font-size:", ";font-weight:600;margin:0;"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), Ji = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionActions",
  componentId: "sc-14y246p-3"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Zi = /* @__PURE__ */ c.div.withConfig({
  displayName: "SectionContent",
  componentId: "sc-14y246p-4"
})(["min-height:200px;"]), ut = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingState",
  componentId: "sc-14y246p-5"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), ea = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorState",
  componentId: "sc-14y246p-6"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";text-align:center;"], ({
  theme: e
}) => e.colors.danger), ra = ({
  name: e,
  title: r,
  children: t,
  actions: n,
  isLoading: s = !1,
  error: a = null,
  className: i,
  collapsible: u = !1,
  defaultCollapsed: d = !1
}) => {
  const [f, l] = Se.useState(d), m = () => {
    u && l(!f);
  }, b = r || e.charAt(0).toUpperCase() + e.slice(1), x = () => a ? /* @__PURE__ */ o.jsx(ea, { children: /* @__PURE__ */ o.jsxs("div", { children: [
    /* @__PURE__ */ o.jsxs("div", { children: [
      "Error loading ",
      e
    ] }),
    /* @__PURE__ */ o.jsx("div", { style: {
      fontSize: "0.9em",
      marginTop: "8px"
    }, children: a })
  ] }) }) : s ? /* @__PURE__ */ o.jsxs(ut, { children: [
    "Loading ",
    e,
    "..."
  ] }) : t || /* @__PURE__ */ o.jsxs(ut, { children: [
    "No ",
    e,
    " data available"
  ] });
  return /* @__PURE__ */ o.jsxs(Ki, { className: i, "data-section": e, children: [
    /* @__PURE__ */ o.jsxs(Qi, { children: [
      /* @__PURE__ */ o.jsxs(Xi, { onClick: m, style: {
        cursor: u ? "pointer" : "default"
      }, children: [
        b,
        u && /* @__PURE__ */ o.jsx("span", { style: {
          marginLeft: "8px",
          fontSize: "0.8em"
        }, children: f ? "▶" : "▼" })
      ] }),
      n && /* @__PURE__ */ o.jsx(Ji, { children: n })
    ] }),
    !f && /* @__PURE__ */ o.jsx(Zi, { children: x() })
  ] });
}, Vc = ra, ta = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), oa = /* @__PURE__ */ c.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), na = /* @__PURE__ */ c.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), sa = /* @__PURE__ */ c.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), Yc = ({
  header: e,
  sidebar: r,
  children: t,
  sidebarCollapsed: n = !1,
  // toggleSidebar, // Unused prop
  className: s
}) => /* @__PURE__ */ o.jsxs(ta, { sidebarCollapsed: n, className: s, children: [
  /* @__PURE__ */ o.jsx(oa, { children: e }),
  /* @__PURE__ */ o.jsx(na, { collapsed: n, children: r }),
  /* @__PURE__ */ o.jsx(sa, { children: t })
] }), ia = /* @__PURE__ */ c.div.withConfig({
  displayName: "BuilderContainer",
  componentId: "sc-5duzr2-0"
})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]), aa = /* @__PURE__ */ c.h3.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-5duzr2-1"
})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]), ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "MatrixGrid",
  componentId: "sc-5duzr2-2"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]), Ve = /* @__PURE__ */ c.div.withConfig({
  displayName: "ElementSection",
  componentId: "sc-5duzr2-3"
})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]), Oe = /* @__PURE__ */ c.h4.withConfig({
  displayName: "ElementTitle",
  componentId: "sc-5duzr2-4"
})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]), Ye = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-5duzr2-5"
})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]), la = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewContainer",
  componentId: "sc-5duzr2-6"
})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]), da = /* @__PURE__ */ c.div.withConfig({
  displayName: "PreviewText",
  componentId: "sc-5duzr2-7"
})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]), ft = /* @__PURE__ */ c.span.withConfig({
  displayName: "RequiredIndicator",
  componentId: "sc-5duzr2-8"
})(["color:#dc2626;margin-left:4px;"]), gt = /* @__PURE__ */ c.span.withConfig({
  displayName: "OptionalIndicator",
  componentId: "sc-5duzr2-9"
})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]), pa = ({
  onSetupChange: e,
  initialComponents: r
}) => {
  const [t, n] = V({
    constant: (r == null ? void 0 : r.constant) || "",
    action: (r == null ? void 0 : r.action) || "None",
    variable: (r == null ? void 0 : r.variable) || "None",
    entry: (r == null ? void 0 : r.entry) || ""
  });
  ae(() => {
    t.constant && t.entry && e(t);
  }, [t, e]);
  const s = (i, u) => {
    n((d) => ({
      ...d,
      [i]: u
    }));
  }, a = () => {
    const {
      constant: i,
      action: u,
      variable: d,
      entry: f
    } = t;
    if (!i || !f)
      return "Select required elements to see setup preview...";
    let l = i;
    return u && u !== "None" && (l += ` → ${u}`), d && d !== "None" && (l += ` → ${d}`), l += ` [${f}]`, l;
  };
  return /* @__PURE__ */ o.jsxs(ia, { children: [
    /* @__PURE__ */ o.jsx(aa, { children: "Setup Construction Matrix" }),
    /* @__PURE__ */ o.jsxs(ca, { children: [
      /* @__PURE__ */ o.jsxs(Ve, { children: [
        /* @__PURE__ */ o.jsxs(Oe, { children: [
          "Constant Element",
          /* @__PURE__ */ o.jsx(ft, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(Ye, { value: t.constant, onChange: (i) => s("constant", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Constant" }),
          Le.constant.parentArrays.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i)),
          Le.constant.fvgTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ve, { children: [
        /* @__PURE__ */ o.jsxs(Oe, { children: [
          "Action Element",
          /* @__PURE__ */ o.jsx(gt, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(Ye, { value: t.action, onChange: (i) => s("action", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Le.action.liquidityEvents.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ve, { children: [
        /* @__PURE__ */ o.jsxs(Oe, { children: [
          "Variable Element",
          /* @__PURE__ */ o.jsx(gt, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(Ye, { value: t.variable, onChange: (i) => s("variable", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Le.variable.rdTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Ve, { children: [
        /* @__PURE__ */ o.jsxs(Oe, { children: [
          "Entry Method",
          /* @__PURE__ */ o.jsx(ft, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(Ye, { value: t.entry, onChange: (i) => s("entry", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Entry Method" }),
          Le.entry.methods.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] })
    ] }),
    /* @__PURE__ */ o.jsxs(la, { children: [
      /* @__PURE__ */ o.jsx(Oe, { children: "Setup Preview" }),
      /* @__PURE__ */ o.jsx(da, { children: a() })
    ] })
  ] });
}, Gc = pa, mt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricsContainer",
  componentId: "sc-opkdti-0"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => e.spacing.md), ht = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricCard",
  componentId: "sc-opkdti-1"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.md), xt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricLabel",
  componentId: "sc-opkdti-2"
})(["color:", ";font-size:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xs), bt = /* @__PURE__ */ c.div.withConfig({
  displayName: "MetricValue",
  componentId: "sc-opkdti-3"
})(["color:", ";font-size:", ";font-weight:600;"], ({
  theme: e,
  positive: r,
  negative: t
}) => r ? e.colors.success : t ? e.colors.danger : e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), ua = ({
  metrics: e,
  isLoading: r
}) => r ? /* @__PURE__ */ o.jsx(mt, { children: Array.from({
  length: 4
}).map((t, n) => /* @__PURE__ */ o.jsxs(ht, { children: [
  /* @__PURE__ */ o.jsx(xt, { children: "Loading..." }),
  /* @__PURE__ */ o.jsx(bt, { children: "--" })
] }, n)) }) : /* @__PURE__ */ o.jsx(mt, { children: e.map((t, n) => /* @__PURE__ */ o.jsxs(ht, { children: [
  /* @__PURE__ */ o.jsx(xt, { children: t.label }),
  /* @__PURE__ */ o.jsx(bt, { positive: t.positive, negative: t.negative, children: t.value })
] }, n)) }), Wc = ua, fa = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContainer",
  componentId: "sc-tp1ymt-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg), ga = /* @__PURE__ */ c.h3.withConfig({
  displayName: "AnalysisTitle",
  componentId: "sc-tp1ymt-1"
})(["color:", ";font-size:", ";font-weight:600;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.spacing.md), ma = /* @__PURE__ */ c.div.withConfig({
  displayName: "AnalysisContent",
  componentId: "sc-tp1ymt-2"
})(["color:", ";line-height:1.6;"], ({
  theme: e
}) => e.colors.textSecondary), ha = ({
  title: e = "Trade Analysis",
  children: r,
  isLoading: t
}) => /* @__PURE__ */ o.jsxs(fa, { children: [
  /* @__PURE__ */ o.jsx(ga, { children: e }),
  /* @__PURE__ */ o.jsx(ma, { children: t ? /* @__PURE__ */ o.jsx("div", { children: "Loading analysis..." }) : r || /* @__PURE__ */ o.jsx("div", { children: "No analysis data available" }) })
] }), Kc = ha, w = {
  // F1 Racing Team Colors
  f1Red: "#e10600",
  // Ferrari Red - CRITICAL ALERTS ONLY
  f1RedDark: "#c10500",
  f1RedLight: "#ff3b36",
  f1Blue: "#0600EF",
  // Racing Blue - Information & Neutral
  f1BlueDark: "#0500CC",
  f1BlueLight: "#4169E1",
  // F1 Racing Performance Colors
  f1MercedesGreen: "#00D2BE",
  // Active, Success, Optimal
  f1MercedesGreenDark: "#00A896",
  f1MercedesGreenLight: "#00FFE5",
  f1McLarenOrange: "#FF8700",
  // Warnings, Transitions
  f1McLarenOrangeDark: "#E67600",
  f1McLarenOrangeLight: "#FFA500",
  f1RacingYellow: "#FFD320",
  // Caution, Pending
  f1RacingYellowDark: "#E6BE1D",
  f1RacingYellowLight: "#FFDC4A",
  f1Carbon: "#1A1A1A",
  // Base background
  f1Silver: "#C0C0C0",
  // Secondary text
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  orange: "#ff9800",
  orangeDark: "#f57c00",
  orangeLight: "#ffb74d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, se = {
  background: "#0f0f0f",
  surface: "#1a1a1a",
  cardBackground: "#1a1a1a",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: w.green,
  warning: w.yellow,
  error: w.red,
  info: w.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: w.f1Red,
  // Trading specific colors
  profit: w.green,
  loss: w.red,
  neutral: w.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, Qc = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: w.green,
  warning: w.yellow,
  error: w.red,
  info: w.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: w.f1Red,
  // Trading specific colors
  profit: w.green,
  loss: w.red,
  neutral: w.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, X = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, ue = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  xxxl: "2.5rem",
  // Added missing xxxl size
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, Xe = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, Je = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, Ze = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, er = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, rr = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, tr = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, or = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, nr = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, xa = /* @__PURE__ */ c.div.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-e71xhh-0"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;border-bottom:2px solid #4b5563;margin-bottom:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["padding:24px 0;margin-bottom:24px;"]);
    case "form":
      return g(["padding:16px 0;margin-bottom:16px;"]);
    default:
      return g(["padding:20px 0;margin-bottom:20px;"]);
  }
}), ba = /* @__PURE__ */ c.div.withConfig({
  displayName: "TitleSection",
  componentId: "sc-e71xhh-1"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), ya = /* @__PURE__ */ c.h1.withConfig({
  displayName: "MainTitle",
  componentId: "sc-e71xhh-2"
})(["font-weight:700;color:", ";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;", " span{color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["font-size:", ";"], ue.xxxl);
    case "analysis":
      return g(["font-size:", ";"], ue.xxl);
    case "form":
      return g(["font-size:", ";"], ue.xl);
    default:
      return g(["font-size:", ";"], ue.xxl);
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), va = /* @__PURE__ */ c.div.withConfig({
  displayName: "Subtitle",
  componentId: "sc-e71xhh-3"
})(["font-size:", ";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"], ue.sm), wa = /* @__PURE__ */ c.div.withConfig({
  displayName: "ActionsSection",
  componentId: "sc-e71xhh-4"
})(["display:flex;align-items:center;gap:", ";flex-wrap:wrap;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), Sa = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusIndicator",
  componentId: "sc-e71xhh-5"
})(["display:flex;align-items:center;gap:", ";padding:", " ", ";border-radius:", ";font-size:", ";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  $isLive: e,
  $variant: r,
  theme: t
}) => {
  var n, s, a, i, u, d, f, l, m;
  return e ? g(["background:", "20;border:1px solid ", ";color:", ";"], ((n = t.colors) == null ? void 0 : n.sessionActive) || "#00D2BE", ((s = t.colors) == null ? void 0 : s.sessionActive) || "#00D2BE", ((a = t.colors) == null ? void 0 : a.sessionActive) || "#00D2BE") : r === "active" ? g(["background:", "20;border:1px solid ", ";color:", ";"], ((i = t.colors) == null ? void 0 : i.sessionOptimal) || "#00FFE5", ((u = t.colors) == null ? void 0 : u.sessionOptimal) || "#00FFE5", ((d = t.colors) == null ? void 0 : d.sessionOptimal) || "#00FFE5") : g(["background:", "20;border:1px solid ", ";color:", ";"], ((f = t.colors) == null ? void 0 : f.textSecondary) || "#9ca3af", ((l = t.colors) == null ? void 0 : l.textSecondary) || "#9ca3af", ((m = t.colors) == null ? void 0 : m.textSecondary) || "#9ca3af");
}), Ca = /* @__PURE__ */ c.div.withConfig({
  displayName: "StatusDot",
  componentId: "sc-e71xhh-6"
})(["width:6px;height:6px;border-radius:50%;background:", ";", ""], ({
  $isLive: e,
  theme: r
}) => {
  var t, n;
  return e ? ((t = r.colors) == null ? void 0 : t.sessionActive) || "#00D2BE" : ((n = r.colors) == null ? void 0 : n.sessionOptimal) || "#00FFE5";
}, ({
  $isLive: e
}) => e && g(["animation:mercedesPulse 2s infinite;@keyframes mercedesPulse{0%,100%{opacity:1;transform:scale(1);}50%{opacity:0.7;transform:scale(1.2);}}"])), Ia = /* @__PURE__ */ c.button.withConfig({
  displayName: "RefreshButton",
  componentId: "sc-e71xhh-7"
})(["padding:", " ", ";background:transparent;color:", ";border:1px solid #4b5563;border-radius:", ";cursor:pointer;font-weight:500;font-size:", ";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:", ";border-color:", ";}&:disabled{opacity:0.6;cursor:not-allowed;}", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  $isRefreshing: e
}) => e && g(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])), ja = /* @__PURE__ */ c.div.withConfig({
  displayName: "CustomActions",
  componentId: "sc-e71xhh-8"
})(["display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), Xc = (e) => {
  const {
    title: r,
    subtitle: t,
    isLive: n = !1,
    liveText: s = "LIVE SESSION",
    statusText: a,
    onRefresh: i,
    isRefreshing: u = !1,
    actions: d,
    variant: f = "dashboard",
    className: l
  } = e, m = n ? s : a;
  return /* @__PURE__ */ o.jsxs(xa, { $variant: f, className: l, children: [
    /* @__PURE__ */ o.jsxs(ba, { children: [
      /* @__PURE__ */ o.jsx(ya, { $variant: f, children: f === "dashboard" ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
        "🏎️ ",
        r.replace("Trading", "TRADING").replace("Dashboard", "DASHBOARD")
      ] }) : r }),
      t && /* @__PURE__ */ o.jsx(va, { children: t })
    ] }),
    /* @__PURE__ */ o.jsxs(wa, { children: [
      m && /* @__PURE__ */ o.jsxs(Sa, { $isLive: n, $variant: !n && a ? "active" : void 0, children: [
        /* @__PURE__ */ o.jsx(Ca, { $isLive: n }),
        m
      ] }),
      i && /* @__PURE__ */ o.jsx(Ia, { onClick: i, disabled: u, $isRefreshing: u, children: u ? "Refreshing..." : "Refresh" }),
      d && /* @__PURE__ */ o.jsx(ja, { children: d })
    ] })
  ] });
}, mr = /* @__PURE__ */ c.div.withConfig({
  displayName: "Container",
  componentId: "sc-vuv4tf-0"
})(["display:flex;flex-direction:column;width:100%;max-width:", ";margin:0 auto;min-height:", ";", " ", " ", " ", ""], ({
  $maxWidth: e
}) => typeof e == "number" ? `${e}px` : e, ({
  $variant: e
}) => e === "dashboard" ? "100vh" : "auto", ({
  $padding: e
}) => {
  const r = {
    sm: X.sm,
    md: X.md,
    lg: X.lg,
    xl: X.xl
  };
  return g(["padding:", ";"], r[e || "lg"]);
}, ({
  $background: e,
  theme: r
}) => {
  const t = {
    default: r.colors.background,
    surface: r.colors.surface,
    elevated: r.colors.elevated
  };
  return g(["background:", ";"], t[e || "default"]);
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["gap:24px;padding-top:0;"]);
    case "form":
      return g(["gap:16px;max-width:800px;"]);
    case "analysis":
      return g(["gap:20px;max-width:1400px;"]);
    case "settings":
      return g(["gap:16px;max-width:1000px;"]);
    default:
      return g(["gap:16px;"]);
  }
}, ({
  $animated: e
}) => e && g(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])), Ta = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingContainer",
  componentId: "sc-vuv4tf-1"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]), Ea = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-vuv4tf-2"
})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Na = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-vuv4tf-3"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]), ka = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorIcon",
  componentId: "sc-vuv4tf-4"
})(["font-size:48px;opacity:0.8;"]), _a = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-vuv4tf-5"
})(["font-size:16px;font-weight:500;"]), La = /* @__PURE__ */ c.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-vuv4tf-6"
})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]), yt = () => /* @__PURE__ */ o.jsxs(Ta, { children: [
  /* @__PURE__ */ o.jsx(Ea, {}),
  /* @__PURE__ */ o.jsx("div", { children: "Loading..." })
] }), Ra = ({
  error: e,
  onRetry: r
}) => /* @__PURE__ */ o.jsxs(Na, { children: [
  /* @__PURE__ */ o.jsx(ka, { children: "⚠️" }),
  /* @__PURE__ */ o.jsx(_a, { children: e }),
  r && /* @__PURE__ */ o.jsx(La, { onClick: r, children: "Retry" })
] }), Jc = (e) => {
  const {
    children: r,
    variant: t = "dashboard",
    maxWidth: n = "100%",
    padding: s = "lg",
    isLoading: a = !1,
    error: i = null,
    loadingFallback: u,
    errorFallback: d,
    className: f,
    animated: l = !0,
    background: m = "default"
  } = e, b = {
    $variant: t,
    $maxWidth: n,
    $padding: s,
    $animated: l,
    $background: m
  };
  return i ? /* @__PURE__ */ o.jsx(mr, { ...b, className: f, children: d || /* @__PURE__ */ o.jsx(Ra, { error: i }) }) : a ? /* @__PURE__ */ o.jsx(mr, { ...b, className: f, children: u || /* @__PURE__ */ o.jsx(yt, {}) }) : /* @__PURE__ */ o.jsx(mr, { ...b, className: f, children: /* @__PURE__ */ o.jsx(ao, { fallback: u || /* @__PURE__ */ o.jsx(yt, {}), children: r }) });
}, Ma = /* @__PURE__ */ c.form.withConfig({
  displayName: "FormContainer",
  componentId: "sc-1gwzj6e-0"
})(["display:flex;flex-direction:column;gap:", ";background:", ";border-radius:", ";border:1px solid ", ";position:relative;", " ", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "quick":
      return g(["padding:", ";max-width:600px;"], X.lg);
    case "detailed":
      return g(["padding:", ";max-width:800px;"], X.xl);
    case "modal":
      return g(["padding:", ";max-width:500px;margin:0 auto;"], X.lg);
    case "inline":
      return g(["padding:", ";background:transparent;border:none;"], X.md);
    default:
      return g(["padding:", ";"], X.lg);
  }
}, ({
  $showAccent: e,
  theme: r
}) => {
  var t, n, s, a, i;
  return e && g(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,", ",", ",", " );border-radius:", " ", " 0 0;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primaryDark) || "#b91c1c", ((s = r.colors) == null ? void 0 : s.primary) || "#dc2626", ((a = r.borderRadius) == null ? void 0 : a.lg) || "8px", ((i = r.borderRadius) == null ? void 0 : i.lg) || "8px");
}, ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), Pa = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormHeader",
  componentId: "sc-1gwzj6e-1"
})(["display:flex;flex-direction:column;gap:", ";margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), Da = /* @__PURE__ */ c.h3.withConfig({
  displayName: "FormTitle",
  componentId: "sc-1gwzj6e-2"
})(["font-size:", ";font-weight:700;color:", ";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.lg) || "1.125rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), $a = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormSubtitle",
  componentId: "sc-1gwzj6e-3"
})(["font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Oa = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormContent",
  componentId: "sc-1gwzj6e-4"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), vt = /* @__PURE__ */ c.div.withConfig({
  displayName: "FormMessage",
  componentId: "sc-1gwzj6e-5"
})(["padding:", " ", ";border-radius:", ";font-size:", ";font-weight:500;display:flex;align-items:center;gap:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $type: e
}) => {
  switch (e) {
    case "error":
      return g(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);
    case "success":
      return g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);
    case "info":
      return g(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"]);
  }
}), Aa = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-1gwzj6e-6"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:", ";z-index:10;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}), za = /* @__PURE__ */ c.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1gwzj6e-7"
})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Fa = /* @__PURE__ */ c.div.withConfig({
  displayName: "AutoSaveIndicator",
  componentId: "sc-1gwzj6e-8"
})(["position:absolute;top:8px;right:8px;font-size:", ";color:", ";opacity:", ";transition:opacity 0.3s ease;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  $visible: e
}) => e ? 1 : 0), Zc = (e) => {
  const {
    children: r,
    onSubmit: t,
    title: n,
    subtitle: s,
    isSubmitting: a = !1,
    error: i = null,
    success: u = null,
    variant: d = "quick",
    showAccent: f = !0,
    className: l,
    disabled: m = !1,
    autoSave: b = !1
  } = e, x = async (h) => {
    h.preventDefault(), t && !a && !m && await t(h);
  };
  return /* @__PURE__ */ o.jsxs(Ma, { $variant: d, $showAccent: f, $disabled: m, className: l, onSubmit: x, noValidate: !0, children: [
    a && /* @__PURE__ */ o.jsx(Aa, { children: /* @__PURE__ */ o.jsx(za, {}) }),
    b && /* @__PURE__ */ o.jsx(Fa, { $visible: !a, children: "Auto-save enabled" }),
    (n || s) && /* @__PURE__ */ o.jsxs(Pa, { children: [
      n && /* @__PURE__ */ o.jsx(Da, { children: n }),
      s && /* @__PURE__ */ o.jsx($a, { children: s })
    ] }),
    i && /* @__PURE__ */ o.jsxs(vt, { $type: "error", children: [
      "⚠️ ",
      i
    ] }),
    u && /* @__PURE__ */ o.jsxs(vt, { $type: "success", children: [
      "✅ ",
      u
    ] }),
    /* @__PURE__ */ o.jsx(Oa, { children: r })
  ] });
}, Ba = /* @__PURE__ */ c.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-sq94oz-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  $size: e
}) => ({
  sm: X.xs,
  md: X.sm,
  lg: X.md
})[e || "md"]), qa = /* @__PURE__ */ c.label.withConfig({
  displayName: "Label",
  componentId: "sc-sq94oz-1"
})(["font-size:", ";font-weight:600;color:", ";display:flex;align-items:center;gap:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "trading":
      return g(["text-transform:uppercase;letter-spacing:0.025em;"]);
    case "analysis":
      return g(["font-weight:500;"]);
    default:
      return g([""]);
  }
}, ({
  $required: e,
  theme: r
}) => {
  var t;
  return e && g(["&::after{content:'*';color:", ";margin-left:2px;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}), Ha = /* @__PURE__ */ c.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-sq94oz-2"
})(["position:relative;display:flex;align-items:center;", ""], ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), Tr = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background:", ";color:", ";font-family:inherit;transition:all 0.2s ease;", " &:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"], ({
  $hasError: e,
  theme: r
}) => {
  var t;
  return e ? ((t = r.colors) == null ? void 0 : t.error) || "#f44336" : "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $size: e
}) => ({
  sm: g(["padding:", " ", ";font-size:", ";"], X.xs, X.sm, ue.sm),
  md: g(["padding:", " ", ";font-size:", ";"], X.sm, X.md, ue.md),
  lg: g(["padding:", " ", ";font-size:", ";"], X.md, X.lg, ue.lg)
})[e || "md"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), Ua = /* @__PURE__ */ c.input.withConfig({
  displayName: "Input",
  componentId: "sc-sq94oz-3"
})(["", ""], Tr), Va = /* @__PURE__ */ c.select.withConfig({
  displayName: "Select",
  componentId: "sc-sq94oz-4"
})(["", " cursor:pointer;option{background:", ";color:", ";}"], Tr, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), Ya = /* @__PURE__ */ c.textarea.withConfig({
  displayName: "TextArea",
  componentId: "sc-sq94oz-5"
})(["", " resize:vertical;min-height:80px;font-family:inherit;"], Tr), Ga = /* @__PURE__ */ c.div.withConfig({
  displayName: "PrefixContainer",
  componentId: "sc-sq94oz-6"
})(["position:absolute;left:12px;display:flex;align-items:center;color:", ";pointer-events:none;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Wa = /* @__PURE__ */ c.div.withConfig({
  displayName: "SuffixContainer",
  componentId: "sc-sq94oz-7"
})(["position:absolute;right:12px;display:flex;align-items:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Ka = /* @__PURE__ */ c.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-sq94oz-8"
})(["font-size:", ";color:", ";font-weight:500;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#f44336";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), Qa = /* @__PURE__ */ c.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-sq94oz-9"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Xa = /* @__PURE__ */ c.div.withConfig({
  displayName: "ValidationIndicator",
  componentId: "sc-sq94oz-10"
})(["position:absolute;right:8px;display:flex;align-items:center;", " ", ""], ({
  $validating: e
}) => e && g(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), ({
  $valid: e,
  $validating: r
}) => !r && g(["color:", ";&::after{content:'", "';}"], e ? "#22c55e" : "#f44336", e ? "✓" : "✗")), el = (e) => {
  const {
    label: r,
    field: t,
    type: n = "text",
    placeholder: s,
    required: a = !1,
    disabled: i = !1,
    helpText: u,
    options: d = [],
    inputProps: f = {},
    className: l,
    size: m = "md",
    variant: b = "default",
    prefix: x,
    suffix: h
  } = e, y = !!(t.error && t.touched), I = t.touched && !t.validating, S = () => {
    const C = {
      id: f.id || r.toLowerCase().replace(/\s+/g, "-"),
      value: t.value,
      onChange: t.setValue,
      onBlur: () => t.setTouched(!0),
      disabled: i,
      placeholder: s,
      $hasError: y,
      $size: m,
      ...f
    };
    switch (n) {
      case "select":
        return /* @__PURE__ */ o.jsxs(Va, { ...C, children: [
          s && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: s }),
          d.map((R) => /* @__PURE__ */ o.jsx("option", { value: R.value, children: R.label }, R.value))
        ] });
      case "textarea":
        return /* @__PURE__ */ o.jsx(Ya, { ...C });
      default:
        return /* @__PURE__ */ o.jsx(Ua, { ...C, type: n });
    }
  };
  return /* @__PURE__ */ o.jsxs(Ba, { $size: m, className: l, children: [
    /* @__PURE__ */ o.jsx(qa, { $required: a, $variant: b, htmlFor: f.id || r.toLowerCase().replace(/\s+/g, "-"), children: r }),
    /* @__PURE__ */ o.jsxs(Ha, { $hasError: y, $disabled: i, children: [
      x && /* @__PURE__ */ o.jsx(Ga, { children: x }),
      S(),
      h && /* @__PURE__ */ o.jsx(Wa, { children: h }),
      I && /* @__PURE__ */ o.jsx(Xa, { $valid: t.valid, $validating: t.validating })
    ] }),
    y && /* @__PURE__ */ o.jsxs(Ka, { children: [
      "⚠️ ",
      t.error
    ] }),
    u && !y && /* @__PURE__ */ o.jsx(Qa, { children: u })
  ] });
}, Ja = (e = !1) => {
  const [r, t] = V(e), [n, s] = V(null), [a, i] = V(!1), u = F((x) => {
    t(x), x && (s(null), i(!1));
  }, []), d = F((x) => {
    s(x), t(!1), i(!1);
  }, []), f = F(() => {
    s(null);
  }, []), l = F(() => {
    t(!1), s(null), i(!1);
  }, []), m = F(async (x) => {
    u(!0);
    try {
      const h = await x();
      return i(!0), t(!1), h;
    } catch (h) {
      const y = h instanceof Error ? h.message : "An unexpected error occurred";
      throw d(y), h;
    }
  }, [u, d]), b = F((x) => async (...h) => {
    try {
      await m(() => x(...h));
    } catch (y) {
      console.error("Operation failed:", y);
    }
  }, [m]);
  return {
    // State
    isLoading: r,
    error: n,
    isSuccess: a,
    isError: n !== null,
    // Actions
    setLoading: u,
    setError: d,
    clearError: f,
    reset: l,
    withLoading: m,
    withLoadingCallback: b
  };
};
function rl(e, r = {}) {
  const {
    fetchOnMount: t = !0,
    dependencies: n = []
  } = r, [s, a] = V({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), i = F(async (...u) => {
    a((d) => ({
      ...d,
      isLoading: !0,
      error: null
    }));
    try {
      const d = await e(...u);
      return a({
        data: d,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), d;
    } catch (d) {
      const f = d instanceof Error ? d : new Error(String(d));
      throw a((l) => ({
        ...l,
        isLoading: !1,
        error: f,
        isInitialized: !0
      })), f;
    }
  }, [e]);
  return ae(() => {
    t && i();
  }, [t, i, ...n]), {
    ...s,
    fetchData: i,
    refetch: () => i()
  };
}
function tl(e, r) {
  const [t, n] = V(e);
  return ae(() => {
    const s = setTimeout(() => {
      n(e);
    }, r);
    return () => {
      clearTimeout(s);
    };
  }, [e, r]), t;
}
function ol(e = {}) {
  const {
    componentName: r,
    logToConsole: t = !0,
    reportToMonitoring: n = !0,
    onError: s
  } = e, [a, i] = V(null), [u, d] = V(!1), f = F((b) => {
    if (i(b), d(!0), t) {
      const x = r ? `[${r}]` : "";
      console.error(`Error caught by useErrorHandler${x}:`, b);
    }
    s && s(b);
  }, [r, t, n, s]), l = F(() => {
    i(null), d(!1);
  }, []), m = F(async (b) => {
    try {
      return await b();
    } catch (x) {
      f(x);
      return;
    }
  }, [f]);
  return ae(() => () => {
    i(null), d(!1);
  }, []), {
    error: a,
    hasError: u,
    handleError: f,
    resetError: l,
    tryExecute: m
  };
}
function wt(e, r) {
  const t = () => {
    if (typeof window > "u")
      return r;
    try {
      const i = window.localStorage.getItem(e);
      return i ? JSON.parse(i) : r;
    } catch (i) {
      return console.warn(`Error reading localStorage key "${e}":`, i), r;
    }
  }, [n, s] = V(t), a = (i) => {
    try {
      const u = i instanceof Function ? i(n) : i;
      s(u), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(u));
    } catch (u) {
      console.warn(`Error setting localStorage key "${e}":`, u);
    }
  };
  return ae(() => {
    const i = (u) => {
      u.key === e && u.newValue && s(JSON.parse(u.newValue));
    };
    return window.addEventListener("storage", i), () => window.removeEventListener("storage", i);
  }, [e]), [n, a];
}
function nl(e) {
  const {
    totalItems: r,
    itemsPerPage: t = 10,
    initialPage: n = 1,
    persistKey: s
  } = e, [a, i] = s ? wt(`${s}_page`, n) : V(n), [u, d] = s ? wt(`${s}_itemsPerPage`, t) : V(t), f = U(() => Math.max(1, Math.ceil(r / u)), [r, u]), l = U(() => Math.min(Math.max(1, a), f), [a, f]);
  l !== a && i(l);
  const m = (l - 1) * u, b = Math.min(m + u - 1, r - 1), x = l > 1, h = l < f, y = U(() => {
    const N = [];
    if (f <= 5)
      for (let k = 1; k <= f; k++)
        N.push(k);
    else {
      let k = Math.max(1, l - Math.floor(2.5));
      const M = Math.min(f, k + 5 - 1);
      M === f && (k = Math.max(1, M - 5 + 1));
      for (let E = k; E <= M; E++)
        N.push(E);
    }
    return N;
  }, [l, f]), I = F(() => {
    h && i(l + 1);
  }, [h, l, i]), S = F(() => {
    x && i(l - 1);
  }, [x, l, i]), C = F((A) => {
    const N = Math.min(Math.max(1, A), f);
    i(N);
  }, [f, i]), R = F((A) => {
    d(A), i(1);
  }, [d, i]);
  return {
    currentPage: l,
    itemsPerPage: u,
    totalPages: f,
    hasPreviousPage: x,
    hasNextPage: h,
    startIndex: m,
    endIndex: b,
    pageRange: y,
    nextPage: I,
    previousPage: S,
    goToPage: C,
    setItemsPerPage: R
  };
}
const Za = (e, r = "$", t = !1) => {
  const s = Math.abs(e).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  return e > 0 ? t ? `+${r}${s}` : `${r}${s}` : e < 0 ? `-${r}${s}` : `${r}${s}`;
}, sl = (e, r = {}) => {
  const {
    currency: t = "$",
    showPositiveSign: n = !1,
    customAriaLabel: s
  } = r;
  return U(() => {
    if (e == null)
      return {
        formattedAmount: "",
        isProfit: !1,
        isLoss: !1,
        isNeutral: !1,
        isEmpty: !0,
        ariaLabel: s || "No profit/loss data available"
      };
    const a = e > 0, i = e < 0, u = e === 0, d = Za(e, t, n), f = `${a ? "Profit" : i ? "Loss" : "Breakeven"} of ${d}`;
    return {
      formattedAmount: d,
      isProfit: a,
      isLoss: i,
      isNeutral: u,
      isEmpty: !1,
      ariaLabel: s || f
    };
  }, [e, t, n, s]);
}, ec = (e) => e == null ? !0 : Array.isArray(e) ? e.length === 0 : typeof e == "object" ? Object.keys(e).length === 0 : typeof e == "string" ? e.trim().length === 0 : !1, il = (e) => {
  const {
    fetchData: r,
    initialData: t = null,
    fetchOnMount: n = !0,
    refreshInterval: s,
    isEmpty: a = ec,
    transformError: i,
    dependencies: u = []
  } = e, [d, f] = V(t), [l, m] = V(null), b = Ja(), x = U(() => d === null || a(d), [d, a]), h = F(async () => {
    try {
      const C = await b.withLoading(r);
      f(C), m(/* @__PURE__ */ new Date());
    } catch (C) {
      const R = i && C instanceof Error ? i(C) : C instanceof Error ? C.message : "Failed to fetch data";
      b.setError(R), console.error("Data fetch failed:", C);
    }
  }, [r, b, i]), y = F(async () => {
    await h();
  }, [h]), I = F(() => {
    f(t), m(null), b.reset();
  }, [t, b]), S = F((C) => {
    f(C), m(/* @__PURE__ */ new Date()), b.clearError();
  }, [b]);
  return ae(() => {
    n && h();
  }, [n, h]), ae(() => {
    u.length > 0 && l !== null && h();
  }, u), ae(() => {
    if (!s || s <= 0)
      return;
    const C = setInterval(() => {
      !b.isLoading && !b.error && h();
    }, s);
    return () => clearInterval(C);
  }, [s, b.isLoading, b.error, h]), {
    // State
    data: d,
    isLoading: b.isLoading,
    error: b.error,
    isEmpty: x,
    isSuccess: b.isSuccess,
    isError: b.isError,
    lastFetched: l,
    // Actions
    refresh: y,
    clearError: b.clearError,
    reset: I,
    setData: S
  };
}, al = (e = "en-US") => U(() => ({
  formatCurrency: (d, f = {}) => {
    const {
      currency: l = "USD",
      locale: m = e,
      minimumFractionDigits: b = 2,
      maximumFractionDigits: x = 2,
      showPositiveSign: h = !1
    } = f, I = new Intl.NumberFormat(m, {
      style: "currency",
      currency: l,
      minimumFractionDigits: b,
      maximumFractionDigits: x
    }).format(Math.abs(d));
    return d > 0 && h ? `+${I}` : d < 0 ? `-${I}` : I;
  },
  formatPercent: (d, f = {}) => {
    const {
      locale: l = e,
      minimumFractionDigits: m = 2,
      maximumFractionDigits: b = 2,
      showPositiveSign: x = !1
    } = f, h = new Intl.NumberFormat(l, {
      style: "percent",
      minimumFractionDigits: m,
      maximumFractionDigits: b
    }), y = d > 1 ? d / 100 : d, I = h.format(Math.abs(y));
    return y > 0 && x ? `+${I}` : y < 0 ? `-${I}` : I;
  },
  formatNumber: (d, f = {}) => {
    const {
      locale: l = e,
      minimumFractionDigits: m = 0,
      maximumFractionDigits: b = 2,
      useGrouping: x = !0
    } = f;
    return new Intl.NumberFormat(l, {
      minimumFractionDigits: m,
      maximumFractionDigits: b,
      useGrouping: x
    }).format(d);
  },
  formatDate: (d, f = "medium") => {
    const l = typeof d == "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat(e, {
      dateStyle: f
    }).format(l);
  },
  formatTime: (d, f = "short") => {
    const l = typeof d == "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat(e, {
      timeStyle: f
    }).format(l);
  },
  formatRelativeTime: (d) => {
    const f = typeof d == "string" ? new Date(d) : d, m = Math.floor(((/* @__PURE__ */ new Date()).getTime() - f.getTime()) / 1e3);
    if (typeof Intl.RelativeTimeFormat < "u") {
      const y = new Intl.RelativeTimeFormat(e, {
        numeric: "auto"
      }), I = [{
        unit: "year",
        seconds: 31536e3
      }, {
        unit: "month",
        seconds: 2592e3
      }, {
        unit: "day",
        seconds: 86400
      }, {
        unit: "hour",
        seconds: 3600
      }, {
        unit: "minute",
        seconds: 60
      }, {
        unit: "second",
        seconds: 1
      }];
      for (const S of I) {
        const C = Math.floor(Math.abs(m) / S.seconds);
        if (C >= 1)
          return y.format(m > 0 ? -C : C, S.unit);
      }
      return y.format(0, "second");
    }
    const b = Math.abs(m), x = m < 0;
    if (b < 60)
      return x ? "in a few seconds" : "a few seconds ago";
    if (b < 3600) {
      const y = Math.floor(b / 60);
      return x ? `in ${y} minute${y > 1 ? "s" : ""}` : `${y} minute${y > 1 ? "s" : ""} ago`;
    }
    if (b < 86400) {
      const y = Math.floor(b / 3600);
      return x ? `in ${y} hour${y > 1 ? "s" : ""}` : `${y} hour${y > 1 ? "s" : ""} ago`;
    }
    const h = Math.floor(b / 86400);
    return x ? `in ${h} day${h > 1 ? "s" : ""}` : `${h} day${h > 1 ? "s" : ""} ago`;
  }
}), [e]), rc = {
  small: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xxs) || "2px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }),
  medium: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }),
  large: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.lg) || "18px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
  })
}, tc = {
  profit: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.profit) || ((t = e.colors) == null ? void 0 : t.success) || "#4caf50";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}15` : "rgba(76, 175, 80, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}30` : "rgba(76, 175, 80, 0.2)";
  }),
  loss: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.loss) || ((t = e.colors) == null ? void 0 : t.error) || "#f44336";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}15` : "rgba(244, 67, 54, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}30` : "rgba(244, 67, 54, 0.2)";
  }),
  neutral: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.neutral) || ((t = e.colors) == null ? void 0 : t.textSecondary) || "#757575";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}15` : "rgba(117, 117, 117, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}30` : "rgba(117, 117, 117, 0.2)";
  }),
  default: g(["color:", ";background-color:transparent;border:1px solid transparent;"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
  })
}, cl = /* @__PURE__ */ g(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:", ";font-family:", ";transition:", ";border-radius:", ";&:hover{transform:translateY(-1px);box-shadow:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontFamilies) == null ? void 0 : r.mono) || "monospace";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.shadows) == null ? void 0 : r.sm) || "0 2px 4px rgba(0, 0, 0, 0.1)";
}), ll = /* @__PURE__ */ g(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]), dl = (e) => rc[e], pl = (e, r, t) => e ? "profit" : r ? "loss" : t ? "neutral" : "default", ul = (e) => tc[e], Er = {
  name: "mercedes-green",
  colors: {
    // Primary colors - Mercedes Green for active/positive states
    primary: w.f1MercedesGreen,
    primaryDark: w.f1MercedesGreenDark,
    primaryLight: w.f1MercedesGreenLight,
    // Secondary colors - Racing Blue for information
    secondary: w.f1Blue,
    secondaryDark: w.f1BlueDark,
    secondaryLight: w.f1BlueLight,
    // Accent colors - McLaren Orange for transitions
    accent: w.f1McLarenOrange,
    accentDark: w.f1McLarenOrangeDark,
    accentLight: w.f1McLarenOrangeLight,
    // F1 Racing Status Colors
    success: w.f1MercedesGreen,
    // Green flag - optimal performance
    warning: w.f1McLarenOrange,
    // Orange flag - caution/transitions
    error: w.f1Red,
    // Red flag - critical alerts only
    danger: w.f1Red,
    // Ferrari red for genuine danger
    info: w.f1Blue,
    // Racing blue for information
    // Neutral colors
    background: se.background,
    surface: se.surface,
    elevated: w.gray700,
    // Added elevated color for F1 theme
    cardBackground: se.surface,
    border: se.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: se.textPrimary,
    textSecondary: se.textSecondary,
    textDisabled: se.textDisabled,
    textInverse: se.textInverse,
    // Chart colors
    chartGrid: se.chartGrid,
    chartLine: se.chartLine,
    chartAxis: w.gray400,
    chartTooltip: se.tooltipBackground,
    // F1 Racing Trading Colors
    profit: w.f1MercedesGreen,
    // Green flag for profitable trades
    loss: w.f1Red,
    // Red flag for losses only
    neutral: w.f1Silver,
    // Silver for neutral data
    // F1 Racing Tab Colors
    tabActive: w.f1MercedesGreen,
    // Active tabs use Mercedes green
    tabInactive: w.gray600,
    // Component specific colors
    tooltipBackground: se.tooltipBackground,
    modalBackground: se.modalBackground,
    sidebarBackground: w.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)",
    // F1 Racing Session States
    sessionActive: w.f1MercedesGreen,
    // Active sessions - green flag
    sessionOptimal: w.f1MercedesGreenLight,
    // Optimal windows - bright green
    sessionCaution: w.f1RacingYellow,
    // Caution periods - yellow flag
    sessionTransition: w.f1McLarenOrange,
    // Transition periods - orange
    sessionInactive: w.gray600,
    // Inactive sessions - neutral
    // F1 Racing Performance States
    performanceExcellent: w.f1MercedesGreen,
    performanceGood: w.f1Blue,
    performanceAverage: w.f1Silver,
    performancePoor: w.f1McLarenOrange,
    performanceAvoid: w.f1Red
  },
  spacing: X,
  breakpoints: er,
  fontSizes: ue,
  fontWeights: Xe,
  lineHeights: Je,
  fontFamilies: Ze,
  borderRadius: rr,
  shadows: tr,
  transitions: or,
  zIndex: nr
}, oc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  f1Theme: Er
}, Symbol.toStringTag, { value: "Module" })), yr = {
  name: "f1-official",
  colors: {
    // F1 Brand Colors - Official Red Primary
    primary: "#e10600",
    primaryDark: "#b30500",
    primaryLight: "#ff1e1e",
    // F1 Secondary Colors - Deep Navy
    secondary: "#15151e",
    secondaryDark: "#0f0f17",
    secondaryLight: "#1e1e2e",
    // F1 Accent Colors - Championship Gold
    accent: "#ffd700",
    accentDark: "#e6be1d",
    accentLight: "#ffdc4a",
    // F1 Status Colors - Timing Screen Colors
    success: "#00ff41",
    // F1 timing green (sector improvements)
    warning: "#ffd700",
    // F1 yellow flags
    error: "#ff1e1e",
    // F1 timing red (sector losses)
    danger: "#ff1e1e",
    // F1 danger red (same as error for F1 theme)
    info: "#00b4d8",
    // F1 information blue
    // Background Colors - F1 App Style
    background: "#15151e",
    surface: "#1e1e2e",
    cardBackground: "#2a2a3a",
    elevated: "#353545",
    // Border Colors
    border: "#3a3a4a",
    divider: "#4a4a5a",
    // Text Colors - High Contrast F1 Style
    textPrimary: "#ffffff",
    textSecondary: "#b8b8c8",
    textDisabled: "#8b8b9b",
    textInverse: "#15151e",
    // Chart Colors
    chartGrid: "rgba(255, 255, 255, 0.1)",
    chartLine: "#e10600",
    chartAxis: "#b8b8c8",
    chartTooltip: "rgba(42, 42, 58, 0.9)",
    // Trading Colors
    profit: "#00ff41",
    // F1 timing green
    loss: "#ff1e1e",
    // F1 timing red
    neutral: "#b8b8c8",
    // Tab Colors
    tabActive: "#e10600",
    tabInactive: "#8b8b9b",
    // Component Colors
    tooltipBackground: "rgba(42, 42, 58, 0.9)",
    modalBackground: "rgba(21, 21, 30, 0.8)",
    sidebarBackground: "#1e1e2e",
    headerBackground: "rgba(21, 21, 30, 0.9)",
    // F1 Session States
    sessionActive: "#e10600",
    // F1 red for active sessions
    sessionOptimal: "#ffd700",
    // Gold for optimal windows
    sessionCaution: "#ff8700",
    // Orange for caution periods
    sessionTransition: "#00b4d8",
    // Blue for transitions
    sessionInactive: "#8b8b9b",
    // Muted for inactive
    // F1 Performance States
    performanceExcellent: "#00ff41",
    // Timing green
    performanceGood: "#ffd700",
    // Championship gold
    performanceAverage: "#ff8700",
    // Warning orange
    performancePoor: "#ff1e1e",
    // Timing red
    performanceAvoid: "#8b8b9b"
    // Muted gray
  },
  spacing: X,
  breakpoints: er,
  fontSizes: ue,
  fontWeights: Xe,
  lineHeights: Je,
  fontFamilies: Ze,
  borderRadius: rr,
  shadows: tr,
  transitions: or,
  zIndex: nr
}, nc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: yr,
  f1OfficialTheme: yr
}, Symbol.toStringTag, { value: "Module" })), sc = {
  name: "f1-official",
  colors: {
    // F1 Official Primary Colors
    primary: w.f1Red,
    // Official F1 red
    primaryDark: w.f1RedDark,
    primaryLight: w.f1RedLight,
    // F1 Racing Blue for information
    secondary: w.f1Blue,
    secondaryDark: w.f1BlueDark,
    secondaryLight: w.f1BlueLight,
    // F1 Yellow accent colors
    accent: "#FFD700",
    // F1 yellow flags
    accentDark: "#E6C200",
    accentLight: "#FFF700",
    // F1 Racing Status Colors (like live timing)
    success: "#00FF41",
    // Bright green like F1 timing gains
    warning: "#FFD700",
    // F1 yellow flags
    error: "#FF1E1E",
    // Bright red like F1 timing losses
    danger: "#FF1E1E",
    // F1 red flags
    info: "#00B4D8",
    // F1 blue information
    // F1 Official Background Colors (deep charcoal like driver tracker)
    background: "#15151E",
    // Deep F1 black
    surface: "#1E1E2E",
    // Slightly lighter panels
    elevated: "#2A2A3A",
    // Card backgrounds
    cardBackground: "#2A2A3A",
    // F1 data cards
    border: "#3A3A4A",
    // Subtle borders
    divider: "rgba(255, 255, 255, 0.1)",
    // F1 Official Text Colors
    textPrimary: "#FFFFFF",
    // Primary white text
    textSecondary: "#B8B8C8",
    // Secondary gray text
    textDisabled: "#8B8B9B",
    // Muted text
    textInverse: "#15151E",
    // Inverse text for light backgrounds
    // F1 Official Chart Colors (like live timing displays)
    chartGrid: "rgba(255, 255, 255, 0.1)",
    chartLine: w.f1Red,
    chartAxis: "#B8B8C8",
    chartTooltip: "rgba(21, 21, 30, 0.95)",
    // F1 Racing Trading Colors (like timing screens)
    profit: "#00FF41",
    // Bright green like F1 timing gains
    loss: "#FF1E1E",
    // Bright red like F1 timing losses
    neutral: "#FFD700",
    // F1 yellow for neutral
    // F1 Official Tab Colors
    tabActive: w.f1Red,
    // F1 red for active tabs
    tabInactive: "#8B8B9B",
    // Muted gray for inactive
    // F1 Official Component Colors
    tooltipBackground: "rgba(21, 21, 30, 0.95)",
    modalBackground: "rgba(21, 21, 30, 0.9)",
    sidebarBackground: "#1A1A24",
    // F1 sidebar background
    headerBackground: "rgba(21, 21, 30, 0.95)",
    // F1 Official Session States (like race status)
    sessionActive: "#00FF41",
    // Bright green like F1 timing
    sessionOptimal: "#9D4EDD",
    // Purple for fastest like F1
    sessionCaution: "#FFD700",
    // F1 yellow flags
    sessionTransition: "#00B4D8",
    // F1 blue for transitions
    sessionInactive: "#8B8B9B",
    // Muted gray
    // F1 Official Performance States (like driver performance)
    performanceExcellent: "#00FF41",
    // Bright green like fastest lap
    performanceGood: "#00B4D8",
    // F1 blue for good performance
    performanceAverage: "#FFD700",
    // F1 yellow for average
    performancePoor: "#FF8700",
    // Orange for poor performance
    performanceAvoid: "#FF1E1E"
    // Bright red for avoid
  },
  spacing: X,
  breakpoints: er,
  fontSizes: ue,
  fontWeights: Xe,
  lineHeights: Je,
  fontFamilies: Ze,
  borderRadius: rr,
  shadows: tr,
  transitions: or,
  zIndex: nr
}, fl = sc, _t = {
  name: "mercedes-dark",
  colors: {
    // Mercedes-inspired primary colors (Silver/Teal accents)
    primary: w.f1Silver,
    primaryDark: w.gray500,
    primaryLight: w.gray300,
    // Secondary colors - Subtle Mercedes teal
    secondary: w.f1MercedesGreenDark,
    secondaryDark: "#006B5D",
    secondaryLight: w.f1MercedesGreen,
    // Accent colors - Mercedes silver
    accent: w.f1Silver,
    accentDark: w.gray500,
    accentLight: w.gray300,
    // Mercedes-themed status colors
    success: w.f1MercedesGreen,
    // Mercedes green for success
    warning: w.f1Silver,
    // Silver for warnings
    error: w.red,
    // Keep red for errors
    danger: w.red,
    // Keep red for danger
    info: w.f1MercedesGreenDark,
    // Dark teal for info
    // Neutral colors
    background: w.gray900,
    // Slightly different from F1 theme
    surface: w.gray800,
    elevated: w.gray700,
    // Added elevated color for dark theme
    cardBackground: w.gray800,
    border: w.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors - Improved contrast for dark theme
    textPrimary: w.white,
    // #ffffff - High contrast
    textSecondary: w.gray200,
    // #e5e7eb - Better contrast than gray300
    textDisabled: w.gray400,
    // #9ca3af - More visible than gray500
    textInverse: w.gray900,
    // Chart colors
    chartGrid: se.chartGrid,
    chartLine: w.f1Blue,
    // Using blue instead of red
    chartAxis: w.gray400,
    chartTooltip: se.tooltipBackground,
    // Mercedes-themed trading colors
    profit: w.f1MercedesGreen,
    // Mercedes green for profits
    loss: w.red,
    // Keep red for losses
    neutral: w.f1Silver,
    // Mercedes silver for neutral
    // Mercedes tab colors
    tabActive: w.f1Silver,
    // Silver for active tabs
    tabInactive: w.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: w.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)",
    // Mercedes-themed session states
    sessionActive: w.f1MercedesGreen,
    // Mercedes green for active
    sessionOptimal: w.f1MercedesGreenLight,
    // Bright Mercedes green
    sessionCaution: w.f1Silver,
    // Silver for caution
    sessionTransition: w.gray300,
    // Light gray for transitions
    sessionInactive: w.gray600,
    // Gray for inactive
    // Mercedes performance states
    performanceExcellent: w.f1MercedesGreen,
    performanceGood: w.f1Silver,
    performanceAverage: w.gray400,
    performancePoor: w.gray500,
    performanceAvoid: w.red
  },
  spacing: X,
  breakpoints: er,
  fontSizes: ue,
  fontWeights: Xe,
  lineHeights: Je,
  fontFamilies: Ze,
  borderRadius: rr,
  shadows: tr,
  transitions: or,
  zIndex: nr
}, ic = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  darkTheme: _t
}, Symbol.toStringTag, { value: "Module" })), ac = /* @__PURE__ */ lo(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), cc = ac, lc = {
  "mercedes-green": Er,
  "f1-official": yr,
  dark: _t
}, Nr = Er, dc = (e) => e === "f1" || e === "formula1" || e === "formula-1" ? "mercedes-green" : e === "light" ? "f1-official" : e, hr = (e) => {
  const r = dc(e);
  return lc[r] || Nr;
}, Lt = Ct({
  theme: Nr,
  setTheme: () => {
  }
}), gl = () => It(Lt), ml = ({
  initialTheme: e = Nr,
  persistTheme: r = !0,
  storageKey: t = "adhd-dashboard-theme",
  children: n
}) => {
  const [s, a] = V(() => {
    if (r && typeof window < "u") {
      const f = window.localStorage.getItem(t);
      if (f)
        try {
          const l = hr(f);
          return l || JSON.parse(f);
        } catch (l) {
          console.error("Failed to parse stored theme:", l);
        }
    }
    return typeof e == "string" ? hr(e) : e;
  });
  ae(() => {
    typeof document < "u" && document.documentElement.setAttribute("data-theme", s.name);
  }, [s.name]);
  const i = (d) => {
    const f = typeof d == "string" ? hr(d) : d;
    a(f), r && typeof window < "u" && window.localStorage.setItem(t, f.name || JSON.stringify(f));
  }, u = ({
    children: d
  }) => /* @__PURE__ */ o.jsxs(po, { theme: s, children: [
    /* @__PURE__ */ o.jsx(cc, {}),
    d
  ] });
  return /* @__PURE__ */ o.jsx(Lt.Provider, { value: {
    theme: s,
    setTheme: i
  }, children: /* @__PURE__ */ o.jsx(u, { children: n }) });
};
function pc(e) {
  const r = [];
  return r.push(`[data-theme="${e.name}"] {`), Object.entries(e.colors).forEach(([t, n]) => {
    r.push(`  --color-${fc(t)}: ${n};`);
  }), Object.entries(e.spacing).forEach(([t, n]) => {
    r.push(`  --spacing-${t}: ${n};`);
  }), Object.entries(e.fontSizes).forEach(([t, n]) => {
    r.push(`  --font-size-${t}: ${n};`);
  }), Object.entries(e.fontWeights).forEach(([t, n]) => {
    r.push(`  --font-weight-${t}: ${n};`);
  }), Object.entries(e.fontFamilies).forEach(([t, n]) => {
    r.push(`  --font-family-${t}: ${n};`);
  }), Object.entries(e.borderRadius).forEach(([t, n]) => {
    r.push(`  --border-radius-${t}: ${n};`);
  }), Object.entries(e.shadows).forEach(([t, n]) => {
    r.push(`  --shadow-${t}: ${n};`);
  }), Object.entries(e.transitions).forEach(([t, n]) => {
    r.push(`  --transition-${t}: ${n};`);
  }), Object.entries(e.zIndex).forEach(([t, n]) => {
    r.push(`  --z-index-${t}: ${n};`);
  }), r.push("}"), r.join(`
`);
}
function uc(e) {
  const r = [];
  return r.push(`[data-theme="${e.name}"] {`), r.push("  /* Component Semantic Variables */"), r.push("  --primary-color: var(--color-primary);"), r.push("  --secondary-color: var(--color-secondary);"), r.push("  --accent-color: var(--color-accent);"), r.push("  --success-color: var(--color-success);"), r.push("  --warning-color: var(--color-warning);"), r.push("  --error-color: var(--color-error);"), r.push("  --info-color: var(--color-info);"), r.push("  --bg-primary: var(--color-background);"), r.push("  --bg-secondary: var(--color-surface);"), r.push("  --bg-card: var(--color-card-background);"), r.push("  --bg-elevated: var(--color-elevated);"), r.push("  --text-primary: var(--color-text-primary);"), r.push("  --text-secondary: var(--color-text-secondary);"), r.push("  --text-disabled: var(--color-text-disabled);"), r.push("  --text-inverse: var(--color-text-inverse);"), r.push("  --border-primary: var(--color-border);"), r.push("  --border-secondary: var(--color-divider);"), r.push("  --session-card-bg: var(--bg-card);"), r.push("  --session-card-border: var(--border-primary);"), r.push("  --session-card-accent: var(--primary-color);"), r.push("  --session-active: var(--color-session-active);"), r.push("  --session-optimal: var(--color-session-optimal);"), r.push("  --session-caution: var(--color-session-caution);"), r.push("  --session-transition: var(--color-session-transition);"), r.push("  --session-inactive: var(--color-session-inactive);"), r.push("}"), r.join(`
`);
}
function fc(e) {
  return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, "$1-$2").toLowerCase();
}
function hl(e) {
  const r = [];
  return r.push(`/**
 * Generated Theme CSS Variables
 * 
 * This file is auto-generated from theme definitions.
 * Do not edit manually - changes will be overwritten.
 */`), Object.values(e).forEach((t) => {
    r.push(""), r.push(`/* ${t.name} Theme */`), r.push(pc(t)), r.push(""), r.push(uc(t));
  }), r.join(`
`);
}
const xl = {
  "mercedes-green": () => Promise.resolve().then(() => oc).then((e) => e.f1Theme),
  "f1-official": () => Promise.resolve().then(() => nc).then((e) => e.f1OfficialTheme),
  dark: () => Promise.resolve().then(() => ic).then((e) => e.darkTheme)
};
function bl(e, r, t = "StoreContext") {
  const n = Ct(void 0);
  n.displayName = t;
  const s = ({
    children: f,
    initialState: l
  }) => {
    const [m, b] = co(e, l || r), x = U(() => ({
      state: m,
      dispatch: b
    }), [m]);
    return /* @__PURE__ */ o.jsx(n.Provider, { value: x, children: f });
  };
  function a() {
    const f = It(n);
    if (f === void 0)
      throw new Error(`use${t} must be used within a ${t}Provider`);
    return f;
  }
  function i(f) {
    const {
      state: l
    } = a();
    return f(l);
  }
  function u(f) {
    const {
      dispatch: l
    } = a();
    return U(() => (...m) => {
      l(f(...m));
    }, [l, f]);
  }
  function d(f) {
    const {
      dispatch: l
    } = a();
    return U(() => {
      const m = {};
      for (const b in f)
        m[b] = (...x) => {
          l(f[b](...x));
        };
      return m;
    }, [l, f]);
  }
  return {
    Context: n,
    Provider: s,
    useStore: a,
    useSelector: i,
    useAction: u,
    useActions: d
  };
}
function yl(...e) {
  const r = e.pop(), t = e;
  let n = null, s = null;
  return (a) => {
    const i = t.map((u) => u(a));
    return (n === null || i.length !== n.length || i.some((u, d) => u !== n[d])) && (s = r(...i), n = i), s;
  };
}
function vl(e, r) {
  const {
    key: t,
    initialState: n,
    version: s = 1,
    migrate: a,
    serialize: i = JSON.stringify,
    deserialize: u = JSON.parse,
    filter: d = (S) => S,
    merge: f = (S, C) => ({
      ...C,
      ...S
    }),
    debug: l = !1
  } = r, m = () => {
    try {
      const S = localStorage.getItem(t);
      if (S === null)
        return null;
      const {
        state: C,
        version: R
      } = u(S);
      return R !== s && a ? (l && console.log(`Migrating state from version ${R} to ${s}`), a(C, R)) : C;
    } catch (S) {
      return l && console.error("Error loading state from local storage:", S), null;
    }
  }, b = (S) => {
    try {
      const C = d(S), R = i({
        state: C,
        version: s
      });
      localStorage.setItem(t, R);
    } catch (C) {
      l && console.error("Error saving state to local storage:", C);
    }
  }, x = () => {
    try {
      localStorage.removeItem(t);
    } catch (S) {
      l && console.error("Error clearing state from local storage:", S);
    }
  }, h = m(), y = h ? f(h, n) : n;
  return l && h && (console.log("Loaded persisted state:", h), console.log("Merged initial state:", y)), {
    reducer: (S, C) => {
      const R = e(S, C);
      return b(R), R;
    },
    initialState: y,
    clear: x
  };
}
function wl(e, r = "$") {
  return `${r}${e.toFixed(2)}`;
}
function Sl(e, r = 1) {
  return `${(e * 100).toFixed(r)}%`;
}
function Cl(e, r = "short") {
  const t = typeof e == "string" ? new Date(e) : e;
  switch (r) {
    case "medium":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function Il(e, r = 50) {
  return e.length <= r ? e : `${e.substring(0, r - 3)}...`;
}
function jl() {
  return Math.random().toString(36).substring(2, 9);
}
function Tl(e, r) {
  let t = null;
  return function(...n) {
    const s = () => {
      t = null, e(...n);
    };
    t && clearTimeout(t), t = setTimeout(s, r);
  };
}
function El(e, r) {
  let t = !1;
  return function(...n) {
    t || (e(...n), t = !0, setTimeout(() => {
      t = !1;
    }, r));
  };
}
function Nl(e = {}) {
  console.log("Monitoring service initialized", e);
}
function kl(e, r) {
  console.error("Error captured by monitoring service:", e, r);
}
function _l(e) {
  console.log("User set for monitoring service:", e);
}
function Ll(e, r) {
  const t = performance.now();
  return {
    name: e,
    startTime: t,
    finish: () => {
      const s = performance.now() - t;
      console.log(`Transaction "${e}" finished in ${s.toFixed(2)}ms`, r);
    }
  };
}
const G = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  R_MULTIPLE: "r_multiple",
  DATE: "date",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  ACHIEVED_PL: "achieved_pl",
  PATTERN_QUALITY_RATING: "pattern_quality_rating"
};
class gc {
  constructor() {
    we(this, "dbName", "adhd-trading-dashboard");
    we(this, "version", 2);
    // Increment version for schema changes
    we(this, "db", null);
    // Store names for different tables
    we(this, "stores", {
      trades: "trades",
      fvg_details: "trade_fvg_details",
      setups: "trade_setups",
      analysis: "trade_analysis",
      sessions: "trading_sessions"
    });
  }
  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  async initDB() {
    return this.db ? this.db : new Promise((r, t) => {
      const n = indexedDB.open(this.dbName, this.version);
      n.onupgradeneeded = (s) => {
        var i;
        const a = s.target.result;
        if (!a.objectStoreNames.contains(this.stores.trades)) {
          const u = a.createObjectStore(this.stores.trades, {
            keyPath: "id",
            autoIncrement: !0
          });
          u.createIndex(G.DATE, G.DATE, {
            unique: !1
          }), u.createIndex(G.MODEL_TYPE, G.MODEL_TYPE, {
            unique: !1
          }), u.createIndex(G.SESSION, G.SESSION, {
            unique: !1
          }), u.createIndex(G.WIN_LOSS, G.WIN_LOSS, {
            unique: !1
          }), u.createIndex(G.R_MULTIPLE, G.R_MULTIPLE, {
            unique: !1
          });
        }
        if (a.objectStoreNames.contains(this.stores.fvg_details) || a.createObjectStore(this.stores.fvg_details, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.setups) || a.createObjectStore(this.stores.setups, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), a.objectStoreNames.contains(this.stores.analysis) || a.createObjectStore(this.stores.analysis, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), !a.objectStoreNames.contains(this.stores.sessions)) {
          a.createObjectStore(this.stores.sessions, {
            keyPath: "id",
            autoIncrement: !0
          }).createIndex("name", "name", {
            unique: !0
          });
          const d = [{
            name: "Pre-Market",
            start_time: "04:00:00",
            end_time: "09:30:00",
            description: "Pre-market trading hours"
          }, {
            name: "NY Open",
            start_time: "09:30:00",
            end_time: "10:30:00",
            description: "New York opening hour"
          }, {
            name: "10:50-11:10",
            start_time: "10:50:00",
            end_time: "11:10:00",
            description: "Mid-morning macro window"
          }, {
            name: "11:50-12:10",
            start_time: "11:50:00",
            end_time: "12:10:00",
            description: "Pre-lunch macro window"
          }, {
            name: "Lunch Macro",
            start_time: "12:00:00",
            end_time: "13:30:00",
            description: "Lunch time trading"
          }, {
            name: "13:50-14:10",
            start_time: "13:50:00",
            end_time: "14:10:00",
            description: "Post-lunch macro window"
          }, {
            name: "14:50-15:10",
            start_time: "14:50:00",
            end_time: "15:10:00",
            description: "Pre-close macro window"
          }, {
            name: "15:15-15:45",
            start_time: "15:15:00",
            end_time: "15:45:00",
            description: "Late afternoon window"
          }, {
            name: "MOC",
            start_time: "15:45:00",
            end_time: "16:00:00",
            description: "Market on close"
          }, {
            name: "Post MOC",
            start_time: "16:00:00",
            end_time: "20:00:00",
            description: "After hours trading"
          }];
          (i = n.transaction) == null || i.addEventListener("complete", () => {
            const l = a.transaction([this.stores.sessions], "readwrite").objectStore(this.stores.sessions);
            d.forEach((m) => l.add(m));
          });
        }
      }, n.onsuccess = (s) => {
        this.db = s.target.result, r(this.db);
      }, n.onerror = (s) => {
        console.error("Error opening IndexedDB:", s), t(new Error("Failed to open IndexedDB"));
      };
    });
  }
  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (f) => {
          console.error("Transaction error:", f), s(new Error("Failed to save trade with details"));
        };
        const i = a.objectStore(this.stores.trades), u = {
          ...r.trade,
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, d = i.add(u);
        d.onsuccess = () => {
          const f = d.result, l = [];
          if (r.fvg_details) {
            const m = a.objectStore(this.stores.fvg_details), b = {
              ...r.fvg_details,
              trade_id: f
            };
            l.push(new Promise((x, h) => {
              const y = m.add(b);
              y.onsuccess = () => x(), y.onerror = () => h(new Error("Failed to save FVG details"));
            }));
          }
          if (r.setup) {
            const m = a.objectStore(this.stores.setups), b = {
              ...r.setup,
              trade_id: f
            };
            l.push(new Promise((x, h) => {
              const y = m.add(b);
              y.onsuccess = () => x(), y.onerror = () => h(new Error("Failed to save setup data"));
            }));
          }
          if (r.analysis) {
            const m = a.objectStore(this.stores.analysis), b = {
              ...r.analysis,
              trade_id: f
            };
            l.push(new Promise((x, h) => {
              const y = m.add(b);
              y.onsuccess = () => x(), y.onerror = () => h(new Error("Failed to save analysis data"));
            }));
          }
          a.oncomplete = () => {
            n(f);
          };
        }, d.onerror = (f) => {
          console.error("Error saving trade:", f), s(new Error("Failed to save trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in saveTradeWithDetails:", t), new Error("Failed to save trade with details");
    }
  }
  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), u = a.objectStore(this.stores.trades).get(r);
        u.onsuccess = () => {
          const d = u.result;
          if (!d) {
            n(null);
            return;
          }
          const f = {
            trade: d
          }, b = a.objectStore(this.stores.fvg_details).index("trade_id").get(r);
          b.onsuccess = () => {
            b.result && (f.fvg_details = b.result);
            const y = a.objectStore(this.stores.setups).index("trade_id").get(r);
            y.onsuccess = () => {
              y.result && (f.setup = y.result);
              const C = a.objectStore(this.stores.analysis).index("trade_id").get(r);
              C.onsuccess = () => {
                C.result && (f.analysis = C.result), n(f);
              }, C.onerror = (R) => {
                console.error("Error getting analysis data:", R), n(f);
              };
            }, y.onerror = (I) => {
              console.error("Error getting setup data:", I), n(f);
            };
          }, b.onerror = (x) => {
            console.error("Error getting FVG details:", x), n(f);
          };
        }, u.onerror = (d) => {
          console.error("Error getting trade:", d), s(new Error("Failed to get trade"));
        };
      });
    } catch (t) {
      return console.error("Error in getTradeById:", t), null;
    }
  }
  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const r = await this.initDB();
      return new Promise((t, n) => {
        const i = r.transaction([this.stores.trades], "readonly").objectStore(this.stores.trades).getAll();
        i.onsuccess = () => {
          const u = i.result;
          if (u.length === 0) {
            t({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: "all",
              startDate: "",
              endDate: ""
            });
            return;
          }
          const d = u.length, f = u.filter(($) => $[G.WIN_LOSS] === "Win").length, l = u.filter(($) => $[G.WIN_LOSS] === "Loss").length, m = d > 0 ? f / d * 100 : 0, b = u.filter(($) => $.achieved_pl !== void 0).map(($) => $.achieved_pl), x = b.reduce(($, J) => $ + J, 0), h = b.filter(($) => $ > 0), y = b.filter(($) => $ < 0), I = h.length > 0 ? h.reduce(($, J) => $ + J, 0) / h.length : 0, S = y.length > 0 ? Math.abs(y.reduce(($, J) => $ + J, 0) / y.length) : 0, C = h.length > 0 ? Math.max(...h) : 0, R = y.length > 0 ? Math.abs(Math.min(...y)) : 0, A = h.reduce(($, J) => $ + J, 0), N = Math.abs(y.reduce(($, J) => $ + J, 0)), k = N > 0 ? A / N : 0, M = u.filter(($) => $[G.R_MULTIPLE] !== void 0).map(($) => $[G.R_MULTIPLE]), E = M.length > 0 ? M.reduce(($, J) => $ + J, 0) / M.length : 0, _ = E * (m / 100);
          let K = 0, W = 0, L = 0;
          for (const $ of u)
            if ($.achieved_pl !== void 0) {
              K += $.achieved_pl, K > W && (W = K);
              const J = W - K;
              J > L && (L = J);
            }
          const H = W > 0 ? L / W * 100 : 0, re = M.length > 0 ? Math.sqrt(M.length) * E / Math.sqrt(M.reduce(($, J) => $ + Math.pow(J - E, 2), 0) / M.length) : 0, te = u.map(($) => $.date).sort(), fe = te.length > 0 ? te[0] : "", oe = te.length > 0 ? te[te.length - 1] : "";
          t({
            totalTrades: d,
            winningTrades: f,
            losingTrades: l,
            winRate: m,
            profitFactor: k,
            averageWin: I,
            averageLoss: S,
            largestWin: C,
            largestLoss: R,
            totalPnl: x,
            maxDrawdown: L,
            maxDrawdownPercent: H,
            sharpeRatio: 0,
            // Would need daily returns to calculate
            sortinoRatio: 0,
            // Would need daily returns to calculate
            calmarRatio: 0,
            // Would need daily returns to calculate
            averageRMultiple: E,
            expectancy: _,
            sqn: re,
            period: "all",
            startDate: fe,
            endDate: oe
          });
        }, i.onerror = (u) => {
          console.error("Error getting performance metrics:", u), n(new Error("Failed to get performance metrics"));
        };
      });
    } catch (r) {
      throw console.error("Error in getPerformanceMetrics:", r), new Error("Failed to get performance metrics");
    }
  }
  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), u = a.objectStore(this.stores.trades).getAll();
        u.onsuccess = async () => {
          let d = u.result;
          r.dateFrom && (d = d.filter((l) => l.date >= r.dateFrom)), r.dateTo && (d = d.filter((l) => l.date <= r.dateTo)), r.model_type && (d = d.filter((l) => l[G.MODEL_TYPE] === r.model_type)), r.session && (d = d.filter((l) => l[G.SESSION] === r.session)), r.direction && (d = d.filter((l) => l[G.DIRECTION] === r.direction)), r.win_loss && (d = d.filter((l) => l[G.WIN_LOSS] === r.win_loss)), r.market && (d = d.filter((l) => l[G.MARKET] === r.market)), r.min_r_multiple !== void 0 && (d = d.filter((l) => l[G.R_MULTIPLE] !== void 0 && l[G.R_MULTIPLE] >= r.min_r_multiple)), r.max_r_multiple !== void 0 && (d = d.filter((l) => l[G.R_MULTIPLE] !== void 0 && l[G.R_MULTIPLE] <= r.max_r_multiple)), r.min_pattern_quality !== void 0 && (d = d.filter((l) => l[G.PATTERN_QUALITY_RATING] !== void 0 && l[G.PATTERN_QUALITY_RATING] >= r.min_pattern_quality)), r.max_pattern_quality !== void 0 && (d = d.filter((l) => l[G.PATTERN_QUALITY_RATING] !== void 0 && l[G.PATTERN_QUALITY_RATING] <= r.max_pattern_quality));
          const f = [];
          for (const l of d) {
            const m = {
              trade: l
            }, h = a.objectStore(this.stores.fvg_details).index("trade_id").get(l.id);
            await new Promise((N) => {
              h.onsuccess = () => {
                h.result && (m.fvg_details = h.result), N();
              }, h.onerror = () => N();
            });
            const S = a.objectStore(this.stores.setups).index("trade_id").get(l.id);
            await new Promise((N) => {
              S.onsuccess = () => {
                S.result && (m.setup = S.result), N();
              }, S.onerror = () => N();
            });
            const A = a.objectStore(this.stores.analysis).index("trade_id").get(l.id);
            await new Promise((N) => {
              A.onsuccess = () => {
                A.result && (m.analysis = A.result), N();
              }, A.onerror = () => N();
            }), f.push(m);
          }
          n(f);
        }, u.onerror = (d) => {
          console.error("Error filtering trades:", d), s(new Error("Failed to filter trades"));
        };
      });
    } catch (t) {
      throw console.error("Error in filterTrades:", t), new Error("Failed to filter trades");
    }
  }
  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades() {
    try {
      return await this.filterTrades({});
    } catch (r) {
      return console.error("Error in getAllTrades:", r), [];
    }
  }
  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const a = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        a.onerror = (S) => {
          console.error("Transaction error:", S), s(new Error("Failed to delete trade"));
        };
        const d = a.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));
        d.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const m = a.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));
        m.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const h = a.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));
        h.onsuccess = (S) => {
          const C = S.target.result;
          C && (C.delete(), C.continue());
        };
        const I = a.objectStore(this.stores.trades).delete(r);
        a.oncomplete = () => {
          n();
        }, I.onerror = (S) => {
          console.error("Error deleting trade:", S), s(new Error("Failed to delete trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in deleteTrade:", t), new Error("Failed to delete trade");
    }
  }
  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(r, t) {
    try {
      const n = await this.initDB();
      return new Promise((s, a) => {
        const i = n.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        i.onerror = (l) => {
          console.error("Transaction error:", l), a(new Error("Failed to update trade"));
        };
        const u = i.objectStore(this.stores.trades), d = {
          ...t.trade,
          id: r,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, f = u.put(d);
        f.onsuccess = () => {
          if (t.fvg_details) {
            const l = i.objectStore(this.stores.fvg_details), m = {
              ...t.fvg_details,
              trade_id: r
            };
            l.put(m);
          }
          if (t.setup) {
            const l = i.objectStore(this.stores.setups), m = {
              ...t.setup,
              trade_id: r
            };
            l.put(m);
          }
          if (t.analysis) {
            const l = i.objectStore(this.stores.analysis), m = {
              ...t.analysis,
              trade_id: r
            };
            l.put(m);
          }
        }, i.oncomplete = () => {
          s();
        }, f.onerror = (l) => {
          console.error("Error updating trade:", l), a(new Error("Failed to update trade"));
        };
      });
    } catch (n) {
      throw console.error("Error in updateTradeWithDetails:", n), new Error("Failed to update trade");
    }
  }
}
const Rt = new gc(), Rl = Rt, Ml = Rt;
export {
  xl as AVAILABLE_THEMES,
  Mc as AppErrorBoundary,
  Qe as Badge,
  le as Button,
  Vn as Card,
  Vc as DashboardSection,
  Yc as DashboardTemplate,
  Uc as DataCard,
  kc as DualTimeDisplay,
  Zr as EmptyState,
  $c as EnhancedFormField,
  ns as ErrorBoundary,
  Jc as F1Container,
  Zc as F1Form,
  el as F1FormField,
  Xc as F1Header,
  Pc as FeatureErrorBoundary,
  zc as FormField,
  qc as HierarchicalSessionSelector,
  je as Input,
  Lc as LoadingCell,
  Uo as LoadingPlaceholder,
  Rc as LoadingSpinner,
  D as MacroPeriodType,
  Fc as Modal,
  ho as OrderSide,
  xo as OrderStatus,
  mo as OrderType,
  Le as SETUP_ELEMENTS,
  Pe as Select,
  _c as SelectDropdown,
  Q as SessionType,
  ie as SessionUtils,
  Gc as SetupBuilder,
  Ac as SortableTable,
  vc as StatusIndicator,
  T as TRADE_COLUMN_IDS,
  Dc as TabPanel,
  Bc as Table,
  wc as Tag,
  Lt as ThemeContext,
  ml as ThemeProvider,
  bo as TimeInForce,
  Sc as TimePicker,
  Kc as TradeAnalysis,
  Ur as TradeConverters,
  fo as TradeDirection,
  Wc as TradeMetrics,
  go as TradeStatus,
  Hc as TradeTable,
  Di as TradeTableFilters,
  Li as TradeTableRow,
  Et as UnifiedErrorBoundary,
  yc as VALID_TRADING_MODELS,
  w as baseColors,
  rr as borderRadius,
  er as breakpoints,
  kl as captureError,
  Cc as convertLocalToNY,
  Qr as convertNYToLocal,
  wr as convertSessionToDualTime,
  yl as createSelector,
  bl as createStoreContext,
  se as darkModeColors,
  _t as darkTheme,
  Tl as debounce,
  yr as f1OfficialTheme,
  Er as f1Theme,
  Ze as fontFamilies,
  ue as fontSizes,
  Xe as fontWeights,
  wl as formatCurrency,
  Cl as formatDate,
  Sl as formatPercentage,
  it as formatTime,
  Ec as formatTimeForDesktop,
  mn as formatTimeForMobile,
  un as formatTimeInterval,
  hl as generateAllThemeCSS,
  pc as generateCSSVariables,
  jl as generateId,
  uc as generateSemanticVariables,
  wi as getCompactTradeTableColumns,
  br as getCurrentDualTime,
  Ic as getCurrentNYMinutes,
  jc as getCurrentNYTime,
  Si as getPerformanceTradeTableColumns,
  ul as getProfitLossColors,
  dl as getProfitLossSize,
  pl as getProfitLossVariant,
  Nc as getSessionStatus,
  We as getTimeUntilNYTime,
  vi as getTradeTableColumns,
  vr as getUserTimezone,
  Nl as initMonitoring,
  gn as isCurrentTimeInNYWindow,
  Qc as lightModeColors,
  fl as lightTheme,
  Je as lineHeights,
  Er as mercedesGreenTheme,
  Tc as minutesToTime,
  vl as persistState,
  cl as profitLossBaseStyles,
  tc as profitLossColors,
  ll as profitLossLoadingStyles,
  rc as profitLossSizes,
  _l as setUser,
  tr as shadows,
  Oc as sortFunctions,
  X as spacing,
  Ll as startTransaction,
  El as throttle,
  Ge as timeToMinutes,
  Ml as tradeStorage,
  Rl as tradeStorageService,
  or as transitions,
  Il as truncateText,
  rl as useAsyncData,
  al as useDataFormatting,
  il as useDataSection,
  tl as useDebounce,
  ol as useErrorHandler,
  ps as useFormField,
  Ja as useLoadingState,
  wt as useLocalStorage,
  nl as usePagination,
  sl as useProfitLossFormatting,
  si as useSessionSelection,
  vs as useSortableTable,
  gl as useTheme,
  ds as validationRules,
  nr as zIndex
};
