{"name": "adhd-trading-dashboard-lib", "version": "1.0.0", "description": "ADHD Trading Dashboard React Library", "private": true, "workspaces": ["packages/*"], "scripts": {"start": "yarn workspace @adhd-trading-dashboard/dashboard start", "dev": "node scripts/dev-tools.js dev --full", "dev:storybook": "node scripts/dev-tools.js dev --storybook", "build": "node scripts/dev-tools.js build", "build:clean": "node scripts/dev-tools.js build --clean", "build:dev": "node scripts/dev-tools.js build --dev", "build:watch": "node scripts/dev-tools.js build --watch", "build:validate": "node scripts/dev-tools.js build --validate", "build:shared": "node scripts/dev-tools.js build --target shared", "build:dashboard": "node scripts/dev-tools.js build --target dashboard", "type-check": "tsc --build", "type-check:watch": "tsc --build --watch", "typescript:optimize": "node scripts/dev-tools.js typescript --optimize", "typescript:validate": "node scripts/dev-tools.js typescript --validate", "deps:check": "node scripts/dev-tools.js deps --check", "deps:sync": "node scripts/dev-tools.js deps --sync", "deps:outdated": "node scripts/dev-tools.js deps --outdated", "deps:audit": "node scripts/dev-tools.js deps --audit", "deps:optimize": "node scripts/dev-tools.js deps --optimize", "deps:report": "node scripts/dev-tools.js deps --report", "test": "node scripts/dev-tools.js test --unit", "test:watch": "vitest", "test:component": "node scripts/dev-tools.js test --component", "test:e2e": "node scripts/dev-tools.js test --e2e", "test:coverage": "node scripts/dev-tools.js test --coverage", "test:performance": "node scripts/dev-tools.js test --performance", "test:all": "node scripts/dev-tools.js test --all", "lint": "eslint packages --ext .js,.jsx,.ts,.tsx", "storybook": "yarn workspace @adhd-trading-dashboard/shared storybook", "build-storybook": "yarn workspace @adhd-trading-dashboard/shared build-storybook", "analyze": "node scripts/dev-tools.js analyze --all", "analyze:data-flow": "node scripts/dev-tools.js analyze --data-flow", "analyze:components": "node scripts/dev-tools.js analyze --components", "analyze:state": "node scripts/dev-tools.js analyze --state", "analyze:performance": "node scripts/dev-tools.js analyze --performance", "analyze:generate-docs": "node scripts/dev-tools.js analyze --generate-docs", "analyze:bundle": "webpack --mode production --env analyze=true", "docs": "node scripts/dev-tools.js docs", "docs:check": "node scripts/dev-tools.js docs --check", "cleanup": "node scripts/dev-tools.js cleanup", "cleanup:legacy": "node scripts/dev-tools.js cleanup --legacy", "health": "node scripts/dev-tools.js health", "health:fix": "node scripts/dev-tools.js health --fix", "setup": "node scripts/dev-tools.js setup", "setup:quick": "node scripts/dev-workflow.js setup", "test:quick": "node scripts/dev-workflow.js test", "build:quick": "node scripts/dev-workflow.js build", "quality:check": "node scripts/dev-workflow.js quality", "clean": "rimraf packages/*/dist packages/*/node_modules node_modules/.cache", "clean:deep": "rimraf packages/*/dist packages/*/node_modules node_modules", "// Enhanced Development Tools": "Use 'yarn setup' to initialize, 'yarn dev' to start development", "// Legacy Scripts": "Keeping some legacy scripts for backward compatibility", "deploy": "node scripts/deploy-monorepo.js", "deploy:all": "node scripts/deploy-monorepo.js --deploy", "deploy:gh-pages": "yarn build && node scripts/deploy-gh-pages.js", "check-versions": "node scripts/manage-versions.js --check", "fix-versions": "node scripts/manage-versions.js", "manage-assets": "node scripts/manage-assets.js", "diagnostics": "node scripts/diagnostics/run-all.js"}, "dependencies": {"core-js": "3.35.0", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.6.2", "recharts": "2.10.3", "styled-components": "5.3.6"}, "devDependencies": {"@babel/cli": "7.23.9", "@babel/core": "7.23.6", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "7.23.3", "@babel/runtime": "7.23.6", "@playwright/test": "1.40.1", "@storybook/addon-a11y": "7.6.7", "@storybook/addon-docs": "7.6.7", "@storybook/addon-essentials": "7.6.7", "@storybook/addon-interactions": "7.6.7", "@storybook/addon-links": "7.6.7", "@storybook/react": "7.6.7", "@storybook/react-webpack5": "7.6.7", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "@types/node": "16.18.11", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "@types/react-router-dom": "5.3.3", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1", "@vitejs/plugin-react": "4.0.0", "acorn": "8.14.1", "babel-loader": "9.1.3", "babel-plugin-styled-components": "2.1.4", "chalk": "5.4.1", "commander": "14.0.0", "css-loader": "6.8.1", "css-minimizer-webpack-plugin": "5.0.1", "eslint": "8.56.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "figlet": "1.8.1", "glob": "11.0.2", "html-webpack-plugin": "5.6.3", "mini-css-extract-plugin": "2.9.2", "rimraf": "6.0.1", "schema-utils": "3.3.0", "storybook": "7.6.7", "style-loader": "3.3.3", "terser-webpack-plugin": "5.3.14", "typescript": "^5.8.0", "vite": "4.3.1", "vite-plugin-dts": "4.5.4", "vitest": "0.30.1", "webpack": "5.89.0", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"typescript": "^5.8.0", "@types/react": "18.0.28", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.6", "@typescript-eslint/typescript-estree": "^8.0.0", "react-scripts": "5.0.1", "@typescript-eslint/parser": "^8.0.0"}, "keywords": ["react", "trading", "dashboard", "typescript"], "author": "", "license": "MIT", "type": "module"}